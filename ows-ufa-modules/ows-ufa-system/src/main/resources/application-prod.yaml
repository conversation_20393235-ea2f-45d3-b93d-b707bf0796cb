# spring配置
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: nacos:8848
      config:
        # 配置中心地址
        server-addr: nacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  redis:
    host: *************
    port: 9000
    password: Lmcc_58120427
    database: 11
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        connectTimeout: 30000
        socketTimeout: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        maxEvictableIdleTimeMillis: 900000
        keepAlive: true
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
          # 主库数据源
          master:
            driver-class-name: com.huawei.gaussdb.jdbc.Driver
            url: jdbc:gaussdb://**************:8000/old_college_chongqing?currentSchema=cq_college_an
            username: user2
            password: cqdx@2024
          # 从库数据源
          slave:
            enabled : true
            driver-class-name: com.huawei.gaussdb.jdbc.Driver
            url: jdbc:gaussdb://**************:8000/old_college_chongqing?currentSchema=eld_college_chongqing_three
            username: rolegs
            password: YLAX@lndx2025

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.ows.ufa.system
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml
pagehelper:
  helperDialect: postgresql

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:8080/NULL
  api-docs:
    # 是否开启接口文档
    enabled: false
  info:
    # 标题
    title: '系统模块接口文档'
    # 描述
    description: '系统模块接口描述'
yulaoai:
  url: https://cqlndx.12399.gov.cn:9003/glpt-api/stu/port/info
  publicKeyStr: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGQoBqSrFZM+5CAFmI08KNA2yjIg2Oa/tp4wSSRqlRpNWl3UfVEYZvhe8iAEedobOGyXqZ9KjZxnJgLxyX/wx7wQlVFhsFA2d8Um07KyZmCgpm+BagSKXOC3KWMqZBmwQM/kKAODkMxBsBUBLj0nKsraU1zbecXoR+tk4bbqbOpwIDAQAB
  privateKeyStr: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAILqAQputX1CTcf0CfQNVQVg0Hb5hGwK5sYeQ02/bnAZWwC4+5FBo4SbQ6lRnHwSMAO90jHxrH7n00yEcAnEhzBasWa2yHMCwPnEJreULLGvvVsW4/G7YlkJ17x1lgdI4y76lkmWi+0bfbrw+SB+y2Ex1/AHd1HGaxX0HA3RHWepAgMBAAECgYBasfk0nAw6EiRGwYffvxjG2BK0Shr2qvrECJVCGLxPBxHnxz/pmxrItLREaljKD+OZw/kKRs7lQIA/g4UHD1NEK/gS26PWm0U5K9ZCzxtqcHCguL7uD1SLV556UYsncsq+kcuVLuumP1M4n/q+SPBcryCktWXrG/9HkIhIdmtvAQJBALekveUymDV+zKdHQ6FldGD9vSmOZyrl1+V001ncFA4qII9OyTgQBrEKqmKBUkHs89LCPZQYA6eqFY1l8WTpZwkCQQC2frD5MmF5Zk33pU/SMsh7HGsW1x+6BV9oHaM7SCOhMadlnKK7dNcAJt4hmBtCkMzJhkYwCPkMa9pfDieSSoOhAkEAjeT+zS+QqBT3cHEB8pz/lUm5dXiQgnbhoGvqOk5wOJmYWuKXW3gWk4kYKdCejE3X/4sSJXGAsXYb/Qs9v6kQcQJAFBi1gmuy4jyK33eb56jh/PSvMk+0Vbbbv8prvE6AZfLi3US3gu8l8gVVtttaPSVW9+ZKemWyj1SdMpSLdQexoQJBAJLe8FImPLBmC+p/a1u4u1sll5q2UaTsx6KF4mmqZNNVpaVUI40iRaQK5hwuTJh255QujFj147+htI8Bh0JlAt8=
event:
    enable: true
    appCode: A50000000019370202312007001
    appSecret: f/UQ6yW+7f1nYxBorNf+mgVYpLvpkwkb6uQhQ1kXXlwfJAfVsP+M2w==
    eventType: DJ131207
    authKey: xxx
    authSecret: xxx
    username: xxx
    reportUrl: https://23.210.52.92:30032/api/push/eventReport/v1
    queryUrl: http://23.210.52.92:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/orderquery/module/data/accept/api/exter/queryCompleteInfo
    tokenUrl: http://23.210.52.92:8799/dcqc/dcqc-gateway/dcqc-system/appKeySecret/apply/keyTokenByKey
    feedbackUrl: https://23.210.52.92:30032/api/push/dealLogPush/v1
ufa:
  url: https://cqlndx.12399.gov.cn:8094/

app:
  profile: /data/cdn/
  file-base: https://cqlndx.12399.gov.cn:8094

ykz:
  verifyToken: http://23.210.52.90/unionauth/token/checkToken/
  keyTokenByKeyUrl: http://23.210.52.92:8799/dcqc/dcqc-gateway/dcqc-system/appKeySecret/apply/keyTokenByKey
  accessTokenUrl: http://23.210.52.92:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/access-token
  accountIdUrl: http://23.210.52.92:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/account-id
  #愉快政sm4解密密钥
  appKey: eef9c2406c7e3da9c81939996657cf01
  appSecret: ecdbc6c0db34e51ca12a15510d80631b1b83b76c1560b11a55208025bb0fa763987c3f3e72e03d7b7b9d3f2722595055
  username: 390

ufa-mobile:
  url: http://cqlndx.12399.gov.cn
  #获取token
  speedLoginMobileByAuthCode: /ykzapp-api/speedy/login/speedLoginMobileByAuthCode
  #根据token获取用户权限等信息
  getMobileLoginInfo: /ykzapp-api/speedy/login/getMobileLoginInfo
  #查看用户信息接口
  userProfile: /ykzapp-api/system/user/profile
  #用户修改邮箱和昵称
  editMobileUser: /ykzapp-api/speedy/login/editMobileUser

#ufa-mobile:
#  # 测试
#  url: http://cqlndx.12399.gov.cn
#  #获取token
#  speedLoginMobileByAuthCode: /ykz-admin/speedy/login/speedLoginMobileByAuthCode
#  #根据token获取用户权限等信息
#  getMobileLoginInfo: /ykz-admin/speedy/login/getMobileLoginInfo
#  #查看用户信息接口
#  userProfile: /ykz-admin/system/user/profile
#  #用户修改邮箱和昵称
#  editMobileUser: /ykz-admin/speedy/login/editMobileUser