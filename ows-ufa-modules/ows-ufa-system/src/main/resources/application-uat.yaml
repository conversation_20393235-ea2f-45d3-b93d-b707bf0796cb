# spring配置
spring:
  redis:
    host: **************
    port: 9000
    password: Jnsk_81908834
    database: 6
  datasource:
    druid:
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: 123456
    dynamic:
      druid:
        initial-size: 5
        min-idle: 5
        maxActive: 20
        maxWait: 60000
        connectTimeout: 30000
        socketTimeout: 60000
        timeBetweenEvictionRunsMillis: 60000
        minEvictableIdleTimeMillis: 300000
        keepAlive: true
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        filters: stat,slf4j
        connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
          # 主库数据源
          master:
            driver-class-name: com.huawei.gaussdb.jdbc.Driver
            url: jdbc:gaussdb://**************:9005/ufa_test?currentSchema=ufa_test
            username: root
            password: H20Nvvna@Huawei
          # 从库数据源
          # slave:
            # username: 
            # password: 
            # url: 
            # driver-class-name: 

# mybatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.ows.ufa.system
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath:mapper/**/*.xml
pagehelper:
  helperDialect: postgresql

# springdoc配置
springdoc:
  gatewayUrl: http://localhost:8280/NULL
  api-docs:
    # 是否开启接口文档
    enabled: false
  info:
    # 标题
    title: '系统模块接口文档'
    # 描述
    description: '系统模块接口描述'
yulaoai:
  url: https://cqlncs.12399.gov.cn:9003/glpt-api/stu/port/info
event:
    enable: false
    appCode: A50000000019370202312007001
    appSecret: f/UQ6yW+7f1nYxBorNf+mgVYpLvpkwkb6uQhQ1kXXlwfJAfVsP+M2w==
    eventType: DJ131207
    authKey: xxx
    authSecret: xxx
    username: xxx
    reportUrl: https://************:30032/api/push/eventReport/v1
    queryUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/orderquery/module/data/accept/api/exter/queryCompleteInfo
    tokenUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-system/appKeySecret/apply/keyTokenByKey
    feedbackUrl: https://************:30032/api/push/dealLogPush/v1
ufa:
  url: https://cqlncs.12399.gov.cn:8093/

app:
  profile: /data/cdn/
  file-base: https://cqlncs.12399.gov.cn:8093

ykz:
  verifyToken: http://************/unionauth/token/checkToken/
  keyTokenByKeyUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-system/appKeySecret/apply/keyTokenByKey
  accessTokenUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/access-token
  accountIdUrl: http://************:8799/dcqc/dcqc-gateway/dcqc-apiserver/gateway/api/third-api/account-id
  #愉快政sm4解密密钥
  appKey: eef9c2406c7e3da9c81939996657cf01
  appSecret: ecdbc6c0db34e51ca12a15510d80631b1b83b76c1560b11a55208025bb0fa763987c3f3e72e03d7b7b9d3f2722595055
  username: 390

#ufa-mobile:
#  url: https://cqlndx.12399.gov.cn:9003
#  #获取token
#  speedLoginMobileByAuthCode: /glpt-api/speedy/login/speedLoginMobileByAuthCode
#  #根据token获取用户权限等信息
#  getMobileLoginInfo: /glpt-api/speedy/login/getMobileLoginInfo
#  #查看用户信息接口
#  userProfile: /glpt-api/system/user/profile
#  #用户修改邮箱和昵称
#  editMobileUser: /glpt-api/speedy/login/editMobileUser
ufa-mobile:
  # 测试
  url: http://cqlndx.12399.gov.cn
  #获取token
  speedLoginMobileByAuthCode: /ykz-admin/speedy/login/speedLoginMobileByAuthCode
  #根据token获取用户权限等信息
  getMobileLoginInfo: /ykz-admin/speedy/login/getMobileLoginInfo
  #查看用户信息接口
  userProfile: /ykz-admin/system/user/profile
  #用户修改邮箱和昵称
  editMobileUser: /ykz-admin/speedy/login/editMobileUser