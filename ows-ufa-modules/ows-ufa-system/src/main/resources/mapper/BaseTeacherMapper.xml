<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.BaseTeacherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.BaseTeacher">
        <id column="id" property="id" />
        <result column="tch_name" property="tchName" />
        <result column="tch_card" property="tchCard" />
        <result column="tch_birthday" property="tchBirthday" />
        <result column="tch_sex" property="tchSex" />
        <result column="tch_phone" property="tchPhone" />
        <result column="tch_addr" property="tchAddr" />
        <result column="tch_graduated_school" property="tchGraduatedSchool" />
        <result column="tch_edu" property="tchEdu" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tch_name, tch_card, tch_birthday, tch_sex, tch_phone, tch_addr, tch_graduated_school, tch_edu, create_by, create_time, update_by, update_time, status
    </sql>
    <select id="querySchoolTeachers" resultType="com.ows.ufa.system.vo.BaseTeacherVO" parameterType="com.ows.ufa.system.request.BaseTeacherRequest">
        select bst.tch_work_time,bs.sch_name schoolName,bs.sch_head,bs.tm_sch_head_phone schHeadPhone,bs.jm_sch_head_phone,bsd.dept_name,bt.tch_name,bt.tch_sex ,bt.tch_edu ,bt.tch_graduated_school ,bt.tch_addr
        from base_school_teacher bst left join base_school bs on bst.school_id =bs.id left join base_school_dept bsd on bst.dept_id =bsd.id left join base_teacher bt on bst.tch_id =bt.id
        where 1=1 and (bst.dept_id is not null and bst.dept_id &lt;&gt; ' ') and bt.tch_card is not null and bt.tch_phone  is not null
        <if test="req.schoolName != null and ''!=req.schoolName">
            and bs.sch_name like CONCAT('%',#{req.schoolName},'%')
        </if>
        <if test="req.schoolId != null and ''!=req.schoolId">
            and bst.school_id =#{req.schoolId}
        </if>
        <if test="req.deptName != null and ''!=req.deptName">
            and bsd.dept_name like CONCAT('%',#{req.deptName},'%')
        </if>
        <if test="req.tchAddr != null and ''!=req.tchAddr">
            and bt.tch_addr like CONCAT('%',#{req.tchAddr},'%')
        </if>
        order by bs.publish_time desc ,bs.create_time desc,bsd.create_time desc,bt.create_time desc
    </select>
    <select id="countSchool" resultType="java.lang.Long">
        select count(1) from (select count(1) from base_school_teacher bst where bst.dept_id is not null and bst.dept_id &lt;&gt; ' ' group by school_id) t
    </select>
    <select id="countTeacher" resultType="com.ows.ufa.system.vo.count.BaseTeacherCountVO">
        select count(1)teacherCount,count(case when tch_sex='1' then 1 end )maleRatio,count(case when tch_sex='0' then 1 end )femaleRatio from base_teacher bt where bt.tch_card is not null and bt.tch_phone  is not null
    </select>
    <select id="countDept" resultType="java.lang.Long">
        select count(1) from (select count(1) from base_school_teacher bst  where bst.dept_id is not null and bst.dept_id &lt;&gt; ' ' group by dept_id) t
    </select>
    <select id="countTeacherEdu" resultType="com.ows.ufa.system.vo.count.BaseTeacherEduCountVO">
        select tch_edu,count(1) eduCount from base_teacher bt where bt.tch_card is not null and bt.tch_phone  is not null group by tch_edu
    </select>

</mapper>