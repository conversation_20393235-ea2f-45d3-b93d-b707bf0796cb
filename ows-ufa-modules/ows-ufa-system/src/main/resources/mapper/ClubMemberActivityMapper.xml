<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ClubMemberActivityMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.ClubMemberActivity">
        <id column="id" property="id" />
        <result column="club_info_id" property="clubInfoId" />
        <result column="club_activity_id" property="clubActivityId" />
        <result column="name" property="name" />
        <result column="sex" property="sex" />
        <result column="phone_number" property="phoneNumber" />
        <result column="application_date" property="applicationDate" />
        <result column="review_date" property="reviewDate" />
        <result column="review_at" property="reviewAt" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="dept_id" property="deptId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, club_info_id, club_activity_id, name, sex, phone_number, application_date, review_date, review_at, status, del_flag, create_at, update_at, create_time, update_time, dept_id
    </sql>
    <select id="queryClubMemberActivitys" resultType="com.ows.ufa.system.vo.ClubMemberActivityVO">
        SELECT cm.*,tci.club_name clubInfoName,tca.activity_name clubActivityName,tcm."phone_number" phoneNumber,tcm."phone_encrypt" phoneEncrypt,tcm."name" memberName,tcm.sex sex,
        tca.start_time,tca.end_time, tca.attachment_url,
        (CASE WHEN tca.end_time &lt; now() THEN 1 ELSE 0 END) AS progressStatus
        FROM t_club_member_activity cm left join t_club_info tci on cm.club_info_id =tci.id
        left join t_club_activities tca on cm.club_activity_id =tca.id
        <!-- 此处获取最新一条成员的信息 -->
        left join t_club_member tcm on
        tcm.id = (select tc.id from t_club_member tc where tc.create_at =cm.club_member_id order by tc.create_time desc limit 1)
        WHERE cm.del_flag = 1
        <if test="req.memberName != null and req.memberName != ''">
            AND tcm."name" LIKE CONCAT('%', #{req.memberName}, '%')
        </if>
        <if test="req.status != null ">
            AND cm.status = #{req.status}
        </if>
        <if test="req.clubInfoName != null and req.clubInfoName != ''">
            AND tci.club_name LIKE CONCAT('%', #{req.clubInfoName}, '%')
        </if>
        <if test="req.clubActivityName != null and req.clubActivityName != ''">
            AND tca.activity_name LIKE CONCAT('%', #{req.clubActivityName}, '%')
        </if>
        <if test="req.applicationBeginTime != null and req.applicationEndTime != null">
            AND cm.application_date BETWEEN  #{req.applicationBeginTime} AND #{req.applicationEndTime}
        </if>
        <if test="req.ancestors != null">
            AND cm.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="req.createAt != null and req.createAt != ''">
            AND cm.create_at =#{req.createAt}
        </if>
        ORDER BY cm.review_date DESC,cm.create_time desc
    </select>
    <select id="queryClubMemberActivity" resultType="com.ows.ufa.system.vo.ClubMemberActivityVO">
        SELECT cm.*,tci.club_name clubInfoName,tca.activity_name clubActivityName,tcm."name" clubInfoName,tcm."name" memberName,tcm.sex sex,
        tca.start_time,tca.end_time, tca.attachment_url,
        (CASE WHEN tca.end_time &lt; now() THEN 1 ELSE 0 END) AS progressStatus
        FROM t_club_member_activity cm left join t_club_info tci on cm.club_info_id =tci.id
        left join t_club_activities tca on cm.club_activity_id =tca.id
        left join t_club_member tcm on tcm.id =cm.club_member_id and tci.id=tcm.club_info_id
        WHERE cm.id = #{id}
    </select>

</mapper>