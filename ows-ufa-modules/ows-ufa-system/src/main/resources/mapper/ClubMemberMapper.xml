<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ClubMemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.ClubMember">
        <id column="id" property="id" />
        <result column="club_info_id" property="clubInfoId" />
        <result column="name" property="name" />
        <result column="sex" property="sex" />
        <result column="phone_number" property="phoneNumber" />
        <result column="application_date" property="applicationDate" />
        <result column="review_date" property="reviewDate" />
        <result column="review_at" property="reviewAt" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="dept_id" property="deptId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, club_info_id, name, sex, phone_number, application_date, review_date, review_at, status, del_flag, create_at, update_at, create_time, update_time, dept_id
    </sql>
    <select id="queryClubMembers" resultType="com.ows.ufa.system.vo.ClubMemberVO">
        SELECT cm.*,tci.club_name clubInfoName,tci.club_photo clubPhoto,tci.club_descp clubDescp
        FROM t_club_member cm left join t_club_info tci on cm.club_info_id =tci.id
        WHERE cm.del_flag = 1
        <if test="req.name != null and req.name != ''">
            AND cm.name LIKE CONCAT('%', #{req.name}, '%')
        </if>
        <if test="req.phoneNumber != null and req.phoneNumber != ''">
            AND cm.phone_encrypt = #{req.phoneNumber}
        </if>
        <if test="req.status != null">
            AND cm.status = #{req.status}
        </if>
        <if test="req.clubInfoName != null and req.clubInfoName != ''">
            AND tci.club_name LIKE CONCAT('%', #{req.clubInfoName}, '%')
        </if>
        <if test="req.beginTime != null and req.endTime != null">
            AND cm.review_date BETWEEN  #{req.beginTime} AND #{req.endTime}
        </if>
        <if test="req.applicationBeginTime != null and req.applicationEndTime != null">
            AND cm.application_date BETWEEN  #{req.applicationBeginTime} AND #{req.applicationEndTime}
        </if>
        <if test="req.ancestors != null">
            AND cm.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="req.createAt != null and req.createAt != ''">
            AND cm.create_at =#{req.createAt}
        </if>
        ORDER BY cm.review_date DESC,cm.create_time desc
    </select>
    <select id="findClubMember" resultType="com.ows.ufa.system.vo.ClubMemberVO">
        SELECT cm.*,tci.club_name clubInfoName,tci.club_photo clubPhoto,tci.club_descp clubDescp
        FROM t_club_member cm left join t_club_info tci on cm.club_info_id =tci.id
        WHERE cm.id = #{id}
    </select>

</mapper>