<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.AbilityLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.AbilityLabel">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="del_flag" property="delFlag" />
        <result column="dept_id" property="deptId" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, del_flag, dept_id, create_at, update_at, create_time, update_time
    </sql>
    <resultMap id="AbilityLabelVOMap" type="com.ows.ufa.system.vo.AbilityLabelVO">
        <id property="id" column="ability_label_id"/>
        <result property="title" column="title"/>
        <!-- 映射一对多关系 -->
        <collection property="abilityLabelInfos" ofType="com.ows.ufa.system.entity.AbilityLabelInfo">
            <id property="id" column="ability_label_info_id"/>
            <result property="abilityId" column="ability_id"/>
            <result property="name" column="name"/>
        </collection>
    </resultMap>

    <select id="queryAbilityLabels" resultMap="AbilityLabelVOMap">
        SELECT
        al.id AS ability_label_id,
        al.title,
        ali.id AS ability_label_info_id,
        ali.ability_id,
        ali.name
        FROM
        t_ability_label al
        LEFT JOIN
        t_ability_label_info ali ON al.id = ali.ability_id
        where al.del_flag = 1
        <if test="req.title != null and req.title != ''">
            AND al.title LIKE CONCAT('%', #{req.title}, '%')
        </if>
        AND al.dept_id in
        <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        order by al.create_time desc
    </select>

</mapper>