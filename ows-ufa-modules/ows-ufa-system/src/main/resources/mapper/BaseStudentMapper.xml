<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.BaseStudentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.BaseStudent">
        <id column="id" property="id" />
        <result column="stu_name" property="stuName" />
        <result column="stu_sex" property="stuSex" />
        <result column="stu_card" property="stuCard" />
        <result column="stu_political" property="stuPolitical" />
        <result column="stu_phone" property="stuPhone" />
        <result column="stu_addr" property="stuAddr" />
        <result column="stu_health_state" property="stuHealthState" />
        <result column="stu_his_company" property="stuHisCompany" />
        <result column="stu_his_post" property="stuHisPost" />
        <result column="stu_his_job" property="stuHisJob" />
        <result column="stu_his_title" property="stuHisTitle" />
        <result column="stu_live_state" property="stuLiveState" />
        <result column="stu_edu_level" property="stuEduLevel" />
        <result column="stu_retire_state" property="stuRetireState" />
        <result column="is_agree" property="isAgree" />
        <result column="is_have_elder_college" property="isHaveElderCollege" />
        <result column="is_have_medical" property="isHaveMedical" />
        <result column="is_buy_insure" property="isBuyInsure" />
        <result column="is_red" property="isRed" />
        <result column="medical_desc" property="medicalDesc" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="stu_card_type" property="stuCardType" />
        <result column="stu_birth" property="stuBirth" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, stu_name, stu_sex, stu_card, stu_political, stu_phone, stu_addr, stu_health_state, stu_his_company, stu_his_post, stu_his_job, stu_his_title, stu_live_state, stu_edu_level, stu_retire_state, is_agree, is_have_elder_college, is_have_medical, is_buy_insure, is_red, medical_desc, create_by, create_time, update_by, update_time, status, stu_card_type, stu_birth
    </sql>

</mapper>