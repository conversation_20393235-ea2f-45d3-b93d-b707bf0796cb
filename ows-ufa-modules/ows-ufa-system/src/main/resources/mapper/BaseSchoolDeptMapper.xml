<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.BaseSchoolDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.BaseSchoolDept">
        <id column="id" property="id" />
        <result column="dept_code" property="deptCode" />
        <result column="dept_name" property="deptName" />
        <result column="dept_head_id" property="deptHeadId" />
        <result column="dept_sort" property="deptSort" />
        <result column="dept_desc" property="deptDesc" />
        <result column="course_fees" property="courseFees" />
        <result column="school_id" property="schoolId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="campus_id" property="campusId" />
        <result column="is_enable" property="isEnable" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, dept_code, dept_name, dept_head_id, dept_sort, dept_desc, course_fees, school_id, create_by, create_time, update_by, update_time, status, campus_id, is_enable
    </sql>
    <select id="querySchools" resultType="com.ows.ufa.system.entity.BaseSchool">
        SELECT id, sch_name
        FROM base_school bs where bs.status =1 and bs.is_publish =1 and bs.is_enable =1 and bs.is_submit =1;
    </select>
    <select id="queryAncestors" resultType="java.lang.String">
        SELECT
        dept_id,
        parent_id,
        dept_name
        FROM sys_dept sd
        WHERE sd.dept_id = #{deptId}
        OR #{deptId} = ANY(string_to_array(sd.ancestors, ',')::BIGINT[]);
    </select>

</mapper>