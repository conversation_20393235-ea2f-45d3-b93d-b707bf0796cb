<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ClubInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.ClubInfo">
        <id column="id" property="id" />
        <result column="club_photo" property="clubPhoto" />
        <result column="club_name" property="clubName" />
        <result column="club_descp" property="clubDescp" />
        <result column="leader" property="leader" />
        <result column="phone_number" property="phoneNumber" />
        <result column="is_exam_required" property="isExamRequired" />
        <result column="recruitment_conditions" property="recruitmentConditions" />
        <result column="del_flag" property="delFlag" />
        <result column="application_date" property="applicationDate" />
        <result column="review_date" property="reviewDate" />
        <result column="review_at" property="reviewAt" />
        <result column="status" property="status" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="dept_id" property="deptId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, club_photo, club_name, club_descp, leader, phone_number, is_exam_required, recruitment_conditions, del_flag, application_date, review_date, review_at, status, create_at, update_at, create_time, update_time, dept_id
    </sql>
    <select id="queryClubInfos" resultType="com.ows.ufa.system.vo.ClubInfoVO">
        SELECT ci.*,
        (select count(1) from t_club_member tcm where tcm.club_info_id=ci.id and tcm.del_flag = 1 and tcm.status=1 AND tcm.dept_id in
        <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>) memberCount,
        (select count(1) from t_club_activities tca where tca.club_info_id=ci.id and tca.del_flag = 1 and tca.status=1 AND tca.dept_id in
        <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>) activityCount
        FROM t_club_info ci
        WHERE ci.del_flag = 1
        <if test="req.clubName != null and req.clubName != ''">
            AND club_name LIKE CONCAT('%', #{req.clubName}, '%')
        </if>
        <if test="req.clubDescp != null and req.clubDescp != ''">
            AND club_descp LIKE CONCAT('%', #{req.clubDescp}, '%')
        </if>
        <if test="req.beginTime != null and req.endTime != null">
            AND review_date BETWEEN  #{req.beginTime} AND #{req.endTime} AND status =1
        </if>
        <if test="req.applicationBeginTime != null and req.applicationEndTime != null">
            AND application_date BETWEEN  #{req.applicationBeginTime} AND #{req.applicationEndTime}
        </if>
        <if test="req.status != null">
            AND status = #{req.status}
        </if>
        <if test="req.ancestors != null">
            AND ci.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="req.searchText != null and req.searchText != ''">
            AND (club_name LIKE CONCAT('%', #{req.searchText}, '%') or club_descp LIKE CONCAT('%', #{req.searchText}, '%'))
        </if>
        <if test="req.recruitmentStatus != null and req.recruitmentStatus == 0">
            AND status = 1 AND (recruitment_start_date is null or recruitment_start_date &gt; NOW() )
        </if>
        <if test="req.recruitmentStatus != null and req.recruitmentStatus == 1">
            AND status = 1 AND recruitment_start_date &lt; NOW() AND recruitment_end_date &gt; NOW()
        </if>
        ORDER BY review_date DESC,create_time desc
    </select>
    <select id="findClubInfo" resultType="com.ows.ufa.system.vo.ClubInfoVO">
        SELECT ci.*,
        (select count(1) from t_club_member tcm where tcm.club_info_id=ci.id and tcm.del_flag = 1 and tcm.status=1) memberCount,
        (select count(1) from t_club_activities tca where tca.club_info_id=ci.id and tca.del_flag = 1 and tca.status=1) activityCount
        FROM t_club_info ci
        WHERE ci.id = #{id}
    </select>
    <select id="h5queryClubInfos" resultType="com.ows.ufa.system.vo.ClubInfoVO">
        SELECT ci.*,
        (select count(1) from t_club_member tcm where tcm.club_info_id=ci.id and tcm.del_flag = 1 and tcm.status=1 AND tcm.dept_id in
        <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>) memberCount,
        (select count(1) from t_club_activities tca where tca.club_info_id=ci.id and tca.del_flag = 1 and tca.status=1 AND tca.dept_id in
        <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>) activityCount
        FROM t_club_info ci
        WHERE ci.del_flag = 1
        <if test="req.clubName != null and req.clubName != ''">
            AND club_name LIKE CONCAT('%', #{req.clubName}, '%')
        </if>
        <if test="req.clubDescp != null and req.clubDescp != ''">
            AND club_descp LIKE CONCAT('%', #{req.clubDescp}, '%')
        </if>
        <if test="req.beginTime != null and req.endTime != null">
            AND review_date BETWEEN  #{req.beginTime} AND #{req.endTime} AND status =1
        </if>
        <if test="req.applicationBeginTime != null and req.applicationEndTime != null">
            AND application_date BETWEEN  #{req.applicationBeginTime} AND #{req.applicationEndTime}
        </if>
        <if test="req.status != null">
            AND status = #{req.status}
        </if>
        <if test="req.ancestors != null">
            AND ci.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="req.searchText != null and req.searchText != ''">
            AND (club_name LIKE CONCAT('%', #{req.searchText}, '%') or club_descp LIKE CONCAT('%', #{req.searchText}, '%'))
        </if>
        <if test="req.recruitmentStatus != null and req.recruitmentStatus == 0">
            AND status = 1 AND (recruitment_start_date is null or recruitment_start_date &gt; NOW() )
        </if>
        <if test="req.recruitmentStatus != null and req.recruitmentStatus == 1">
            AND status = 1 AND recruitment_start_date &lt; NOW() AND recruitment_end_date &gt; NOW()
        </if>
        ORDER BY
        CASE
        WHEN recruitment_start_date &lt;= NOW() AND recruitment_end_date >= NOW() THEN 1

        WHEN recruitment_start_date > NOW() THEN 2

        WHEN recruitment_start_date IS NULL THEN 3

        WHEN recruitment_end_date &lt; NOW() THEN 4

        ELSE 5
        END ,update_time desc
    </select>

</mapper>