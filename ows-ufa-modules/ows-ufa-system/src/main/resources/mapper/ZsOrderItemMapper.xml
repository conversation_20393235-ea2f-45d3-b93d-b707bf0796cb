<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ZsOrderItemMapper">

    <select id="selectOrderAmountByPayTime" resultType="java.math.BigDecimal">
        SELECT
            sum( order_amount )
        FROM
            zs_order_item
        WHERE
            plan_id = #{planId}
          AND pay_status = 1
          AND is_back = 0
          AND pay_time BETWEEN #{startPayTime}
            AND #{endPayTime}
    </select>
</mapper>