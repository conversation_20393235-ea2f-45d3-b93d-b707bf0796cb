<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.DeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.Dept">
        <id column="dept_id" property="deptId" />
        <result column="parent_id" property="parentId" />
        <result column="ancestors" property="ancestors" />
        <result column="dept_name" property="deptName" />
        <result column="order_num" property="orderNum" />
        <result column="leader" property="leader" />
        <result column="phone" property="phone" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="dept_address" property="deptAddress" />
        <result column="dept_fax" property="deptFax" />
        <result column="dept_postcode" property="deptPostcode" />
        <result column="dept_email" property="deptEmail" />
        <result column="is_school" property="isSchool" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        parent_id, ancestors, dept_name, order_num, leader, phone, create_by, create_time, update_by, update_time, status, dept_address, dept_fax, dept_postcode, dept_email, is_school, dept_id
    </sql>

</mapper>