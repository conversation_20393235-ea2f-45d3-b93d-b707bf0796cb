<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.BackupTeacherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.BackupTeacher">
        <id column="id" property="id" />
        <result column="tch_card" property="tchCard" />
        <result column="tch_name" property="tchName" />
        <result column="tch_edu" property="tchEdu" />
        <result column="tch_phone" property="tchPhone" />
        <result column="tch_addr" property="tchAddr" />
        <result column="tch_graduated_school" property="tchGraduatedSchool" />
        <result column="head_path" property="headPath" />
        <result column="tch_desc" property="tchDesc" />
        <result column="tch_no" property="tchNo" />
        <result column="tch_employ_date" property="tchEmployDate" />
        <result column="tch_work_time" property="tchWorkTime" />
        <result column="dept_name" property="deptName" />
        <result column="tch_work_state" property="tchWorkState" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tch_card, tch_name, tch_edu, tch_phone, tch_addr, tch_graduated_school, head_path, tch_desc, tch_no, tch_employ_date, tch_work_time, dept_name, tch_work_state, created_time, updated_time, created_at, updated_at
    </sql>

</mapper>