<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ZsPlanClassMapper">

    <select id="selectSignUpNumber" resultType="com.ows.ufa.system.entity.ZsPlanClass">
        SELECT
            dept_name,
            ( COALESCE ( sum( back_num ), 0 )+ COALESCE ( sum( renew_pay_show_num ), 0 )+ COALESCE ( sum( sign_num ), 0 ) ) signNum
        FROM
            zs_plan_class
        WHERE
            plan_id = #{planId}
        GROUP BY
            dept_name
        ORDER BY
            signNum DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>
    <select id="selectMajorNumber" resultType="java.lang.Long">
        SELECT
            count( DISTINCT major_id )
        FROM
            zs_plan_class
        WHERE
            plan_id = #{planId}
          AND school_id = #{schoolId}
          <if test="deptId != null and deptId != ''">
              AND dept_id = #{deptId}
          </if>
    </select>
</mapper>