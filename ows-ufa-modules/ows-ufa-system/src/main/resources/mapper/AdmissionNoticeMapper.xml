<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.AdmissionNoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.AdmissionNotice">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="order_no" property="orderNo" />
        <result column="pay_time" property="payTime" />
        <result column="user_name" property="userName" />
        <result column="log_url" property="logUrl" />
        <result column="school_name" property="schoolName" />
        <result column="dept_name" property="deptName" />
        <result column="major_name" property="majorName" />
        <result column="course_name" property="courseName" />
        <result column="refund_time" property="refundTime" />
        <result column="status" property="status" />
        <result column="read_status" property="readStatus" />
        <result column="notice_url" property="noticeUrl" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="course_start_time" property="courseStartTime" />
        <result column="school_addr" property="schoolAddr" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, order_no, pay_time, user_name, log_url, school_name, dept_name, major_name, course_name, refund_time, status, read_status, notice_url, create_time, update_time, course_start_time, school_addr
    </sql>

</mapper>