<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.QuestionnaireTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.QuestionnaireTemplate">
        <id column="id" property="id" />
        <result column="title" property="title" />
        <result column="cover_image" property="coverImage" />
        <result column="initiator" property="initiator" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="fill_permission" property="fillPermission" />
        <result column="participation_limit" property="participationLimit" />
        <result column="allow_modify" property="allowModify" />
        <result column="collection_limit" property="collectionLimit" />
        <result column="status" property="status" />
        <result column="content_json" property="contentJson" />
        <result column="del_flag" property="delFlag" />
        <result column="dept_id" property="deptId" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="collection" property="collection" />
        <result column="title_desc" property="titleDesc" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, title, cover_image, initiator, start_time, end_time, fill_permission, participation_limit, allow_modify, collection_limit, status, content_json, del_flag, dept_id, create_at, update_at, create_time, update_time, collection, title_desc
    </sql>

</mapper>