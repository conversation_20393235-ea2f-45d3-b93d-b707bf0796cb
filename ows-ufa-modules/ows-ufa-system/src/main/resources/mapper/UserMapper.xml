<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.UserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.User">
        <id column="user_id" property="userId" />
        <result column="openid" property="openid" />
        <result column="top_org_id" property="topOrgId" />
        <result column="detail_id" property="detailId" />
        <result column="user_name" property="userName" />
        <result column="nick_name" property="nickName" />
        <result column="user_card" property="userCard" />
        <result column="user_type" property="userType" />
        <result column="email" property="email" />
        <result column="phonenumber" property="phonenumber" />
        <result column="sex" property="sex" />
        <result column="avatar" property="avatar" />
        <result column="password" property="password" />
        <result column="login_ip" property="loginIp" />
        <result column="login_date" property="loginDate" />
        <result column="remark" property="remark" />
        <result column="dept_names" property="deptNames" />
        <result column="post_names" property="postNames" />
        <result column="role_names" property="roleNames" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="status" property="status" />
        <result column="del_flag" property="delFlag" />
        <result column="account_id" property="accountId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        user_id, openid, top_org_id, detail_id, user_name, nick_name, user_card, user_type, email, phonenumber, sex, avatar, password, login_ip, login_date, remark, dept_names, post_names, role_names, create_by, create_time, update_by, update_time, status, del_flag, account_id
    </sql>
    <select id="findUser" resultType="com.ows.ufa.system.vo.UserVO">
        select su.*,sud.dept_id from sys_user su left join sys_user_dept sud on su.user_id =sud.user_id where su.user_id =#{id}
    </select>

</mapper>