<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ZsPlanStudentMapper">


    <select id="getZsPlanStudentTotal" resultType="java.lang.Long">
        SELECT
            count(1)
        FROM
            zs_plan_student zps
                LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
        WHERE
            ( zps.is_pay = '1' OR zps.change_type = '2' )
          AND zps.is_flag = '0'
          AND zpc.school_id = #{schoolId}
          AND zpc.plan_id = #{planId}
    </select>
    <select id="getZsPlanStudentNumber" resultType="java.lang.Long">
        SELECT
            count( zps.id )
        FROM
            zs_plan_student zps
                LEFT JOIN zs_plan_class zpc ON zps.class_id = zpc.id
        WHERE
          zpc.plan_id = #{planId}
          AND zpc.school_id = #{schoolId}
          <if test="deptId != null and deptId != ''">
              AND zpc.dept_id = #{deptId}
          </if>
    </select>
    <select id="selectStudentPolitical" resultType="com.ows.ufa.system.domain.vo.StudentStatisticsVo">
        SELECT
            bs.stu_political AS data,
            count( 1 ) AS num
        FROM
            zs_plan_student zps
                LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
        WHERE
            ( zps.is_pay = '1' OR zps.change_type = '2' )
          AND zps.is_flag = '0'
          AND zpc.school_id = #{schoolId}
          AND zpc.plan_id = #{planId}
        GROUP BY
            bs.stu_political
    </select>
    <select id="selectStudentRetireState" resultType="com.ows.ufa.system.domain.vo.StudentStatisticsVo">
        SELECT
            bs.stu_retire_state AS data,
            count(1) AS num
        FROM
            zs_plan_student zps
                LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
        WHERE
            ( zps.is_pay = '1' OR zps.change_type = '2' )
          AND zps.is_flag = '0'
          AND zpc.school_id = #{schoolId}
          AND zpc.plan_id = #{planId}
        group by bs.stu_retire_state
    </select>
    <select id="selectStudentEduLevel" resultType="com.ows.ufa.system.domain.vo.StudentStatisticsVo">
        SELECT
            bs.stu_edu_level AS data,
            count(1) AS num
        FROM
            zs_plan_student zps
                LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
        WHERE
            ( zps.is_pay = '1' OR zps.change_type = '2' )
          AND zps.is_flag = '0'
          AND zpc.school_id = #{schoolId}
          AND zpc.plan_id = #{planId}
        group by bs.stu_edu_level
    </select>
    <select id="selectStudentLiveState" resultType="com.ows.ufa.system.domain.vo.StudentStatisticsVo">
        SELECT
            bs.stu_live_state AS data,
            count(1) AS num
        FROM
            zs_plan_student zps
                LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
        WHERE
            ( zps.is_pay = '1' OR zps.change_type = '2' )
          AND zps.is_flag = '0'
          AND zpc.school_id = #{schoolId}
          AND zpc.plan_id = #{planId}
        group by bs.stu_live_state
    </select>
    <select id="selectStudentSex" resultType="com.ows.ufa.system.domain.vo.StudentStatisticsVo">
        SELECT
            bs.stu_sex AS data,
            count(1) AS num
        FROM
            zs_plan_student zps
                LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
        WHERE
            ( zps.is_pay = '1' OR zps.change_type = '2' )
          AND zps.is_flag = '0'
          AND zpc.school_id = #{schoolId}
          AND zpc.plan_id = #{planId}
        group by bs.stu_sex
    </select>
    <select id="selectStudentCard" resultType="com.ows.ufa.system.domain.vo.StudentStatisticsVo">
        SELECT
            bs.stu_card AS data
        FROM
            zs_plan_student zps
                LEFT JOIN base_student bs ON bs.ID = zps.stu_id
                LEFT JOIN zs_plan_class zpc ON zpc.ID = zps.class_id
                LEFT JOIN base_school_dept bsd ON bsd.ID = zpc.dept_id
        WHERE
            ( zps.is_pay = '1' OR zps.change_type = '2' )
          AND zps.is_flag = '0'
          AND zpc.school_id = #{schoolId}
          AND zpc.plan_id = #{planId}
    </select>
</mapper>