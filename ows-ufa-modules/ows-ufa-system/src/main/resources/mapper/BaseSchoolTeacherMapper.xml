<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.BaseSchoolTeacherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.BaseSchoolTeacher">
        <id column="id" property="id" />
        <result column="school_id" property="schoolId" />
        <result column="dept_id" property="deptId" />
        <result column="tch_id" property="tchId" />
        <result column="tch_no" property="tchNo" />
        <result column="tch_employ_date" property="tchEmployDate" />
        <result column="tch_work_time" property="tchWorkTime" />
        <result column="tch_work_state" property="tchWorkState" />
        <result column="head_path" property="headPath" />
        <result column="tch_desc" property="tchDesc" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, school_id, dept_id, tch_id, tch_no, tch_employ_date, tch_work_time, tch_work_state, head_path, tch_desc, create_by, create_time, update_by, update_time
    </sql>

</mapper>