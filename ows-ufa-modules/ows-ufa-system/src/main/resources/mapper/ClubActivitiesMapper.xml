<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.ClubActivitiesMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.ClubActivities">
        <id column="id" property="id"/>
        <result column="club_info_id" property="clubInfoId"/>
        <result column="activity_name" property="activityName"/>
        <result column="activity_location" property="activityLocation"/>
        <result column="registration_time" property="registrationTime"/>
        <result column="registration_deadline" property="registrationDeadline"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="need_approval" property="needApproval"/>
        <result column="activity_descp" property="activityDescp"/>
        <result column="attachment_url" property="attachmentUrl"/>
        <result column="application_date" property="applicationDate"/>
        <result column="review_date" property="reviewDate"/>
        <result column="review_at" property="reviewAt"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_at" property="createAt"/>
        <result column="update_at" property="updateAt"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="dept_id" property="deptId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, club_info_id, activity_name, activity_location, registration_time, registration_deadline, start_time,
        end_time, need_approval, activity_descp, attachment_url, application_date, review_date, review_at, status,
        del_flag, create_at, update_at, create_time, update_time, dept_id, progress_status
    </sql>
    <select id="queryClubActivitiess" resultType="com.ows.ufa.system.vo.ClubActivitiesVO">
        SELECT
        cm.*,
        tci.club_name AS clubInfoName
        FROM
        t_club_activities cm
        LEFT JOIN
        t_club_info tci ON cm.club_info_id = tci.id
        WHERE
        cm.del_flag = 1
        <!-- 根据社团名称查询 -->
        <if test="req.clubInfoName != null and req.clubInfoName != ''">
            AND tci.club_name LIKE CONCAT('%', #{req.clubInfoName}, '%')
        </if>
        <!-- 根据活动名称查询 -->
        <if test="req.activityName != null and req.activityName != ''">
            AND cm.activity_name LIKE CONCAT('%', #{req.activityName}, '%')
        </if>
        <!-- 根据活动地点查询 -->
        <if test="req.activityLocation != null and req.activityLocation != ''">
            AND cm.activity_location LIKE CONCAT('%', #{req.activityLocation}, '%')
        </if>
        <!-- 根据活动状态查询 -->
        <if test="req.progressStatus ==1 ">
            <!--  this.registrationTime.isAfter(LocalDateTime.now()) || (this.registrationDeadline.isBefore(LocalDateTime.now()) && this.startTime.isAfter(LocalDateTime.now()))-->
            AND (cm.registration_time &gt; now() or (cm.registration_deadline &lt; now() AND cm.start_time &gt; now()))
        </if>
        <if test="req.progressStatus ==2 ">
            AND cm.registration_time &lt; now() AND registration_deadline &gt; now()
        </if>
        <if test="req.progressStatus ==3 ">
            AND cm.start_time &lt; now() AND end_time &gt; now()
        </if>
        <if test="req.progressStatus ==4 ">
            AND end_time &lt; now()
        </if>
        <!-- 根据审核状态查询 -->
        <if test="req.status != null">
            AND cm.status = #{req.status}
        </if>
        <if test="req.ancestors != null">
            AND cm.dept_id in
            <foreach collection="req.ancestors" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>

        <if test="req.searchText != null and req.searchText != ''">
            AND (cm.activity_name LIKE CONCAT('%', #{req.searchText}, '%') or cm.activity_location LIKE CONCAT('%', #{req.searchText}, '%'))
        </if>

        ORDER BY
        CASE
        WHEN now() BETWEEN cm.registration_time AND registration_deadline THEN 1
        WHEN now() BETWEEN cm.start_time AND end_time THEN 2
        WHEN now() &lt; cm.registration_time THEN 3
        ELSE 4
        END,update_time desc
    </select>
    <select id="countClubActivity" resultType="com.ows.ufa.system.vo.count.ClubActivityCountVO">
        select
        COUNT(case when (registration_time &gt; now() or (registration_deadline &lt; now() AND start_time &gt; now()))
        then 1 end) notStartedCount,
        COUNT(case when registration_time &lt; NOW() and registration_deadline &gt; NOW() then 1 end) signUpCount,
        COUNT(case when start_time &lt; NOW() and end_time &gt; NOW() then 1 end) inProgressCount,
        COUNT(case when end_time &lt; NOW() then 1 end) finishedCount
        FROM
        t_club_activities
        where del_flag =1 and status =1
        AND dept_id in
        <foreach collection="ancestors" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>
    <select id="findClubActivities" resultType="com.ows.ufa.system.vo.ClubActivitiesVO">
        SELECT
        cm.*,
        tci.club_name AS clubInfoName
        FROM
        t_club_activities cm
        LEFT JOIN
        t_club_info tci ON cm.club_info_id = tci.id
        WHERE
        cm.del_flag = 1 and cm.id=#{id}
    </select>

</mapper>