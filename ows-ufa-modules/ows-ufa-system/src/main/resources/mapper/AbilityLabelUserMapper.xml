<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.AbilityLabelUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.AbilityLabelUser">
        <id column="id" property="id" />
        <result column="ability_id" property="abilityId" />
        <result column="label_id" property="labelId" />
        <result column="user_id" property="userId" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ability_id, label_id, user_id, create_at, update_at, create_time, update_time
    </sql>
    <select id="queryStudentLabels" resultType="com.ows.ufa.system.vo.AbilityLabelUserVO">
        select talu.id, talu.ability_id, talu.label_id,talu.user_id ,tali."name" labelName from t_ability_label_user talu left join t_ability_label_info tali on talu.label_id =tali.id
        where talu.user_id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        order by talu.user_id
    </select>

</mapper>