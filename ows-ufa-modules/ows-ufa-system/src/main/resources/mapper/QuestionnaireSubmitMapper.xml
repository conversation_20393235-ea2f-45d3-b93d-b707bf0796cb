<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.QuestionnaireSubmitMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.QuestionnaireSubmit">
        <id column="id" property="id" />
        <result column="questionnaire_id" property="questionnaireId" />
        <result column="user_name" property="userName" />
        <result column="content_json" property="contentJson" />
        <result column="del_flag" property="delFlag" />
        <result column="dept_id" property="deptId" />
        <result column="create_at" property="createAt" />
        <result column="update_at" property="updateAt" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, questionnaire_id, user_name, content_json, del_flag, dept_id, create_at, update_at, create_time, update_time
    </sql>
    <select id="countFilledQuantity" resultType="java.lang.Long">
        select count(1) from (SELECT  questionnaire_id,count(1) FROM t_questionnaire_submit
        WHERE (del_flag = 1 AND create_at = #{req.userId}) GROUP BY questionnaire_id)t left join t_questionnaire tq on t.questionnaire_id=tq.id
        WHERE tq.del_flag = 1 and tq.status >0
        and ((tq.fill_permission=0 AND tq.dept_id in
            <foreach collection="req.ancestors" item="dId" open="(" separator="," close=")">
                #{dId}
            </foreach>)
        or <![CDATA[ (tq.fill_permission =1 and  #{req.deptId} = ANY(string_to_array(tq.fill_permission_dept_ids, '&&&')::bigint[]))) ]]>
    </select>
    <select id="countUnfilledQuantity" resultType="java.lang.Long">
        select count(1) from (select count(1) from t_questionnaire_submit tqs where del_flag=1 and tqs.questionnaire_id=#{questionnaireId} group by create_at )t
    </select>
    <select id="submitTrendsCount" resultType="com.ows.ufa.system.vo.count.StatisticsTrendsCountVO">
        SELECT
        TO_CHAR(create_time, 'YYYY-MM-DD') AS submit_date,
        COUNT(*) AS submit_count
        FROM
        t_questionnaire_submit
        WHERE
        del_flag = 1 and questionnaire_id=#{questionnaireId} and
        create_time >= CURRENT_TIMESTAMP - INTERVAL '7 days'
        GROUP BY
        TO_CHAR(create_time, 'YYYY-MM-DD')
        ORDER BY
        submit_date;
    </select>

</mapper>