<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ows.ufa.system.mapper.TeacherResourcesApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.ows.ufa.system.entity.TeacherResourcesApply">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="title" property="title" />
        <result column="descp" property="descp" />
        <result column="status" property="status" />
        <result column="user_name" property="userName" />
        <result column="school_name" property="schoolName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, title, descp, status, user_name, school_name, create_time, update_time
    </sql>

</mapper>