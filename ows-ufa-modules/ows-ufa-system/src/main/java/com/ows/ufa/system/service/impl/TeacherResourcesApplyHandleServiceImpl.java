package com.ows.ufa.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.system.entity.NoticeLog;
import com.ows.ufa.system.entity.TeacherResourcesApply;
import com.ows.ufa.system.entity.TeacherResourcesApplyHandle;
import com.ows.ufa.system.enums.NoticeType;
import com.ows.ufa.system.enums.ResourcesStatus;
import com.ows.ufa.system.mapper.TeacherResourcesApplyHandleMapper;
import com.ows.ufa.system.mapper.TeacherResourcesApplyMapper;
import com.ows.ufa.system.request.TeacherResourcesApplyHandleRequest;
import com.ows.ufa.system.service.NoticeLogService;
import com.ows.ufa.system.service.TeacherResourcesApplyHandleService;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.TeacherResourcesApplyHandleVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * <p>
 * 师资资源申请处理流程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class TeacherResourcesApplyHandleServiceImpl extends ServiceImpl<TeacherResourcesApplyHandleMapper, TeacherResourcesApplyHandle> implements TeacherResourcesApplyHandleService {

    private final TeacherResourcesApplyMapper teacherResourcesApplyMapper;
    private final NoticeLogService noticeLogService;

    @Override
    public List<TeacherResourcesApplyHandle> queryTeacherResourcesApplyHandles(TeacherResourcesApplyHandleRequest request) {
        TeacherResourcesApplyHandle entity = (TeacherResourcesApplyHandle) DataTransfer.transfer(request, TeacherResourcesApplyHandle.class);
        LambdaQueryWrapper<TeacherResourcesApplyHandle> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public TeacherResourcesApplyHandleVO findTeacherResourcesApplyHandle(Long id) {
        TeacherResourcesApplyHandle entity = this.getById(id);
        TeacherResourcesApplyHandleVO vo = (TeacherResourcesApplyHandleVO) DataTransfer.transfer(entity, TeacherResourcesApplyHandleVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveTeacherResourcesApplyHandle(TeacherResourcesApplyHandleVO vo) {
        TeacherResourcesApplyHandle entity = (TeacherResourcesApplyHandle) DataTransfer.transfer(vo, TeacherResourcesApplyHandle.class);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTeacherResourcesApplyHandle(TeacherResourcesApplyHandleVO vo) {
        TeacherResourcesApplyHandle entity = (TeacherResourcesApplyHandle) DataTransfer.transfer(vo, TeacherResourcesApplyHandle.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTeacherResourcesApplyHandle(Long id) {
        return this.removeById(id);
    }

    @Override
    public boolean callback(Object data) {
        String jsonString = JSON.toJSONString(data);
        log.info("收到事件更新回调数据：{}", jsonString);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handleEventDispatch(JSONObject event) {
        try {
            // 获取eventInfo中的eventNum
            log.info("收到事件分派回调数据：{}", event.toJSONString());
            String eventNum = event.getJSONObject("eventInfo").getString("eventNum");

            // 获取logList中的最后一条记录
            JSONArray logList = event.getJSONArray("logList");
            if (logList == null || logList.isEmpty()) {
                log.error("处理事件分发失败：logList为空, eventNum: {}", eventNum);
                return false;
            }
            String executor = null;
            String executorDept = null;
            String executeTime = null;
            String executeContent = null;
            for (int i = logList.size() - 1; i >= 0; i--) {
                //TODO 根据事项名称来匹配处置结果
                JSONObject log = logList.getJSONObject(i);
                if (null != log.getString("executeContent")) {
                    executor = log.getString("executor");
                    executorDept = log.getString("executeDept");
                    executeTime = log.getString("executeTime");
                    executeContent = log.getString("executeContent");
                }
            }
            if (null == executeTime) {
                int index = Math.max(logList.size() - 2, 0);
                JSONObject lastLog = logList.getJSONObject(index);
                // 从最后一条记录中获取需要的信息
                executor = lastLog.getString("executor");
                executorDept = lastLog.getString("executeDept");
                executeTime = lastLog.getString("executeTime");
                executeContent = lastLog.getString("executeContent");
            }
            // 更新TeacherResourcesApplyHandle表
            LambdaQueryWrapper<TeacherResourcesApplyHandle> handleWrapper = new LambdaQueryWrapper<>();
            handleWrapper.eq(TeacherResourcesApplyHandle::getEventNum, eventNum);
            TeacherResourcesApplyHandle handle = this.getOne(handleWrapper);
            if (handle == null) {
                log.error("未找到对应的处理记录, eventNum: {}", eventNum);
                return false;
            }

            handle.setHandleUserName(executor);
            handle.setHandleDept(executorDept);
            if (executeTime.startsWith("1")) {
                handle.setUpdateTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(executeTime)), ZoneId.systemDefault()));
            } else {
                handle.setUpdateTime(LocalDateTime.parse(executeTime, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            handle.setDescp(executeContent);
            handle.setStatus(ResourcesStatus.PROCESSED.getCode());

            boolean handleUpdateResult = this.updateById(handle);

            // 更新TeacherResourcesApply的状态
            if (handleUpdateResult) {
                LambdaQueryWrapper<TeacherResourcesApply> applyWrapper = new LambdaQueryWrapper<>();
                applyWrapper.eq(TeacherResourcesApply::getEventNum, eventNum);

                TeacherResourcesApply apply = teacherResourcesApplyMapper.selectOne(applyWrapper);
                if (apply != null) {
                    apply.setStatus(ResourcesStatus.PROCESSED.getCode());
                    apply.setUpdateTime(LocalDateTime.now());
                    int update = teacherResourcesApplyMapper.updateById(apply);
                    if (update > 0) {
                        try {
                            NoticeLog noticeLog = new NoticeLog();
                            noticeLog.setContent(JSON.toJSONString(event));
                            noticeLog.setCreateTime(LocalDateTime.now());
                            noticeLog.setNoticeType(NoticeType.EVENT.getCode());
                            noticeLog.setStatus(1);
                            noticeLogService.save(noticeLog);
                        } catch (Exception e) {
                            log.error("保存事件分派日志错误{}, eventNum: {}", e.getMessage(), eventNum);
                        }
                        return true;
                    } else {
                        log.error("更新TeacherResourcesApply状态失败, eventNum: {}", eventNum);
                        return false;
                    }
                } else {
                    log.error("未找到对应的申请记录, eventNum: {}", eventNum);
                    return false;
                }
            }
            return false;
        } catch (Exception e) {
            log.error("处理事件分发异常", e);
            return false;
        }
    }
}