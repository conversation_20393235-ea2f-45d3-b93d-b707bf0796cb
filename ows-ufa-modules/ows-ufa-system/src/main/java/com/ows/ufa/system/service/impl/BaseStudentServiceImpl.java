package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.entity.BackupTeacher;
import com.ows.ufa.system.entity.BaseStudent;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.request.AbilityLabelUserRequest;
import com.ows.ufa.system.vo.BaseStudentVO;
import com.ows.ufa.system.form.BaseStudentForm;
import com.ows.ufa.system.request.BaseStudentRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.BaseStudentMapper;
import com.ows.ufa.system.service.BaseStudentService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.system.vo.StudentLabelVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.time.LocalDateTime;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 生源学生表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class BaseStudentServiceImpl extends ServiceImpl<BaseStudentMapper, BaseStudent> implements BaseStudentService {

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public TableDataInfo queryBaseStudents(BaseStudentRequest request) {
        LambdaQueryWrapper<BaseStudent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNoneBlank(request.getStuName()),BaseStudent::getStuName, request.getStuName());
        List<BaseStudent> list = this.list(queryWrapper);
        List<BaseStudentVO> baseStudentVOS = DataTransfer.transferList(list, BaseStudentVO.class);
        for (BaseStudentVO vo : baseStudentVOS) {
            vo.setAge(calculateAge(vo.getStuCard(), vo.getStuBirth()));
        }
        TableDataInfo tableDataInfo = new TableDataInfo();
        tableDataInfo.setRows(baseStudentVOS);
        tableDataInfo.setMsg("查询成功");
        tableDataInfo.setCode(200);
        Long total = this.count(queryWrapper);
        tableDataInfo.setTotal(total);
        return tableDataInfo;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public BaseStudentVO findBaseStudent(String id) {
        BaseStudent entity = this.lambdaQuery().eq(BaseStudent::getId,id).one();
        BaseStudentVO vo = (BaseStudentVO) DataTransfer.transfer(entity, BaseStudentVO.class);
        vo.setAge(calculateAge(vo.getStuCard(), vo.getStuBirth()));
        return vo;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<StudentLabelVO> queryStudentLabels(AbilityLabelUserRequest request) {
        LambdaQueryWrapper<BaseStudent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNoneBlank(request.getStuName()),BaseStudent::getStuName, request.getStuName());
        queryWrapper.eq(StringUtils.isNoneBlank(request.getStuHisJob()),BaseStudent::getStuHisJob, request.getStuHisJob());
        queryWrapper.in(null!=request.getUserIds(),BaseStudent::getId, request.getUserIds());
        List<BaseStudent> list = this.list(queryWrapper);
        List<StudentLabelVO> baseStudentVOS = DataTransfer.transferList(list, StudentLabelVO.class);
        for (StudentLabelVO vo : baseStudentVOS) {
            vo.setAge(calculateAge(vo.getStuCard(), vo.getStuBirth()));
        }
        return baseStudentVOS;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public String updatePhone() {
        List<BaseStudent> allList = this.lambdaQuery().list();
        //将所有数据手机号加密脱敏
        for(BaseStudent baseStudent : allList){
            if(StringUtils.isEmpty(baseStudent.getPhone())){
                continue;
            }
            //脱敏
//            String desPhone = com.ows.ufa.common.core.utils.DataTransfer.maskValue(backupTeacher.getTchPhone(), SensitiveType.PHONE);
            //加密
            String encrypt = com.ows.ufa.common.core.utils.DataTransfer.encryptValue(baseStudent.getPhone());
//            backupTeacher.setTchPhone(desPhone);
            baseStudent.setJmStuPhone(encrypt);
            this.updateById(baseStudent);
        }
        return "1";
    }

    /**
     * 根据身份证号或指定的出生日期计算年龄
     *
     * @param idCard       身份证号码（18位），如果为null则使用指定的出生日期
     * @param birthDateStr 指定的出生日期，格式为"yyyy-MM-dd"，当idCard为null时使用
     * @return 年龄，如果输入无效则返回-1
     */
    private int calculateAge(String idCard, String birthDateStr) {
        LocalDate birthDate;

        if (idCard != null && idCard.length() == 18) {
            // 从身份证号中提取出生日期
            String idBirthDateStr = idCard.substring(6, 14);
            birthDate = LocalDate.parse(idBirthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } else if (StringUtils.isNoneBlank(birthDateStr )) {
            // 使用指定的出生日期
            birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        } else {
            log.error("身份证号码为空且未提供出生日期:" + idCard);
            return -1;
        }

        LocalDate currentDate = LocalDate.now();
        if ((birthDate != null) && (!birthDate.isAfter(currentDate))) {
            Period period = Period.between(birthDate, currentDate);
            return period.getYears();
        } else {
            log.error("出生日期无效:" + birthDate);
            return -1;
        }
    }

}