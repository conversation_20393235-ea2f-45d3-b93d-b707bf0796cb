package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 班级表
 *
 */
@Data
@TableName("zs_plan_class")
@Schema(description ="班级表")
public class ZsPlanClass implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "计划id")
    private String planId;

    @Schema(description = "学院id")
    private String schoolId;

    @Schema(description = "院系id")
    private String deptId;

    @Schema(description = "院系名称")
    private String deptName;

    @Schema(description = "复学数量")
    private Integer backNum;

    @Schema(description = "续费数量")
    private Integer renewPayShowNum;

    @Schema(description = "新学员报名数量")
    private Integer signNum;

}