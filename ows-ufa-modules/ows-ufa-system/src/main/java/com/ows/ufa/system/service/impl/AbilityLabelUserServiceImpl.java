package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.AbilityLabelUser;
import com.ows.ufa.system.vo.AbilityLabelUserVO;
import com.ows.ufa.system.form.AbilityLabelUserForm;
import com.ows.ufa.system.request.AbilityLabelUserRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.AbilityLabelUserMapper;
import com.ows.ufa.system.service.AbilityLabelUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.system.vo.StudentLabelVO;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 标签人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class AbilityLabelUserServiceImpl extends ServiceImpl<AbilityLabelUserMapper, AbilityLabelUser> implements AbilityLabelUserService {

    @Override
    public List<AbilityLabelUser> queryAbilityLabelUsers(AbilityLabelUserRequest request) {
        AbilityLabelUser entity = (AbilityLabelUser) DataTransfer.transfer(request, AbilityLabelUser.class);
        LambdaQueryWrapper<AbilityLabelUser> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public AbilityLabelUserVO findAbilityLabelUser(Long id) {
        AbilityLabelUser entity = this.getById(id);
        AbilityLabelUserVO vo = (AbilityLabelUserVO) DataTransfer.transfer(entity, AbilityLabelUserVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAbilityLabelUser(AbilityLabelUserForm form) {
        AbilityLabelUser entity = (AbilityLabelUser) DataTransfer.transfer(form, AbilityLabelUser.class);
        entity.setCreateTime(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAbilityLabelUser(AbilityLabelUserForm form) {
        AbilityLabelUser entity = (AbilityLabelUser) DataTransfer.transfer(form, AbilityLabelUser.class);
        entity.setUpdateTime(LocalDateTime.now());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAbilityLabelUser(Long id) {
        return this.lambdaUpdate().eq(AbilityLabelUser::getId,id).remove();
    }

    @Override
    public void setLabelNames(List<StudentLabelVO> studentLabelVOS) {
        List<String> userIds = studentLabelVOS.stream().map(StudentLabelVO::getId).collect(Collectors.toList());
        List<AbilityLabelUserVO> abilityLabelUserVOS = this.baseMapper.queryStudentLabels(userIds);
        for(StudentLabelVO studentLabelVO : studentLabelVOS){
            List<AbilityLabelUserVO> labels = abilityLabelUserVOS.stream().filter(user -> user.getUserId().equalsIgnoreCase(studentLabelVO.getId())).collect(Collectors.toList());
            studentLabelVO.setLabels(labels);
        }
    }

    @Override
    public List<AbilityLabelUserVO> queryLabels(String userId) {
        List<AbilityLabelUserVO> abilityLabelUserVOS = this.baseMapper.queryStudentLabels(Arrays.asList(userId));
        return abilityLabelUserVOS.stream().collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAbilityLabelUser(List<AbilityLabelUserForm> form) {
        String userId = form.get(0).getUserId();
        this.lambdaUpdate().eq(AbilityLabelUser::getUserId,userId).remove();
        List<AbilityLabelUser> insertUsers=new ArrayList<>();
        for(AbilityLabelUserForm abilityLabelUserForm : form){
            AbilityLabelUser labelUser=new AbilityLabelUser();
            labelUser.setAbilityId(abilityLabelUserForm.getAbilityId());
            labelUser.setUserId(abilityLabelUserForm.getUserId());
            labelUser.setLabelId(abilityLabelUserForm.getLabelId());
            labelUser.setCreateAt(SecurityUtils.getUsername());
            labelUser.setCreateTime(LocalDateTime.now());
            insertUsers.add(labelUser);
        }
        return this.saveBatch(insertUsers);
    }
}