package com.ows.ufa.system.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * 师资资源申请表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(description="师资资源申请表Request")
public class TeacherResourcesApplyRequest implements Serializable {
    private static final long serialVersionUID=1L;
    @Schema(description = "用户姓名")
    private String userName;
    @Schema(description = "事件标题")
    private String title;
    @Schema(description = "状态:0-待处理;1-已处理")
    private Integer status;
    @Schema(description = "开始日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate beginTime;
    @Schema(description = "结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate endTime;
}