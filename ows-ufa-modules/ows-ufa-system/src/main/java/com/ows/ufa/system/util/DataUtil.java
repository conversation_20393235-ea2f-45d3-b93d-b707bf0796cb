package com.ows.ufa.system.util;


import java.text.DecimalFormat;
import java.util.List;
import java.util.Random;

public class DataUtil {


	public static int rate(long num1, long num2) {
		if (num1 == 0 || num2 == 0) {
			return 0;
		}
		double s = ((double) num1 / (double) num2) * 100;
		DecimalFormat df = new DecimalFormat("#");
		return Integer.parseInt(df.format(s));
	}

    public static int rate(int num1, int num2) {
        if (num1 == 0 || num2 == 0) {
            return 0;
        }
        double s = ((double) num1 / (double) num2) * 100;
        DecimalFormat df = new DecimalFormat("#");
        return Integer.parseInt(df.format(s));
    }

	public static String rate(long num1, long num2,String decimalFormatStr) {
		if (num1 == 0 || num2 == 0) {
			return "0";
		}
		double s = ((double) num1 / (double) num2) * 100;
		DecimalFormat df = new DecimalFormat(decimalFormatStr);
		return df.format(s);
	}

	public static double rateToDouble(long num1, long num2) {
		if (num1 == 0 || num2 == 0) {
			return 0;
		}
		double s = ((double) num1 / (double) num2) * 100;
		DecimalFormat df = new DecimalFormat("#0.00");
		return Double.parseDouble(df.format(s));
	}
}

