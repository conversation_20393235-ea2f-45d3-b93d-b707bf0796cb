package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Schema(description = "共享师资VO")
public class BaseTeacherVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "所属院系")
    private String deptName;

    @Schema(description = "教师名称")
    private String tchName;

    @Schema(description = "性别")
    private String tchSex;

    @Schema(description = "工作年限")
    private Integer tchWorkTime;

    @Schema(description = "学历")
    private String tchEdu;

    @Schema(description = "毕业学校")
    private String tchGraduatedSchool;

    @Schema(description = "家庭住址")
    private String tchAddr;

    @Schema(description = "管理员名称")
    private String schHead;

    @Schema(description = "管理员电话")
    private String schHeadPhone;

    @Schema(description = "加密管理员电话")
    private String jmSchHeadPhone;

}