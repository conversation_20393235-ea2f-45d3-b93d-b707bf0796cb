package com.ows.ufa.system.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 问卷调查信息表
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Data
@Schema(description ="问卷调查信息表Request")
public class QuestionnaireRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "标题")
    private String title;

    @Schema(description = "状态:0-未发布;1-收集中;2-已结束")
    private Integer status;

    @Schema(description = "填报状态:0-已填;1-未填;2-结束")
    private Integer submitStatus;

    @Schema(hidden = true)
    private String deptId;

    @Schema(hidden = true)
    private List<String> ancestors;

    @Schema(hidden = true)
    private String userId;

    @Schema(hidden = true)
    private String appName;
}