package com.ows.ufa.system.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.Questionnaire;
import com.ows.ufa.system.entity.QuestionnaireSubmit;
import com.ows.ufa.system.enums.QuestionType;
import com.ows.ufa.system.enums.QuestionnaireStatus;
import com.ows.ufa.system.mapper.QuestionnaireMapper;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.service.QuestionnaireService;
import com.ows.ufa.system.util.DataUtil;
import com.ows.ufa.system.util.DateUtils;
import com.ows.ufa.system.util.Md5Utils;
import com.ows.ufa.system.vo.BarChartVO;
import com.ows.ufa.system.vo.QuestionnaireOptionsVO;
import com.ows.ufa.system.vo.QuestionnaireRadioVO;
import com.ows.ufa.system.vo.QuestionnaireSubmitDetailVO;
import com.ows.ufa.system.vo.QuestionnaireSubmitOptions;
import com.ows.ufa.system.vo.QuestionnaireSubmitVO;
import com.ows.ufa.system.form.QuestionnaireSubmitForm;
import com.ows.ufa.system.request.QuestionnaireSubmitRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.QuestionnaireSubmitMapper;
import com.ows.ufa.system.service.QuestionnaireSubmitService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.system.vo.QuestionnaireVO;
import com.ows.ufa.system.vo.count.QuestionnaireSubmitCountVO;
import com.ows.ufa.system.vo.count.StatisticsCountVO;
import com.ows.ufa.system.vo.count.StatisticsTrendsCountVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 问卷调查信息填报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class QuestionnaireSubmitServiceImpl extends ServiceImpl<QuestionnaireSubmitMapper, QuestionnaireSubmit> implements QuestionnaireSubmitService {

    private final QuestionnaireMapper questionnaireMapper;
    private final QuestionnaireService questionnaireServiceImpl;

    @Override
    public List<QuestionnaireSubmit> queryQuestionnaireSubmits(QuestionnaireSubmitRequest request) {
        LambdaQueryWrapper<QuestionnaireSubmit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionnaireSubmit::getCreateAt, SecurityUtils.getThirdUserid());
        queryWrapper.eq(QuestionnaireSubmit::getQuestionnaireId, request.getQuestionnaireId());
        queryWrapper.orderByDesc(QuestionnaireSubmit::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public QuestionnaireSubmitDetailVO findQuestionnaireSubmit(Long id) {
        QuestionnaireSubmit entity = this.getById(id);
        QuestionnaireSubmitDetailVO vo= (QuestionnaireSubmitDetailVO) DataTransfer.transfer(entity,QuestionnaireSubmitDetailVO.class);
        Questionnaire questionnaire = questionnaireServiceImpl.getById(entity.getQuestionnaireId());
        vo.setQuestionnaire(questionnaire);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveQuestionnaireSubmit(QuestionnaireSubmitForm form) {
        Questionnaire questionnaire = questionnaireServiceImpl.getById(form.getQuestionnaireId());
        if (null == questionnaire) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (questionnaire.getStatus() != QuestionnaireStatus.COLLECTING.getCode()) {
            throw new ServiceException("该问卷收集未发布或已结束，无法作答");
        }
        if (questionnaire.getFillPermission() == 1) {
            List list = DataTransfer.transferStringToList(questionnaire.getFillPermissionDeptIds());
            if (!list.contains(SecurityUtils.getThirdDeptId())) {
                throw new ServiceException("你未在该问卷指定范围内，无法作答");
            }
        }
        if (questionnaire.getParticipationLimit() == 1) {
            Long count = this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                    .eq(QuestionnaireSubmit::getCreateAt, SecurityUtils.getThirdUserid())
                    .eq(QuestionnaireSubmit::getQuestionnaireId, questionnaire.getId()).count();
            if (count > 0) {
                throw new ServiceException("你已填写过该问卷，无法重复作答");
            }
        }
        if (questionnaire.getCollection() == 1) {
            Long count = this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                    .eq(QuestionnaireSubmit::getQuestionnaireId, questionnaire.getId()).count();
            if (count >= questionnaire.getCollectionLimit()) {
                throw new ServiceException("该问卷调查信息已达到收集总数，无法作答");
            }
        }
        QuestionnaireSubmit entity = (QuestionnaireSubmit) DataTransfer.transfer(form, QuestionnaireSubmit.class);
        entity.setUserName(SecurityUtils.getLoginUser().getSysUser().getNickName());
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        entity.setDeptName(SecurityUtils.getThirdDeptName());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestionnaireSubmit(QuestionnaireSubmitForm form) {
        Questionnaire questionnaire = questionnaireServiceImpl.getById(form.getQuestionnaireId());
        if (null == questionnaire) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (questionnaire.getStatus() != QuestionnaireStatus.COLLECTING.getCode()) {
            throw new ServiceException("该问卷调查信息未发布或已结束，无法修改");
        }
        if (questionnaire.getAllowModify() == 0) {
            throw new ServiceException("该问卷调查信息不允许修改");
        }
        QuestionnaireSubmit byId = this.getById(form.getId());
        if (null == byId) {
            throw new ServiceException("该问卷调查填报信息不存在");
        }
        if (!byId.getCreateAt().equals(SecurityUtils.getThirdUserid())) {
            throw new ServiceException("该问卷调查填报信息不属于你，无法修改");
        }
        QuestionnaireSubmit entity = (QuestionnaireSubmit) DataTransfer.transfer(form, QuestionnaireSubmit.class);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(SecurityUtils.getThirdUserid());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeQuestionnaireSubmit(Long id) {
        return this.lambdaUpdate().eq(QuestionnaireSubmit::getId, id).set(QuestionnaireSubmit::getDelFlag, DelFlag.DELETE.getCode()).update();
    }

    @Override
    public List<QuestionnaireVO> queryQuestionnaires(QuestionnaireRequest request) {
        request.setAncestors(SecurityUtils.getAncestors());
        request.setUserId(SecurityUtils.getThirdUserid());
        request.setDeptId(SecurityUtils.getThirdDeptId());
        return questionnaireMapper.querySubmitQuestionnaires(request);
    }

    @Override
    public QuestionnaireSubmitCountVO listCount() {
        QuestionnaireSubmitCountVO count = new QuestionnaireSubmitCountVO();
        QuestionnaireRequest req = new QuestionnaireRequest();
        req.setUserId(SecurityUtils.getThirdUserid());
        req.setDeptId(SecurityUtils.getThirdDeptId());
        req.setAncestors(SecurityUtils.getAncestors());
        count.setFilledQuantity(this.baseMapper.countFilledQuantity(req));
        List<QuestionnaireVO> list = questionnaireMapper.countSubmitQuestionnaires(req);
        count.setCompletedQuantity(list.stream().filter(questionnaireVO -> questionnaireVO.getStatus() == QuestionnaireStatus.ENDED.getCode()).count());
        count.setUnfilledQuantity(list.size() - count.getFilledQuantity());
        return count;
    }

    @Override
    public QuestionnaireSubmitDetailVO queryLastSubmit(Long id) {
        Questionnaire questionnaire = questionnaireServiceImpl.getById(id);
        if (null == questionnaire) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        QuestionnaireSubmit entity= this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                .eq(QuestionnaireSubmit::getQuestionnaireId, questionnaire.getId()).orderByDesc(QuestionnaireSubmit::getCreateTime)
                .last("limit 1").one();
        QuestionnaireSubmitDetailVO vo= (QuestionnaireSubmitDetailVO) DataTransfer.transfer(entity,QuestionnaireSubmitDetailVO.class);
        vo.setQuestionnaire(questionnaire);
        return vo;
    }

    @Override
    public StatisticsCountVO submitCount(Long id) {
        StatisticsCountVO count = new StatisticsCountVO();
        count.setFilledQuantity(this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                .eq(QuestionnaireSubmit::getQuestionnaireId, id).count());
        count.setUnfilledQuantity(this.baseMapper.countUnfilledQuantity(id));
        return count;
    }

    @Override
    public List<QuestionnaireSubmit> submitPageCount(Long id) {
        LambdaQueryWrapper<QuestionnaireSubmit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(QuestionnaireSubmit::getQuestionnaireId, id);
        queryWrapper.orderByDesc(QuestionnaireSubmit::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public BarChartVO submitTrendsCount(Long id) {
        BarChartVO barChart = new BarChartVO();
        List<StatisticsTrendsCountVO> statisticsTrends = this.baseMapper.submitTrendsCount(id);
        List<String> titles = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate today = LocalDate.now();
        Map<String, Long> map = statisticsTrends.stream()
                .collect(Collectors.toMap(
                        StatisticsTrendsCountVO::getSubmitDate,
                        StatisticsTrendsCountVO::getSubmitCount
                ));
        List<Long> values = new ArrayList<>();
        for (int i = 6; i >= 0; i--) {
            LocalDate date = today.minusDays(i);
            String formatDate = date.format(formatter);
            titles.add(formatDate);
            if (map.containsKey(formatDate)) {
                values.add(map.get(formatDate));
            } else {
                values.add(0l);
            }
        }
        barChart.setTitle(titles);
        barChart.setData(values);
        return barChart;
    }


    @Override
    public List<QuestionnaireOptionsVO> situationCount(Long id) {
        // 1. 获取问卷基础信息
        Questionnaire questionnaire = questionnaireServiceImpl.getById(id);
        if (questionnaire == null) {
            throw new ServiceException("问卷调查不存在");
        }

        // 2. 解析问卷题目列表
        List<QuestionnaireRadioVO> radioVOS = JSONArray.parseArray(
                questionnaire.getContentJson(), QuestionnaireRadioVO.class);

        // 3. 获取有效提交记录
        List<QuestionnaireSubmit> submitList = lambdaQuery()
                .eq(QuestionnaireSubmit::getQuestionnaireId, id)
                .eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                .list();
        List<String> questionTypes = Arrays.stream(QuestionType.values()).map(QuestionType::getCode).collect(Collectors.toList());
        // 4. 预处理：建立题目ID -> VO映射（过滤无效题型）
        Map<Long, QuestionnaireRadioVO> questionMap = radioVOS.stream().filter(q -> questionTypes.contains(q.getType()))
                .collect(Collectors.toMap(QuestionnaireRadioVO::getId, Function.identity()));

        // 5. 统计容器：按题目ID存储统计结果
        Map<Long, QuestionnaireOptionsVO> statsMap = new HashMap<>();

        // 6. 处理每个提交记录
        for (QuestionnaireSubmit submit : submitList) {
            List<QuestionnaireRadioVO> answers = JSONArray.parseArray(
                    submit.getContentJson(), QuestionnaireRadioVO.class);

            for (QuestionnaireRadioVO answer : answers) {
                Long questionId = answer.getId();
                if (!questionMap.containsKey(questionId)){
                    continue; // 无效题目
                }

                QuestionnaireRadioVO config = questionMap.get(questionId);
                QuestionnaireOptionsVO vo = statsMap.computeIfAbsent(
                        questionId,
                        k -> createVO(config)
                );

                // 根据题型处理答案
                handleAnswer(config, answer.getValue(), vo);
            }
        }

        // 7. 计算比例并返回结果
        return convertToFinalVO(statsMap.values().stream().collect(Collectors.toList()));
    }

    @Override
    public QuestionnaireVO findQuestionnaire(Long id) {
        Questionnaire questionnaire = questionnaireServiceImpl.getById(id);
        if (null == questionnaire) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        QuestionnaireVO questionnaireVO = (QuestionnaireVO) DataTransfer.transfer(questionnaire, QuestionnaireVO.class);
        Long count = this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                .eq(QuestionnaireSubmit::getQuestionnaireId, id).eq(QuestionnaireSubmit::getCreateAt, SecurityUtils.getThirdUserid()).count();
        questionnaireVO.setNum(count.intValue());
        return questionnaireVO;
    }

    @Override
    public List<QuestionnaireSubmit> situationList(IdRequest id) {
        List<QuestionnaireSubmit> list = this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                .eq(QuestionnaireSubmit::getQuestionnaireId, id.getId()).list();
        return list;
    }

    @Override
    public List<QuestionnaireRadioVO> titleList(Long id) {
        Questionnaire questionnaire = questionnaireServiceImpl.getById(id);
        if (null == questionnaire) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        return JSONArray.parseArray(questionnaire.getContentJson(), QuestionnaireRadioVO.class);
    }

    @Override
    public boolean touristSubmit(QuestionnaireSubmitForm form, String clientIPAddress) {
        Questionnaire questionnaire = questionnaireServiceImpl.getById(form.getQuestionnaireId());
        if (null == questionnaire) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if((StringUtils.isNotEmpty(questionnaire.getAppName()) && !Md5Utils.hash(questionnaire.getAppName()).equals(form.getAppName()))
                || (null == questionnaire.getAppName() && !"default".equals(form.getAppName()))){
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (questionnaire.getStatus() != QuestionnaireStatus.COLLECTING.getCode()) {
            throw new ServiceException("该问卷收集未发布或已结束，无法作答");
        }
        if (questionnaire.getFillPermission() == 1) {
            throw new ServiceException("该问卷为指定范围填写，无法作答");
        }
        if (questionnaire.getCollection() == 1) {
            Long count = this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                    .eq(QuestionnaireSubmit::getQuestionnaireId, questionnaire.getId()).count();
            if (count >= questionnaire.getCollectionLimit()) {
                throw new ServiceException("该问卷调查信息已达到收集总数，无法作答");
            }
        }
        QuestionnaireSubmit entity = (QuestionnaireSubmit) DataTransfer.transfer(form, QuestionnaireSubmit.class);
        entity.setUserName("游客");
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(clientIPAddress);
        entity.setDeptName("-");
        return this.save(entity);
    }

    @Override
    public boolean submitQuestionnaire(QuestionnaireSubmitForm form, String clientIPAddress) {
        Questionnaire questionnaire = questionnaireServiceImpl.getById(form.getQuestionnaireId());
        if (null == questionnaire) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (questionnaire.getStatus() != QuestionnaireStatus.COLLECTING.getCode()) {
            throw new ServiceException("该问卷收集未发布或已结束，无法作答");
        }
        if (questionnaire.getCollection() == 1) {
            Long count = this.lambdaQuery().eq(QuestionnaireSubmit::getDelFlag, DelFlag.VALID.getCode())
                    .eq(QuestionnaireSubmit::getQuestionnaireId, questionnaire.getId()).count();
            if (count >= questionnaire.getCollectionLimit()) {
                throw new ServiceException("该问卷调查信息已达到收集总数，无法作答");
            }
        }
        QuestionnaireSubmit entity = (QuestionnaireSubmit) DataTransfer.transfer(form, QuestionnaireSubmit.class);
        entity.setUserName("游客");
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(clientIPAddress);
        entity.setDeptName("第三方");
        return this.save(entity);
    }

    private QuestionnaireOptionsVO createVO(QuestionnaireRadioVO config) {
        QuestionnaireOptionsVO vo = new QuestionnaireOptionsVO();
        vo.setId(config.getId());
        vo.setName(config.getName());
        vo.setType(config.getType());
        List<QuestionnaireSubmitOptions> options = new ArrayList<>();
        for (QuestionnaireRadioVO.OptionItem item : config.getProps().getOptions()) {
            String value = item.getValue();
            options.add(new QuestionnaireSubmitOptions(value));
        }
        vo.setOptions(options);
        return vo;
    }

    private void handleAnswer(QuestionnaireRadioVO config, Object values, QuestionnaireOptionsVO vo) {
        String type = config.getType();
        if (null == values) {
            return;
        }
        if (type.equalsIgnoreCase(QuestionType.MULTIPLE_CHOICE.getCode())) {
            processMultiple(config, convertToList(values), vo);
        } else {
            processSingle(config, (String) values, vo);
        }
    }

    private List<String> convertToList(Object obj) {
        if (obj instanceof Collection<?>) {
            Collection<?> collection = (Collection<?>) obj;
            return collection.stream()
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .collect(Collectors.toList());
        }
        throw new ServiceException("对象不是集合类型");
    }

    private void processSingle(QuestionnaireRadioVO config, String selectedValue, QuestionnaireOptionsVO vo) {
        updateOption(config, selectedValue, vo, 1);
    }

    private void processMultiple(QuestionnaireRadioVO config, List<String> values, QuestionnaireOptionsVO vo) {
        for (String value : values) {
            updateOption(config, value, vo, 1);
        }
    }

    private void updateOption(QuestionnaireRadioVO config, String value, QuestionnaireOptionsVO vo, int increment) {
        if (value == null) {
            return;
        }
        // 查找对应选项
        QuestionnaireRadioVO.OptionItem option = config.getProps().getOptions()
                .stream()
                .filter(opt -> opt.getValue().equals(value))
                .findFirst()
                .orElse(null);

        if (option == null) {
            return; // 无效选项
        }

        // 更新统计项
        Optional<QuestionnaireSubmitOptions> options = vo.getOptions().stream()
                .filter(so -> so.getName().equals(option.getValue()))
                .findFirst();
        if (!options.isPresent()) {
            return;
        }

        options.get().setTotal(options.get().getTotal() + increment);
//        vo.getOptions().add(submitOption);
    }

    private List<QuestionnaireOptionsVO> convertToFinalVO(List<QuestionnaireOptionsVO> stats) {
        return stats.stream()
                .map(vo -> {
                    vo.setOptions(vo.getOptions().stream()
                            .peek(so -> so.setRate(DataUtil.rate(so.getTotal(), getTotal(vo))))
                            .collect(Collectors.toList()));
                    return vo;
                })
                .collect(Collectors.toList());
    }


    private long getTotal(QuestionnaireOptionsVO vo) {
        return vo.getOptions().stream()
                .mapToLong(QuestionnaireSubmitOptions::getTotal)
                .sum();
    }


}