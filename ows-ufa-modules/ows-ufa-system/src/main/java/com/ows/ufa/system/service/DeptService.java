package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.Dept;
import com.ows.ufa.system.vo.DeptVO;
import com.ows.ufa.system.form.DeptForm;
import com.ows.ufa.system.request.DeptRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface DeptService extends IService<Dept> {

    List<Dept> queryDepts();

    Dept findDept(String deptId);
}
