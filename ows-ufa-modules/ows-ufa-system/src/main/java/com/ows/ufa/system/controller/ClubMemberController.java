package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.utils.poi.ExcelUtil;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.entity.ClubMember;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubMemberService;
import com.ows.ufa.system.form.ClubMemberForm;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.ClubInfoVO;
import com.ows.ufa.system.vo.ClubMemberVO;
import com.ows.ufa.system.vo.excel.ClubInfoExcelVO;
import com.ows.ufa.system.vo.excel.ClubMemberExcelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "clubMember")
@Tag(name = "clubMember", description = "社团成员接口")
public class ClubMemberController extends BaseController {

    private final ClubMemberService ClubMemberServiceImpl;

    @GetMapping("countClubMember")
    @Operation(summary = "社团成员统计")
    public AjaxResult countClub() {
        return success(ClubMemberServiceImpl.countClubMember());
    }

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubMemberByPage(ClubMemberRequest request) {
        startPage();
        return success(getDataTable(ClubMemberServiceImpl.queryClubMembers(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "查询详情")
    public AjaxResult findClubMember(@PathVariable Long id) {
        return success(ClubMemberServiceImpl.findClubMember(id));
    }

    @PostMapping
    @Operation(summary = "新增数据")
    public AjaxResult saveClubMember(@RequestBody ClubMemberForm form) {
        return success(ClubMemberServiceImpl.saveClubMember(form));
    }

    @PostMapping("update")
    @Operation(summary = "修改数据")
    public AjaxResult updateClubMember(@RequestBody ClubMemberForm form) {
        return success(ClubMemberServiceImpl.updateClubMember(form));
    }

    @PostMapping("delete")
    @Operation(summary = "删除数据")
    public AjaxResult removeClubMember(@RequestBody IdRequest id) {
        return success(ClubMemberServiceImpl.removeClubMember(id.getId()));
    }

    @PostMapping("/export")
    @Operation(summary = "社团成员导出")
    public void export(HttpServletResponse response,@RequestBody ClubMemberRequest request) {
        List<ClubMemberVO> list = ClubMemberServiceImpl.queryClubMembers(request);
        List<ClubMemberExcelVO> excelVOList = DataTransfer.transferList(list, ClubMemberExcelVO.class);
        ExcelUtil<ClubMemberExcelVO> util = new ExcelUtil<>(ClubMemberExcelVO.class);
        util.exportExcel(response, excelVOList, "社团成员数据");
    }
}
