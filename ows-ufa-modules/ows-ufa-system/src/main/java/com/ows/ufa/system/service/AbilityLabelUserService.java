package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.AbilityLabelUser;
import com.ows.ufa.system.vo.AbilityLabelUserVO;
import com.ows.ufa.system.form.AbilityLabelUserForm;
import com.ows.ufa.system.request.AbilityLabelUserRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.StudentLabelVO;

import java.util.List;
/**
 * <p>
 * 标签人员表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
public interface AbilityLabelUserService extends IService<AbilityLabelUser> {

    List<AbilityLabelUser> queryAbilityLabelUsers(AbilityLabelUserRequest request);

    AbilityLabelUserVO findAbilityLabelUser(Long id);

    Long saveAbilityLabelUser(AbilityLabelUserForm form);

    boolean updateAbilityLabelUser(AbilityLabelUserForm form);

    boolean removeAbilityLabelUser(Long id);

    void setLabelNames(List<StudentLabelVO> studentLabelVOS);

    List<AbilityLabelUserVO> queryLabels(String userId);

    boolean saveAbilityLabelUser(List<AbilityLabelUserForm> form);
}
