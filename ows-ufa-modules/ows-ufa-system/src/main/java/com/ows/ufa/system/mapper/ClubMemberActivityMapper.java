package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.ClubMemberActivity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.request.ClubMemberActivityRequest;
import com.ows.ufa.system.vo.ClubMemberActivityVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 社团成员活动信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Mapper
public interface ClubMemberActivityMapper extends BaseMapper<ClubMemberActivity> {

    List<ClubMemberActivityVO> queryClubMemberActivitys(@Param("req") ClubMemberActivityRequest request);

    ClubMemberActivityVO queryClubMemberActivity(@Param("id") Long id);
}