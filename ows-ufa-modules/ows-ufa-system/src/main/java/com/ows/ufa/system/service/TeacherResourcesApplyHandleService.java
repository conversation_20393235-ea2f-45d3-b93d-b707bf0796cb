package com.ows.ufa.system.service;

import com.alibaba.fastjson.JSONObject;
import com.ows.ufa.system.entity.TeacherResourcesApplyHandle;
import com.ows.ufa.system.vo.TeacherResourcesApplyHandleVO;
import com.ows.ufa.system.request.TeacherResourcesApplyHandleRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 * 师资资源申请处理流程表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface TeacherResourcesApplyHandleService extends IService<TeacherResourcesApplyHandle> {

    List<TeacherResourcesApplyHandle> queryTeacherResourcesApplyHandles(TeacherResourcesApplyHandleRequest request);

    TeacherResourcesApplyHandleVO findTeacherResourcesApplyHandle(Long id);

    Long saveTeacherResourcesApplyHandle(TeacherResourcesApplyHandleVO vo);

    boolean updateTeacherResourcesApplyHandle(TeacherResourcesApplyHandleVO vo);

    boolean removeTeacherResourcesApplyHandle(Long id);

    boolean callback(Object data);

    boolean handleEventDispatch(JSONObject event);
}
