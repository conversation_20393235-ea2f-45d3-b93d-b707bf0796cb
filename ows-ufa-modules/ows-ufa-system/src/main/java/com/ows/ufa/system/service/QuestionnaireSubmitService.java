package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.Questionnaire;
import com.ows.ufa.system.entity.QuestionnaireSubmit;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.vo.BarChartVO;
import com.ows.ufa.system.vo.QuestionnaireOptionsVO;
import com.ows.ufa.system.vo.QuestionnaireRadioVO;
import com.ows.ufa.system.vo.QuestionnaireSubmitDetailVO;
import com.ows.ufa.system.vo.QuestionnaireSubmitVO;
import com.ows.ufa.system.form.QuestionnaireSubmitForm;
import com.ows.ufa.system.request.QuestionnaireSubmitRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.QuestionnaireVO;
import com.ows.ufa.system.vo.count.QuestionnaireSubmitCountVO;
import com.ows.ufa.system.vo.count.StatisticsCountVO;

import java.util.List;
/**
 * <p>
 * 问卷调查信息填报表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
public interface QuestionnaireSubmitService extends IService<QuestionnaireSubmit> {

    List<QuestionnaireSubmit> queryQuestionnaireSubmits(QuestionnaireSubmitRequest request);

    QuestionnaireSubmitDetailVO findQuestionnaireSubmit(Long id);

    Long saveQuestionnaireSubmit(QuestionnaireSubmitForm form);

    boolean updateQuestionnaireSubmit(QuestionnaireSubmitForm form);

    boolean removeQuestionnaireSubmit(Long id);

    List<QuestionnaireVO> queryQuestionnaires(QuestionnaireRequest request);

    QuestionnaireSubmitCountVO listCount();

    QuestionnaireSubmitDetailVO queryLastSubmit(Long id);

    StatisticsCountVO submitCount(Long id);

    List<QuestionnaireSubmit> submitPageCount(Long id);

    BarChartVO submitTrendsCount(Long id);

    List<QuestionnaireOptionsVO> situationCount(Long id);

    QuestionnaireVO findQuestionnaire(Long id);

    List<QuestionnaireSubmit> situationList(IdRequest id);

    List<QuestionnaireRadioVO> titleList(Long id);

    boolean touristSubmit(QuestionnaireSubmitForm form, String clientIPAddress);

    boolean submitQuestionnaire(QuestionnaireSubmitForm form, String clientIPAddress);

}
