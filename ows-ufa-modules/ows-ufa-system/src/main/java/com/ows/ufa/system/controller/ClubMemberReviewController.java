package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.utils.poi.ExcelUtil;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.form.ClubMemberForm;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubMemberService;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.ClubMemberVO;
import com.ows.ufa.system.vo.excel.ClubMemberExcelVO;
import com.ows.ufa.system.vo.excel.ClubMemberReviewExcelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "clubMemberReview")
@Tag(name = "clubMemberReview", description = "社团成员申请审核接口")
public class ClubMemberReviewController extends BaseController {

    private final ClubMemberService ClubMemberServiceImpl;

    @GetMapping("countReview")
    @Operation(summary = "社团成员统计")
    public AjaxResult countReview() {
        return success(ClubMemberServiceImpl.countReview());
    }

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubMemberByPage(ClubMemberRequest request) {
        startPage();
        return success(getDataTable(ClubMemberServiceImpl.queryClubMemberReviews(request)));
    }

    @PostMapping("review")
    @Operation(summary = "审核")
    public AjaxResult review(@RequestBody ReviewForm form) {
        return success(ClubMemberServiceImpl.review(form));
    }

    @PostMapping("/export")
    @Operation(summary = "社团成员申请数据导出")
    public void export(HttpServletResponse response,@RequestBody ClubMemberRequest request) {
        List<ClubMemberVO> list = ClubMemberServiceImpl.queryClubMemberReviews(request);
        List<ClubMemberReviewExcelVO> excelVOList = DataTransfer.transferList(list, ClubMemberReviewExcelVO.class);
        ExcelUtil<ClubMemberReviewExcelVO> util = new ExcelUtil<>(ClubMemberReviewExcelVO.class);
        util.exportExcel(response, excelVOList, "社团成员申请数据");
    }
}
