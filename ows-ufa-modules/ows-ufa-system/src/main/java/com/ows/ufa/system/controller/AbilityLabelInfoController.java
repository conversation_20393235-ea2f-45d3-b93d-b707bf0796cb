/*
package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.AbilityLabelInfoService;
import com.ows.ufa.system.form.AbilityLabelInfoForm;
import com.ows.ufa.system.request.AbilityLabelInfoRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

*/
/**
 * <p>
 * 标签信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 *//*

@RestController
@RequiredArgsConstructor
@RequestMapping(value = "abilityLabelInfo")
@Tag(name = "abilityLabelInfo", description = "标签信息表接口")
public class AbilityLabelInfoController extends BaseController {

    private final AbilityLabelInfoService AbilityLabelInfoServiceImpl;

    @GetMapping("list")
    @Operation(summary = "标签信息表分页查询")
    public AjaxResult listAbilityLabelInfoByPage(AbilityLabelInfoRequest request) {
        startPage();
        return success(getDataTable(AbilityLabelInfoServiceImpl.queryAbilityLabelInfos(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "标签信息表查询详情")
    public AjaxResult findAbilityLabelInfo(@PathVariable Long id) {
        return success(AbilityLabelInfoServiceImpl.findAbilityLabelInfo(id));
    }

    @PostMapping
    @Operation(summary = "标签信息表新增数据")
    public AjaxResult saveAbilityLabelInfo(@RequestBody AbilityLabelInfoForm form) {
        return success(AbilityLabelInfoServiceImpl.saveAbilityLabelInfo(form));
    }

    @PostMapping("update")
    @Operation(summary = "标签信息表修改数据")
    public AjaxResult updateAbilityLabelInfo(@RequestBody AbilityLabelInfoForm form) {
        return success(AbilityLabelInfoServiceImpl.updateAbilityLabelInfo(form));
    }

    @PostMapping("delete")
    @Operation(summary = "标签信息表删除数据")
    public AjaxResult removeAbilityLabelInfo(@RequestBody IdRequest id) {
        return success(AbilityLabelInfoServiceImpl.removeAbilityLabelInfo(id.getId()));
    }
}
*/
