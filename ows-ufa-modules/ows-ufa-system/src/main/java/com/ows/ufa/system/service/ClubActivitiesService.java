package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.ClubActivities;
import com.ows.ufa.system.vo.ClubActivitiesVO;
import com.ows.ufa.system.form.ClubActivitiesForm;
import com.ows.ufa.system.request.ClubActivitiesRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.count.ClubActivityCountVO;

import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface ClubActivitiesService extends IService<ClubActivities> {

    List<ClubActivitiesVO> queryClubActivitiess(ClubActivitiesRequest request);

    List<ClubActivitiesVO> h5queryClubActivitiess(ClubActivitiesRequest request);

    ClubActivitiesVO findClubActivities(Long id);

    Long saveClubActivities(ClubActivitiesForm form);

    boolean updateClubActivities(ClubActivitiesForm form);

    boolean removeClubActivities(Long id);

    ClubActivityCountVO countClubActivity();

    ClubActivitiesVO h5findClubActivities(Long id);
}
