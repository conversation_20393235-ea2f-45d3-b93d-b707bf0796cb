package com.ows.ufa.system.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.entity.QuestionnaireSubmit;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.QuestionnaireSubmitService;
import com.ows.ufa.system.util.ExcelUtil;
import com.ows.ufa.system.vo.QuestionnaireRadioVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * <p>
 * 问卷调查信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "questionnaireStatistics")
@Tag(name = "questionnaire", description = "问卷调查信息表接口")
@Slf4j
public class QuestionnaireStatisticsController extends BaseController {

    private final QuestionnaireSubmitService questionnaireSubmitServiceImpl;

    @Value("${app.file-base}")
    private String fileBase;

    @Value("${app.profile}")
    private String profile;

    @GetMapping("submitCount")
    @Operation(summary = "提交情况统计")
    public AjaxResult listCount(IdRequest idRequest) {
        return success(questionnaireSubmitServiceImpl.submitCount(idRequest.getId()));
    }

    @GetMapping("submitPageCount")
    @Operation(summary = "提交情况列表统计")
    public AjaxResult submitPageCount(IdRequest idRequest) {
        return success(questionnaireSubmitServiceImpl.submitPageCount(idRequest.getId()));
    }

    @GetMapping("submitTrendsCount")
    @Operation(summary = "每日提交趋势统计")
    public AjaxResult submitTrends(IdRequest idRequest) {
        return success(questionnaireSubmitServiceImpl.submitTrendsCount(idRequest.getId()));
    }

    @GetMapping("situationCount")
    @Operation(summary = "问卷情况统计")
    public AjaxResult situationCount(IdRequest idRequest) {
        return success(questionnaireSubmitServiceImpl.situationCount(idRequest.getId()));
    }

    @GetMapping("titleList")
    @Operation(summary = "问卷情况题目列表")
    public AjaxResult titleList(IdRequest idRequest) {
        return success(questionnaireSubmitServiceImpl.titleList(idRequest.getId()));
    }

    @GetMapping("situationList")
    @Operation(summary = "问卷情况统计列表")
    public AjaxResult situationList(IdRequest idRequest) {
        startPage();
        return success(getDataTable(questionnaireSubmitServiceImpl.situationList(idRequest)));
    }

    @PostMapping("situationExport")
    @Operation(summary = "问卷情况统计列表导出")
    public void export(HttpServletResponse response, @RequestBody IdRequest idRequest) {
        List<QuestionnaireSubmit> questionnaireSubmits = questionnaireSubmitServiceImpl.situationList(idRequest);
        List<QuestionnaireRadioVO> radioVOS = questionnaireSubmitServiceImpl.titleList(idRequest.getId());
        List<QuestionnaireRadioVO> titles = radioVOS.stream().filter(item -> idRequest.getIds().contains(item.getId())).collect(Collectors.toList());
        ExcelUtil excelUtil = new ExcelUtil();
        excelUtil.exportToExcel(response, questionnaireSubmits, titles);
    }

    @PostMapping("/situationDownloadZip")
    @Operation(summary = "问卷情况统计附件下载ZIP")
    public void downloadZip(HttpServletResponse response, @RequestBody IdRequest idRequest) {
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream())) {
            List<QuestionnaireSubmit> questionnaireSubmits = questionnaireSubmitServiceImpl.situationList(idRequest);
            for (QuestionnaireSubmit submit : questionnaireSubmits) {
                List<QuestionnaireRadioVO> contentJson = JSONArray.parseArray(submit.getContentJson(), QuestionnaireRadioVO.class);
                List<QuestionnaireRadioVO> fileUrls = contentJson.stream()
                        .filter(item -> ("DragUploadFile".equalsIgnoreCase(item.getType())
                                || "DragUploadImg".equalsIgnoreCase(item.getType())))
                        .filter(item -> idRequest.getIds().contains(item.getId()))
                        .filter(item -> item.getValue() != null)
//                        .flatMap(item -> ((JSONArray) item.getValue()).stream()
//                                .map(obj -> (JSONObject) obj)
//                                .map(img -> (String) img.get("url"))
//                                .filter(Objects::nonNull))
                        .collect(Collectors.toList());
                for (QuestionnaireRadioVO radio : fileUrls) {
                    JSONArray jsons = (JSONArray) radio.getValue();
                    for (Object json : jsons) {
                        JSONObject jsonObject = (JSONObject) json;
                        String fileUrl = jsonObject.getString("url");
                        String fileName = jsonObject.getString("name");
                        if (StringUtils.isBlank(fileUrl) || StringUtils.isBlank(fileName)) {
                            continue;
                        }
                        fileUrl = fileUrl.replace(fileBase + "/", profile);
                        File file = new File(fileUrl);
                        if (!file.exists()) {
                            // 如果文件不存在，跳过并记录日志
                            log.warn("文件不存在: {}", submit.getContentJson());
                            continue;
                        }
                        try (FileInputStream fis = new FileInputStream(file);
                             BufferedInputStream bis = new BufferedInputStream(fis)) {
                            // 使用相对路径避免Zip Slip漏洞
                            String entryName = radio.getName() + "/" + submit.getUserName() + "/" + submit.getId() + "/" + fileName;
                            ZipEntry zipEntry = new ZipEntry(entryName);
                            zos.putNextEntry(zipEntry);

                            byte[] buffer = new byte[4096];
                            int bytesRead;
                            while ((bytesRead = bis.read(buffer)) != -1) {
                                zos.write(buffer, 0, bytesRead);
                            }
                            zos.closeEntry();
                        } catch (IOException e) {
                            log.error("添加文件到ZIP失败: {}", file.getAbsolutePath(), e);
                            // 可以选择继续处理其他文件或中断整个操作
                        }
                    }
                }
            }
            // 完成所有条目的写入
            zos.finish();
        } catch (Exception e) {
            log.error("下载文件失败", e);
        } finally {
            // 设置响应头
            response.setHeader("Content-Disposition", "attachment; filename=projects.zip");
            response.setContentType("application/octet-stream");
            try {
                response.getOutputStream().flush();
            } catch (IOException e) {
                log.error("输出流刷新失败", e);
            }
        }
    }
}
