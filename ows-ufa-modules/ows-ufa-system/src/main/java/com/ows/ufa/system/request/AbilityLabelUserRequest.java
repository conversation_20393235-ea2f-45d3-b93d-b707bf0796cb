package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 标签人员表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@Schema(description ="标签人员表Request")
public class AbilityLabelUserRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "姓名")
    private String stuName;

    @Schema(description = "原职务[1经理 2部长 3局长 4处长 5主管 6科长 9其他]")
    private String stuHisJob;

    @Schema(description = "标签ID")
    private Long labelId;

    @Schema(hidden = true)
    private List<String> userIds;
}