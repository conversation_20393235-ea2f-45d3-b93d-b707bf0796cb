package com.ows.ufa.system.h5.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.entity.AbilityLabelUser;
import com.ows.ufa.system.form.AbilityLabelUserForm;
import com.ows.ufa.system.request.AbilityLabelUserRequest;
import com.ows.ufa.system.service.AbilityLabelUserService;
import com.ows.ufa.system.service.BaseStudentService;
import com.ows.ufa.system.vo.BaseStudentVO;
import com.ows.ufa.system.vo.StudentLabelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签人员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "h5/studentLabel")
@Tag(name = "h5studentLabel", description = "h5人员画像接口")
public class H5StudentLabelController extends BaseController {

    private final AbilityLabelUserService AbilityLabelUserServiceImpl;

    private final BaseStudentService BaseStudentServiceImpl;

    @GetMapping("list")
    @Operation(summary = "标签人员表分页查询")
    public AjaxResult listAbilityLabelUserByPage(AbilityLabelUserRequest request) {
        if (null != request.getLabelId()) {
            List<AbilityLabelUser> list = AbilityLabelUserServiceImpl.lambdaQuery().eq(AbilityLabelUser::getLabelId, request.getLabelId()).list();
            List<String> userIds = list.stream().map(AbilityLabelUser::getUserId).collect(Collectors.toList());
            request.setUserIds(userIds);
        }
        startPage();
        List<StudentLabelVO> studentLabelVOS = BaseStudentServiceImpl.queryStudentLabels(request);
        if(!studentLabelVOS.isEmpty()){
            AbilityLabelUserServiceImpl.setLabelNames(studentLabelVOS);
        }
        return success(getDataTable(studentLabelVOS));
    }

    @GetMapping("{id}")
    @Operation(summary = "生源学生表查询详情")
    public AjaxResult findBaseStudent(@PathVariable String id) {
        BaseStudentVO baseStudent = BaseStudentServiceImpl.findBaseStudent(id);
        baseStudent.setLabels(AbilityLabelUserServiceImpl.queryLabels(id));
        return success(baseStudent);
    }

    @PostMapping
    @Operation(summary = "标签人员表新增数据")
    public AjaxResult saveAbilityLabelUser(@RequestBody List<AbilityLabelUserForm> forms) {
        return success(AbilityLabelUserServiceImpl.saveAbilityLabelUser(forms));
    }
}
