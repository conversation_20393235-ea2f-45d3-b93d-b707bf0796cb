package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="社团活动统计VO")
public class ClubActivityCountVO implements Serializable {
    private static final long serialVersionUID=1L;
    @Schema(description = "未开始数量")
    private Integer notStartedCount;

    @Schema(description = "报名中数量")
    private Integer signUpCount;

    @Schema(description = "进行中数量")
    private Integer inProgressCount;

    @Schema(description = "已结束数量")
    private Integer finishedCount;

}