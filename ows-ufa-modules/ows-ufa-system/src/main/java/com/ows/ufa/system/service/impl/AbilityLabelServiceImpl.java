package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.AbilityLabel;
import com.ows.ufa.system.entity.AbilityLabelInfo;
import com.ows.ufa.system.entity.AbilityLabelUser;
import com.ows.ufa.system.form.AbilityLabelInfoForm;
import com.ows.ufa.system.form.AbilityLabelUserForm;
import com.ows.ufa.system.service.AbilityLabelInfoService;
import com.ows.ufa.system.service.AbilityLabelUserService;
import com.ows.ufa.system.vo.AbilityLabelInfoVO;
import com.ows.ufa.system.vo.AbilityLabelVO;
import com.ows.ufa.system.form.AbilityLabelForm;
import com.ows.ufa.system.request.AbilityLabelRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.AbilityLabelMapper;
import com.ows.ufa.system.service.AbilityLabelService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.time.LocalDateTime;
import java.util.stream.Collectors;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 能力标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class AbilityLabelServiceImpl extends ServiceImpl<AbilityLabelMapper, AbilityLabel> implements AbilityLabelService {

    private final AbilityLabelInfoService abilityLabelInfoServiceImpl;
    private final AbilityLabelUserService abilityLabelUserServiceImpl;

    @Override
    public List<AbilityLabelVO> queryAbilityLabels(AbilityLabelRequest request) {
        request.setAncestors(SecurityUtils.getAncestors());
        return this.baseMapper.queryAbilityLabels(request);
    }

    @Override
    public AbilityLabelVO findAbilityLabel(Long id) {
        AbilityLabel entity = this.getById(id);
        AbilityLabelVO vo = (AbilityLabelVO) DataTransfer.transfer(entity, AbilityLabelVO.class);
        List<AbilityLabelInfo> labelInfos = abilityLabelInfoServiceImpl.lambdaQuery().eq(AbilityLabelInfo::getAbilityId, id).list();
        if(!labelInfos.isEmpty()){
            List<AbilityLabelUser> labelUsers = abilityLabelUserServiceImpl.lambdaQuery().eq(AbilityLabelUser::getAbilityId, id).list();
            List<AbilityLabelInfoVO> labelInfoVOS=DataTransfer.transferList(labelInfos,AbilityLabelInfoVO.class);
            for(AbilityLabelInfoVO labelInfoVO : labelInfoVOS){
                labelInfoVO.setLabelUsers(labelUsers.stream().filter(user->user.getLabelId().equals(labelInfoVO.getId())).collect(Collectors.toList()));
            }
            vo.setAbilityLabelInfos(labelInfoVOS);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAbilityLabel(AbilityLabelForm form) {
        AbilityLabel entity = new AbilityLabel();
        entity.setTitle(form.getTitle());
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        this.save(entity);
        saveAbilityLabel(form, entity);
        return entity.getId();
    }

    private void saveAbilityLabel(AbilityLabelForm form, AbilityLabel entity) {
        //保存标签列表
        List<AbilityLabelInfoForm> labelInfos = form.getLabelInfos();
        for (AbilityLabelInfoForm labelInfoForm : labelInfos) {
            AbilityLabelInfo abilityLabelInfo = new AbilityLabelInfo();
            abilityLabelInfo.setName(labelInfoForm.getName());
            abilityLabelInfo.setCreateTime(LocalDateTime.now());
            abilityLabelInfo.setCreateAt(SecurityUtils.getThirdUserid());
            abilityLabelInfo.setAbilityId(entity.getId());
            abilityLabelInfoServiceImpl.save(abilityLabelInfo);
            //保存标签用户
            List<AbilityLabelUserForm> labelUserForms = labelInfoForm.getLabelUserForms();
            if (labelUserForms != null && !labelUserForms.isEmpty()) {
                List<AbilityLabelUser> abilityLabelUsers = new ArrayList<>();
                for (AbilityLabelUserForm labelUserForm : labelUserForms) {
                    AbilityLabelUser abilityLabelUser = new AbilityLabelUser();
                    abilityLabelUser.setAbilityId(entity.getId());
                    abilityLabelUser.setLabelId(abilityLabelInfo.getId());
                    abilityLabelUser.setUserId(labelUserForm.getUserId());
                    abilityLabelUser.setCreateTime(LocalDateTime.now());
                    abilityLabelUser.setCreateAt(SecurityUtils.getThirdUserid());
                    abilityLabelUsers.add(abilityLabelUser);
                }
                abilityLabelUserServiceImpl.saveBatch(abilityLabelUsers);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAbilityLabel(AbilityLabelForm form) {
        AbilityLabel entity = this.getById(form.getId());
        if (null == entity) {
            throw new ServiceException("能力标签不存在");
        }
        entity.setTitle(form.getTitle());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(SecurityUtils.getThirdUserid());
        //保存标签列表
        abilityLabelInfoServiceImpl.lambdaUpdate().eq(AbilityLabelInfo::getAbilityId, entity.getId()).remove();
        abilityLabelUserServiceImpl.lambdaUpdate().eq(AbilityLabelUser::getAbilityId, entity.getId()).remove();
        saveAbilityLabel(form, entity);
        return this.updateById(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAbilityLabel(Long id) {
        abilityLabelInfoServiceImpl.lambdaUpdate().eq(AbilityLabelInfo::getAbilityId, id).remove();
        abilityLabelUserServiceImpl.lambdaUpdate().eq(AbilityLabelUser::getAbilityId, id).remove();
        return this.lambdaUpdate().eq(AbilityLabel::getId, id).set(AbilityLabel::getDelFlag, DelFlag.DELETE.getCode()).update();
    }

    @Override
    public List<AbilityLabelInfo> queryLabelInfos() {
        List<AbilityLabel> list = this.lambdaQuery().select(AbilityLabel::getId).eq(AbilityLabel::getDelFlag, DelFlag.VALID.getCode())
                .in(AbilityLabel::getDeptId, SecurityUtils.getAncestors()).list();
        if(!list.isEmpty()){
            return abilityLabelInfoServiceImpl.lambdaQuery().select(AbilityLabelInfo::getId,AbilityLabelInfo::getName)
                    .in(AbilityLabelInfo::getAbilityId,list.stream().map(AbilityLabel::getId).collect(Collectors.toList())).list();
        }
        return new ArrayList<>();
    }
}