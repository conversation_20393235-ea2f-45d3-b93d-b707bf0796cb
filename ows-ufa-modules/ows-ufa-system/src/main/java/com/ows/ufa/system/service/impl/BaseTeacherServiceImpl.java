package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.entity.BaseSchool;
import com.ows.ufa.system.entity.BaseStudent;
import com.ows.ufa.system.entity.BaseTeacher;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.mapper.BaseSchoolMapper;
import com.ows.ufa.system.util.DataUtil;
import com.ows.ufa.system.vo.BaseTeacherVO;
import com.ows.ufa.system.request.BaseTeacherRequest;
import com.ows.ufa.system.mapper.BaseTeacherMapper;
import com.ows.ufa.system.service.BaseTeacherService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.system.vo.count.BaseTeacherCountVO;
import com.ows.ufa.system.vo.count.BaseTeacherEduCountVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class BaseTeacherServiceImpl extends ServiceImpl<BaseTeacherMapper, BaseTeacher> implements BaseTeacherService {

    @Autowired
    private BaseSchoolMapper baseSchoolMapper;

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<BaseTeacherVO> querySchoolTeachers(BaseTeacherRequest request) {
        return this.baseMapper.querySchoolTeachers(request);
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public String hasTeacher(String idNo) {
        BaseTeacher one = this.lambdaQuery().eq(BaseTeacher::getTchCard, idNo).eq(BaseTeacher::getStatus, 1).one();
        if(null!=one){
            return one.getId();
        }
        return null;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public String saveBaseTeacher(BaseTeacher entity) {
        this.save(entity);
        return entity.getId();
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public BaseTeacherCountVO countBaseTeacher() {
        BaseTeacherCountVO countVO =  this.baseMapper.countTeacher();
        Long schoolCount =  this.baseMapper.countSchool();
        Long deptCount =  this.baseMapper.countDept();
        countVO.setSchoolCount(schoolCount);
        countVO.setDeptCount(deptCount);
        countVO.setMaleRatio(DataUtil.rate(countVO.getMaleRatio(), countVO.getTeacherCount()));
        countVO.setFemaleRatio(DataUtil.rate(countVO.getFemaleRatio(), countVO.getTeacherCount()));
        List<BaseTeacherEduCountVO> list=this.baseMapper.countTeacherEdu();
        if(!list.isEmpty()){
            //eduCount 加和
            int totalEduCount = list.stream()
                    .map(BaseTeacherEduCountVO::getEduCount)
                    .reduce(0, Integer::sum);
            for(BaseTeacherEduCountVO vo:list){
                vo.setEduRatio(DataUtil.rate(vo.getEduCount(), totalEduCount));
            }
            countVO.setEduCounts(list);
        }
        return countVO;
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public String updatePhone() {
        List<BaseSchool> allList = baseSchoolMapper.selectList(new QueryWrapper<>());
        //将所有数据手机号加密脱敏
        for(BaseSchool baseSchool : allList){
            if(StringUtils.isEmpty(baseSchool.getPhone())){
                continue;
            }
            //脱敏
//            String desPhone = com.ows.ufa.common.core.utils.DataTransfer.maskValue(backupTeacher.getTchPhone(), SensitiveType.PHONE);
            //加密
            String encrypt = com.ows.ufa.common.core.utils.DataTransfer.encryptValue(baseSchool.getPhone());
//            backupTeacher.setTchPhone(desPhone);
            baseSchool.setJmSchHeadPhone(encrypt);
            baseSchoolMapper.updateById(baseSchool);
        }
        return "1";
    }
}