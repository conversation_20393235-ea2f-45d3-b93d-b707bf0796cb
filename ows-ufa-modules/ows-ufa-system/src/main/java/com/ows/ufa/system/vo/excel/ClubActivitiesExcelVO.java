package com.ows.ufa.system.vo.excel;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ows.ufa.common.core.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
public class ClubActivitiesExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "社团名称", type = Excel.Type.ALL)
    private String clubInfoName;

    @Excel(name = "活动名称", type = Excel.Type.ALL)
    private String activityName;

    @Excel(name = "活动地点", type = Excel.Type.ALL)
    private String activityLocation;

    @Excel(name = "活动状态", readConverterExp = "1=未开始,2=报名中,3=进行中,4=已结束", type = Excel.Type.ALL)
    private Integer progressStatus;

    @Excel(name = "报名时间", dateFormat = "yyyy-MM-dd HH:mm:ss",type = Excel.Type.ALL)
    private LocalDateTime registrationTime;

    @Excel(name = "截止报名时间",dateFormat = "yyyy-MM-dd HH:mm:ss", type = Excel.Type.ALL)
    private LocalDateTime registrationDeadline;
}