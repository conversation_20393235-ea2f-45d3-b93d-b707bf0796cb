package com.ows.ufa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.domain.vo.StudentStatisticsVo;
import com.ows.ufa.system.entity.ZsPlanStudent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 在籍学员 Mapper 接口
 * </p>
 *
 */
@Mapper
public interface ZsPlanStudentMapper extends BaseMapper<ZsPlanStudent> {

    /**
     * 查询在籍学员总数
     *
     * @return
     */
    public Long getZsPlanStudentTotal(@Param(value = "schoolId") String schoolId,@Param(value = "planId") String planId);

    /**
     * 各院系/全部院系学员数量
     *
     * @return
     */
    public Long getZsPlanStudentNumber(@Param(value = "schoolId") String schoolId,
                                       @Param(value = "planId") String planId,
                                       @Param(value = "deptId") String deptId);


    /**
     * 政治面貌统计
     *
     * @param schoolId
     * @param planId
     * @return
     */
    public List<StudentStatisticsVo> selectStudentPolitical(@Param(value = "schoolId") String schoolId,
                                                            @Param(value = "planId") String planId);


    /**
     * 退休状态统计
     *
     * @param schoolId
     * @param planId
     * @return
     */
    public List<StudentStatisticsVo> selectStudentRetireState(@Param(value = "schoolId") String schoolId,
                                                           @Param(value = "planId") String planId);

    /**
     * 学历统计
     *
     * @param schoolId
     * @param planId
     * @return
     */
    public List<StudentStatisticsVo> selectStudentEduLevel(@Param(value = "schoolId") String schoolId,
                                                              @Param(value = "planId") String planId);

    /**
     * 居住情况统计
     *
     * @param schoolId
     * @param planId
     * @return
     */
    public List<StudentStatisticsVo> selectStudentLiveState(@Param(value = "schoolId") String schoolId,
                                                           @Param(value = "planId") String planId);

    /**
     * 性别统计
     *
     * @param schoolId
     * @param planId
     * @return
     */
    public List<StudentStatisticsVo> selectStudentSex(@Param(value = "schoolId") String schoolId,
                                                            @Param(value = "planId") String planId);

    /**
     * 查询学员身份证号
     *
     * @param schoolId
     * @param planId
     * @return
     */
    public List<StudentStatisticsVo> selectStudentCard(@Param(value = "schoolId") String schoolId,
                                                      @Param(value = "planId") String planId);
}