package com.ows.ufa.system.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.exception.base.BaseException;
import com.ows.ufa.common.redis.service.RedisService;
import com.ows.ufa.system.domain.SysChatHistory;
import com.ows.ufa.system.domain.vo.ImageVO;
import com.ows.ufa.system.entity.AdmissionNotice;
import com.ows.ufa.system.enums.ReadStatus;
import com.ows.ufa.system.enums.Status;
import com.ows.ufa.system.form.AdmissionNoticeForm;
import com.ows.ufa.system.mapper.AdmissionNoticeMapper;
import com.ows.ufa.system.mapper.SysNoticeMapper;
import com.ows.ufa.system.request.AdmissionNoticeRequest;
import com.ows.ufa.system.service.AdmissionNoticeService;
import com.ows.ufa.system.service.ImageService;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.AdmissionNoticeVO;
import com.ows.ufa.system.vo.StudentVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 课程信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class AdmissionNoticeServiceImpl extends ServiceImpl<AdmissionNoticeMapper, AdmissionNotice> implements AdmissionNoticeService {

    private final ImageService imageService;
    @Value("${projectImg}")
    private String projectUrl;
    @Autowired
    private SysNoticeMapper noticeMapper;
    public static final String key=  "MESSAGE-USER-";
    @Autowired
    private RedisService redisService;

    @Override
    public List<AdmissionNotice> queryAdmissionNotices(AdmissionNoticeRequest request) {
        AdmissionNotice entity = (AdmissionNotice) DataTransfer.transfer(request, AdmissionNotice.class);
        LambdaQueryWrapper<AdmissionNotice> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public String findAdmissionNotice(Long id) {
        AdmissionNotice entity = this.getById(id);
        ImageVO image = generateImageVO(entity);
        String base64 = imageService.generateLetterImage(image);
        if (null == base64) {
            throw new ServiceException("生成通知书失败,请联系管理员处理");
        }
        if(ReadStatus.UnRead.getCode().equals(entity.getReadStatus())){
            entity.setReadStatus(ReadStatus.Read.getCode());
            this.updateById(entity);
        }
        return base64;
    }

    private ImageVO generateImageVO(AdmissionNotice entity) {
        ImageVO image = new ImageVO();
        image.setAdmissionNotice(entity);
        String imageUrl = "notice/" + entity.getId() + ".jpg";
        image.setUrl(imageUrl);
        image.setFileUrl(projectUrl + imageUrl);
        return image;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAdmissionNotice(AdmissionNoticeForm vo) {
        AdmissionNotice entity = (AdmissionNotice) DataTransfer.transfer(vo, AdmissionNotice.class);
        entity.setCreateTime(LocalDateTime.now());
        entity.setStatus(Status.PAYMENT.getCode());
        entity.setReadStatus(ReadStatus.UnRead.getCode());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAdmissionNotice(AdmissionNoticeVO vo) {
        AdmissionNotice entity = (AdmissionNotice) DataTransfer.transfer(vo, AdmissionNotice.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAdmissionNotice(Long id) {
        return this.removeById(id);
    }

    @Override
    public Long unreadNotice(AdmissionNoticeRequest request) {
        return this.lambdaQuery().eq(AdmissionNotice::getUserId, request.getUserId())
                .eq(AdmissionNotice::getStatus, Status.PAYMENT.getCode())
                .eq(AdmissionNotice::getReadStatus, ReadStatus.UnRead.getCode()).count();
    }

    @Override
    public List<AdmissionNotice> listNotices(AdmissionNoticeRequest request) {
        return this.lambdaQuery().eq(AdmissionNotice::getUserId, request.getUserId())
                .eq(AdmissionNotice::getStatus, Status.PAYMENT.getCode()).
                orderByDesc(AdmissionNotice::getPayTime).
                list();
    }

    @Override
    public Boolean refundAdmissionNotice(AdmissionNoticeForm vo) {
        AdmissionNotice entity = this.lambdaQuery().eq(AdmissionNotice::getUserId, vo.getUserId()).eq(AdmissionNotice::getOrderNo, vo.getOrderNo()).one();
        if (entity == null) {
            throw new ServiceException("未找到通知书信息");
        }
        return this.lambdaUpdate().eq(AdmissionNotice::getUserId, vo.getUserId())
                .eq(AdmissionNotice::getOrderNo, vo.getOrderNo()).set(AdmissionNotice::getStatus, Status.REFUND.getCode())
                .set(AdmissionNotice::getRefundTime,vo.getRefundTime())
                .set(AdmissionNotice::getUpdateTime,LocalDateTime.now()).update();
    }

    @Override
    public SysChatHistory getChatHistory(SysChatHistory sysChatHistory) {
        //用户缓存key
        String keys=key+sysChatHistory.getUserId();
        //查询缓存数据
        String cacheObject = redisService.getCacheObject(keys);
        //缓存校验
        if (ObjectUtils.isEmpty(cacheObject)){
            //缓存没数据查数据库
            String chatHistory = noticeMapper.getChatHistory(sysChatHistory.getUserId());
            sysChatHistory.setContent(chatHistory);
            //更新到缓存
            redisService.setCacheObject(keys,chatHistory);
        }else {
            sysChatHistory.setContent(cacheObject);//直接拿缓存数据
        }
        //关键字“录取通知书”校验
        if (!ObjectUtils.isEmpty(sysChatHistory.getContent())
                && sysChatHistory.getContent().contains("录取通知书")){
            sysChatHistory.setJugeFlag(1);
        }else {
            sysChatHistory.setJugeFlag(2);
        }
        return sysChatHistory;
    }

    @Override
    public Integer saveChatHistory(SysChatHistory sysChatHistory) {
        //获取用户缓存key
        String keys= key + sysChatHistory.getUserId();
        //查询数据库条数
        Integer chatHistory = noticeMapper.getChatHistoryCount(sysChatHistory.getUserId());
        //判断数据库是有有数据
        if (chatHistory==0){
            noticeMapper.insertChatHistory(sysChatHistory.getUserId(),sysChatHistory.getContent());//无数据插入数据
        }else {
            noticeMapper.saveChatHistory(sysChatHistory.getUserId(), sysChatHistory.getContent());//有数据更新数据
        }
        redisService.setCacheObject(keys,sysChatHistory.getContent());//更新缓存
        return 1;
    }
}