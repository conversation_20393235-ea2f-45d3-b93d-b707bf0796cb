package com.ows.ufa.system.service;

import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.entity.BaseStudent;
import com.ows.ufa.system.request.AbilityLabelUserRequest;
import com.ows.ufa.system.vo.BaseStudentVO;
import com.ows.ufa.system.form.BaseStudentForm;
import com.ows.ufa.system.request.BaseStudentRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.StudentLabelVO;

import java.util.List;
/**
 * <p>
 * 生源学生表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
public interface BaseStudentService extends IService<BaseStudent> {

    TableDataInfo queryBaseStudents(BaseStudentRequest request);

    BaseStudentVO findBaseStudent(String id);

    List<StudentLabelVO> queryStudentLabels(AbilityLabelUserRequest request);

    public String updatePhone();

}
