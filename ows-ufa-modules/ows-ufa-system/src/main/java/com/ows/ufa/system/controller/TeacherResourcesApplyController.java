package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.form.TeacherResourcesApplyForm;
import com.ows.ufa.system.request.TeacherResourcesApplyRequest;
import com.ows.ufa.system.service.TeacherResourcesApplyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 师资资源申请表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "teacherResourcesApply")
@Tag(name = "师资资源申请接口", description = "师资资源申请接口")
public class TeacherResourcesApplyController extends BaseController {

    private final TeacherResourcesApplyService TeacherResourcesApplyServiceImpl;


    @GetMapping("count")
    @Operation(summary = "统计")
    public AjaxResult countTeacherApply() {
        return success(TeacherResourcesApplyServiceImpl.countTeacherApply());
    }

    @GetMapping("list")
    @Operation(summary = "师资资源申请表分页查询")
    public AjaxResult listTeacherResourcesApplyByPage(TeacherResourcesApplyRequest request) {
        startPage();
        return success(getDataTable(TeacherResourcesApplyServiceImpl.queryTeacherResourcesApplys(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "师资资源申请表查询详情")
    public AjaxResult findTeacherResourcesApply(@PathVariable Long id) {
        return success(TeacherResourcesApplyServiceImpl.findTeacherResourcesApply(id));
    }

    @PostMapping
    @Operation(summary = "师资资源申请表新增数据")
    public AjaxResult saveTeacherResourcesApply(@RequestBody TeacherResourcesApplyForm vo) {
        return success(TeacherResourcesApplyServiceImpl.saveTeacherResourcesApply(vo));
    }
/*
    @PostMapping("update")
    @Operation(summary = "师资资源申请表修改数据")
    public AjaxResult updateTeacherResourcesApply(@RequestBody TeacherResourcesApplyVO vo) {
        return success(TeacherResourcesApplyServiceImpl.updateTeacherResourcesApply(vo));
    }

    @PostMapping("delete")
    @Operation(summary = "师资资源申请表删除数据")
    public AjaxResult removeTeacherResourcesApply(@RequestBody IdRequest id) {
        return success(TeacherResourcesApplyServiceImpl.removeTeacherResourcesApply(id.getId()));
    }*/
}
