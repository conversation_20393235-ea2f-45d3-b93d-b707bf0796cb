package com.ows.ufa.system.service;

import com.ows.ufa.system.domain.response.*;
import com.ows.ufa.system.entity.BaseSchoolDept;

import java.util.List;

/**
 * <p>
 *  数据概览实现类
 * </p>
 *
 */
public interface DataOverviewService {

    public DataOverviewHomeNumber homePageNumber();

    public DataOverviewHomePay homePagePay(Integer type);

    public DataOverviewHomeSignUp homePageSignUp();

    public DataOverviewDeptSituation deptSituation(String deptId);

    public List<BaseSchoolDept> dept();

    public DataOverviewDeptSignUp deptSignUp();

    public DataOverviewStudentStatistics student();
}
