package com.ows.ufa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.domain.SysChatHistory;
import com.ows.ufa.system.entity.AdmissionNotice;
import com.ows.ufa.system.form.AdmissionNoticeForm;
import com.ows.ufa.system.request.AdmissionNoticeRequest;
import com.ows.ufa.system.vo.AdmissionNoticeVO;

import java.util.List;
/**
 * <p>
 * 课程信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
public interface AdmissionNoticeService extends IService<AdmissionNotice> {

    List<AdmissionNotice> queryAdmissionNotices(AdmissionNoticeRequest request);

    String findAdmissionNotice(Long id);

    Long saveAdmissionNotice(AdmissionNoticeForm vo);

    boolean updateAdmissionNotice(AdmissionNoticeVO vo);

    boolean removeAdmissionNotice(Long id);

    Long unreadNotice(AdmissionNoticeRequest request);

    List<AdmissionNotice> listNotices(AdmissionNoticeRequest request);

    Boolean refundAdmissionNotice(AdmissionNoticeForm vo);

    /**
     * 获取聊天记录，并更新
     * @param sysChatHistory
     * @return
     */
    public SysChatHistory getChatHistory(SysChatHistory sysChatHistory);

    /**
     * 更新聊天记录
     * @param sysChatHistory
     * @return
     */
    Integer saveChatHistory(SysChatHistory sysChatHistory);
}
