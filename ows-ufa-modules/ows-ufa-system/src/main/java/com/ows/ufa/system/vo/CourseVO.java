package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CourseVO implements Serializable {
    private static final long serialVersionUID = 1L;
    @Schema(description = "学员报名该课程的时间")
    private Date payTime;

    @Schema(description = "学员报名的学校")
    private String schoolName;

    @Schema(description = "学员所在的系别")
    private String deptName;

    @Schema(description = "学员所选的专业")
    private String majorName;

    @Schema(description = "学员报名的具体课程")
    private String courseName;

    @Schema(description = "学员担任的职位名称")
    private String classPost;

    private String campusName;
}

