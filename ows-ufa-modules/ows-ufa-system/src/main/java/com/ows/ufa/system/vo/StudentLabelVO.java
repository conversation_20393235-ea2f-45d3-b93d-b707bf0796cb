package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 生源学生表
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@Schema(description ="生源学生表")
public class StudentLabelVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "学员基础表-主键(UUID)")
    private String id;

    @Schema(description = "姓名")
    private String stuName;

    @Schema(description = "身份证号")
    private String stuCard;

    @Schema(description = "原职务[1经理 2部长 3局长 4处长 5主管 6科长 9其他]")
    private String stuHisJob;

    private String stuBirth;

    private Integer age;

    private List<AbilityLabelUserVO> labels;
}