package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 问卷调查信息填报表
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@Schema(description ="问卷调查信息填报统计")
public class QuestionnaireSubmitOptions implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "选项名称")
    private String name;
    @Schema(description = "统计数量")
    private long total;
    @Schema(description = "比例")
    private int rate;

    public QuestionnaireSubmitOptions(String name) {
        this.name = name;
    }

    public QuestionnaireSubmitOptions() {
    }
}