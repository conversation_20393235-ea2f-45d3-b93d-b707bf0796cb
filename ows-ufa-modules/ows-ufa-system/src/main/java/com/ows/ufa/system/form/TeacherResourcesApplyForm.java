package com.ows.ufa.system.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 师资资源申请表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(description="师资资源申请表Form")
public class TeacherResourcesApplyForm implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "事件标题")
    @NotNull(message = "事件标题不能为空")
    private String title;

    @Schema(description = "事件描述")
    @NotNull(message = "事件描述不能为空")
    private String descp;

    @Schema(description = "行政区编码，不要填")
    private String areaCode;

}