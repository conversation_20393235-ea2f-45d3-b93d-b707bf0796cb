package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.service.QuestionnaireService;
import com.ows.ufa.system.service.QuestionnaireSubmitService;
import com.ows.ufa.system.form.QuestionnaireSubmitForm;
import com.ows.ufa.system.request.QuestionnaireSubmitRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 问卷调查信息填报表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "questionnaireSubmit")
@Tag(name = "questionnaireSubmit", description = "问卷调查信息填报表接口")
public class QuestionnaireSubmitController extends BaseController {

    private final QuestionnaireSubmitService QuestionnaireSubmitServiceImpl;

    @GetMapping("listCount")
    @Operation(summary = "列表统计")
    public AjaxResult listCount() {
        return success(QuestionnaireSubmitServiceImpl.listCount());
    }

    @GetMapping("questionnaireList")
    @Operation(summary = "问卷调查信息分页查询")
    public AjaxResult listSubmitQuestionnaireByPage(QuestionnaireRequest request) {
        startPage();
        return success(getDataTable(QuestionnaireSubmitServiceImpl.queryQuestionnaires(request)));
    }

    @GetMapping("list")
    @Operation(summary = "问卷调查信息填报查询")
    public AjaxResult listQuestionnaireSubmitByPage(QuestionnaireSubmitRequest request) {
        return success(QuestionnaireSubmitServiceImpl.queryQuestionnaireSubmits(request));
    }

    @GetMapping("queryLastSubmit/{id}")
    @Operation(summary = "获取最新问卷调查填报信息")
    public AjaxResult queryLastSubmit(@PathVariable Long id) {
        return success(QuestionnaireSubmitServiceImpl.queryLastSubmit(id));
    }

    @GetMapping("detail/{id}")
    @Operation(summary = "问卷调查信息填报查询详情")
    public AjaxResult findQuestionnaireSubmit(@PathVariable Long id) {
        return success(QuestionnaireSubmitServiceImpl.findQuestionnaireSubmit(id));
    }

    @GetMapping("{id}")
    @Operation(summary = "移动端-问卷调查信息查询详情")
    public AjaxResult findQuestionnaire(@PathVariable Long id) {
        return success(QuestionnaireSubmitServiceImpl.findQuestionnaire(id));
    }

    @PostMapping
    @Operation(summary = "问卷调查信息填报表新增数据")
    public AjaxResult saveQuestionnaireSubmit(@RequestBody QuestionnaireSubmitForm form) {
        return success(QuestionnaireSubmitServiceImpl.saveQuestionnaireSubmit(form));
    }

    @PostMapping("update")
    @Operation(summary = "问卷调查信息填报表修改数据")
    public AjaxResult updateQuestionnaireSubmit(@RequestBody QuestionnaireSubmitForm form) {
        return success(QuestionnaireSubmitServiceImpl.updateQuestionnaireSubmit(form));
    }

    @PostMapping("delete")
    @Operation(summary = "问卷调查信息填报表删除数据")
    public AjaxResult removeQuestionnaireSubmit(@RequestBody IdRequest id) {
        return success(QuestionnaireSubmitServiceImpl.removeQuestionnaireSubmit(id.getId()));
    }
}
