package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 备份师资资源
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Schema(description ="备份师资资源Request")
public class BackupTeacherRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "姓名")
    private String tchName;

    @Schema(description = "家庭住址地区ID")
    private String tchAddrAreaId;

    @Schema(description = "教学类型")
    private String deptName;

    @Schema(description = "师资来源:0-自主新增;1-区县教委")
    private Integer source;
}