package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.BackupTeacher;
import com.ows.ufa.system.vo.BackupTeacherVO;
import com.ows.ufa.system.form.BackupTeacherForm;
import com.ows.ufa.system.request.BackupTeacherRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.count.BackupTeacherCountVO;

import java.util.List;
/**
 * <p>
 * 备份师资资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface BackupTeacherService extends IService<BackupTeacher> {

    List<BackupTeacher> queryBackupTeachers(BackupTeacherRequest request);

    BackupTeacherVO findBackupTeacher(Long id);

    Long saveBackupTeacher(BackupTeacherForm form);

    boolean updateBackupTeacher(BackupTeacherForm form);

    boolean removeBackupTeacher(Long id);

    BackupTeacher hireBackupTeacher(BackupTeacherVO vo);

    boolean updateTeacher(BackupTeacher entity) ;

    BackupTeacherCountVO countBackupTeacher();

    String updatePhone();
}
