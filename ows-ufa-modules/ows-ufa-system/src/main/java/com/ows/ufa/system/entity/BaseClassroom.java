package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 教室表
 *
 */
@Data
@TableName("base_classroom")
@Schema(description ="教室表")
public class BaseClassroom implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "学校id")
    private String schoolId;

}