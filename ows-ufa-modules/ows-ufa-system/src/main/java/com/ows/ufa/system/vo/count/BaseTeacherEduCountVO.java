package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="共享师资库统计VO")
public class BaseTeacherEduCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "学历")
    private String tchEdu;

    @Schema(description = "学历人数")
    private Integer eduCount;

    @Schema(description = "学历人数")
    private Integer eduRatio;
}