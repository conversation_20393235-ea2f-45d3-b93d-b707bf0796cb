package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.log.annotation.Log;
import com.ows.ufa.common.log.enums.BusinessType;
import com.ows.ufa.system.domain.SysChatHistory;
import com.ows.ufa.system.request.AdmissionNoticeRequest;
import com.ows.ufa.system.service.AdmissionNoticeService;
import com.ows.ufa.system.service.ISysNoticeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 通知书信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "admissionNotice")
@Tag(name = "通知书接口", description = "通知书接口")
public class AdmissionNoticeController extends BaseController {

    private final AdmissionNoticeService AdmissionNoticeServiceImpl;
    @Autowired
    private ISysNoticeService noticeService;

    @GetMapping("unreadNotice")
    @Operation(summary = "查询未读通知书")
    public AjaxResult findCourse(AdmissionNoticeRequest request) {
        return success(AdmissionNoticeServiceImpl.unreadNotice(request));
    }

    @GetMapping("listNotices")
    @Operation(summary = "通知书列表")
    public AjaxResult listNotices(AdmissionNoticeRequest request) {
        return success(AdmissionNoticeServiceImpl.listNotices(request));
    }

    @GetMapping("{id}")
    @Operation(summary = "查询通知书")
    public AjaxResult findAdmissionNotice(@PathVariable Long id) {
        return success(AdmissionNoticeServiceImpl.findAdmissionNotice(id));
    }

    /**
     * AI聊天记录
     * @param sysChatHistory
     * @return
     */
    @PostMapping("/getChatHistory")
    @Log(title = "获取聊天记录" , businessType = BusinessType.INSERT)
    public AjaxResult getChatHistory (@Validated @RequestBody SysChatHistory sysChatHistory){
        return success(AdmissionNoticeServiceImpl.getChatHistory(sysChatHistory));
    }

    /**
     * 聊天记录保存
     * @param sysChatHistory
     * @return
     */
    @PostMapping("/saveChatHistory")
    @Log(title = "更新聊天记录" , businessType = BusinessType.INSERT)
    public AjaxResult saveChatHistory (@Validated @RequestBody SysChatHistory sysChatHistory){
        return success(AdmissionNoticeServiceImpl.saveChatHistory(sysChatHistory));
    }
}
