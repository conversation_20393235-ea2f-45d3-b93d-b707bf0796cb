package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 社团成员活动信息表
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@TableName("t_club_member_activity")
@Schema(description ="社团成员活动信息表实体")
public class ClubMemberActivity implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "社团ID")
    private Long clubInfoId;

    @Schema(description = "活动ID")
    private Long clubActivityId;

    @Schema(description = "社团成员id")
    private Long clubMemberId;

    @Schema(description = "申请日期")
    private LocalDateTime applicationDate;

    @Schema(description = "审核日期")
    private LocalDateTime reviewDate;

    @Schema(description = "审核人")
    private String reviewAt;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(description = "删除标志:0-删除;1-有效")
    private Integer delFlag;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "部门id")
    private String deptId;


}