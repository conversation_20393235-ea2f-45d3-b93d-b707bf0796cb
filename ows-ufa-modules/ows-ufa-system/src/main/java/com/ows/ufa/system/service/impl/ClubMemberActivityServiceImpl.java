package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.ClubActivities;
import com.ows.ufa.system.entity.ClubMember;
import com.ows.ufa.system.entity.ClubMemberActivity;
import com.ows.ufa.system.enums.ReviewStatus;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.mapper.ClubActivitiesMapper;
import com.ows.ufa.system.service.ClubActivitiesService;
import com.ows.ufa.system.vo.ClubActivitiesVO;
import com.ows.ufa.system.vo.ClubMemberActivityVO;
import com.ows.ufa.system.form.ClubMemberActivityForm;
import com.ows.ufa.system.request.ClubMemberActivityRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.ClubMemberActivityMapper;
import com.ows.ufa.system.service.ClubMemberActivityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.system.vo.UserVO;
import com.ows.ufa.system.vo.count.ClubReviewCountVO;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.time.LocalDateTime;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 社团成员活动信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ClubMemberActivityServiceImpl extends ServiceImpl<ClubMemberActivityMapper, ClubMemberActivity> implements ClubMemberActivityService {

    private final ClubActivitiesMapper clubActivitiesMapper;

    @Override
    public List<ClubMemberActivityVO> queryClubMemberActivitys(ClubMemberActivityRequest request) {
        request.setAncestors(SecurityUtils.getAncestors());
        return this.baseMapper.queryClubMemberActivitys(request);
    }

    @Override
    public ClubMemberActivityVO findClubMemberActivity(Long id) {
        ClubMemberActivityVO clubMemberActivityVO = this.baseMapper.queryClubMemberActivity(id);
        clubMemberActivityVO.setActivitiesVO(clubActivitiesMapper.findClubActivities(clubMemberActivityVO.getClubActivityId()));
        return clubMemberActivityVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveClubMemberActivity(ClubMemberActivityForm form) {
        ClubMemberActivity entity = (ClubMemberActivity) DataTransfer.transfer(form, ClubMemberActivity.class);
        entity.setCreateTime(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateClubMemberActivity(ClubMemberActivityForm form) {
        ClubMemberActivity entity = (ClubMemberActivity) DataTransfer.transfer(form, ClubMemberActivity.class);
        entity.setUpdateTime(LocalDateTime.now());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeClubMemberActivity(Long id) {
        return this.lambdaUpdate().eq(ClubMemberActivity::getId, id).set(ClubMemberActivity::getDelFlag, DelFlag.DELETE.getCode()).update();
    }

    @Override
    public boolean review(ReviewForm form) {
        if (null != form.getId()) {
            ClubMemberActivity entity = this.getById(form.getId());
            if (null == entity) {
                throw new ServiceException("未找到该社团成员活动申请信息");
            }
            if (!entity.getStatus().equals(ReviewStatus.PENDING.getCode())) {
                throw new ServiceException("该社团成员活动申请已审核，不能重复审核");
            }
            return this.lambdaUpdate().eq(ClubMemberActivity::getId, form.getId()).set(ClubMemberActivity::getStatus, form.getStatus())
                    .set(ClubMemberActivity::getReviewAt, SecurityUtils.getThirdUserid())
                    .set(ClubMemberActivity::getReviewDate, LocalDateTime.now()).update();
        }
        if (null != form.getIds()) {
            return this.lambdaUpdate().in(ClubMemberActivity::getId, form.getIds()).set(ClubMemberActivity::getStatus, form.getStatus())
                    .set(ClubMemberActivity::getReviewAt, SecurityUtils.getThirdUserid())
                    .set(ClubMemberActivity::getReviewDate, LocalDateTime.now()).update();
        }
        return true;
    }

    @Override
    public ClubReviewCountVO countReview() {
        ClubReviewCountVO count = new ClubReviewCountVO();
        List<ClubMemberActivity> list = this.lambdaQuery().select(ClubMemberActivity::getStatus).eq(ClubMemberActivity::getDelFlag, DelFlag.VALID.getCode())
                .in(ClubMemberActivity::getDeptId, SecurityUtils.getAncestors()).list();
        if (!list.isEmpty()) {
            count.setPendingAuditCount(list.stream().filter(item -> item.getStatus().equals(ReviewStatus.PENDING.getCode())).count());
            count.setApprovedAuditCount(list.size() - count.getPendingAuditCount());
        }
        return count;
    }

    @Override
    public Long joinActivity(Long id) {
        Long count = this.lambdaQuery().eq(ClubMemberActivity::getClubActivityId, id)
                .eq(ClubMemberActivity::getClubMemberId, SecurityUtils.getThirdUserid())
                .eq(ClubMemberActivity::getDelFlag, DelFlag.VALID.getCode())
                .ne(ClubMemberActivity::getStatus, ReviewStatus.REJECT.getCode()).count();
        if (count > 0) {
            throw new ServiceException("您已参加该活动，不能重复参加");
        }

        ClubActivities byId = clubActivitiesMapper.selectById(id);
        if (null == byId) {
            throw new ServiceException("未找到该活动信息");
        }
        //校验活动开始结束时间
        if (byId.getRegistrationTime().isAfter(LocalDateTime.now())) {
            throw new ServiceException("该活动未开始报名，不能参加");
        }
        if (byId.getRegistrationDeadline().isBefore(LocalDateTime.now())) {
            throw new ServiceException("该活动已结束报名，不能参加");
        }
        ClubMemberActivity entity = new ClubMemberActivity();
        entity.setClubInfoId(byId.getClubInfoId());
        entity.setClubActivityId(byId.getId());
        entity.setClubMemberId(Long.parseLong(SecurityUtils.getThirdUserid()));
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        entity.setApplicationDate(LocalDateTime.now());
        if(byId.getNeedApproval()==1){
            entity.setStatus(ReviewStatus.PENDING.getCode());
        }else{
            entity.setStatus(ReviewStatus.PASS.getCode());
        }
        entity.setReviewAt(SecurityUtils.getThirdUserid());
        entity.setReviewDate(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    public boolean cancelActivity(Long id) {
        return this.lambdaUpdate().eq(ClubMemberActivity::getClubActivityId, id)
                .eq(ClubMemberActivity::getClubMemberId, SecurityUtils.getThirdUserid()).set(ClubMemberActivity::getDelFlag, DelFlag.DELETE.getCode()).update();
    }

    @Override
    public List<ClubMemberActivityVO> myActivities(ClubMemberActivityRequest request) {
        request.setCreateAt(SecurityUtils.getThirdUserid());
        return this.baseMapper.queryClubMemberActivitys(request);
    }
}