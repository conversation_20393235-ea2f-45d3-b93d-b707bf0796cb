package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.utils.poi.ExcelUtil;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubActivitiesService;
import com.ows.ufa.system.form.ClubActivitiesForm;
import com.ows.ufa.system.request.ClubActivitiesRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.ClubActivitiesVO;
import com.ows.ufa.system.vo.ClubMemberVO;
import com.ows.ufa.system.vo.excel.ClubActivitiesExcelVO;
import com.ows.ufa.system.vo.excel.ClubMemberExcelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "clubActivity")
@Tag(name = "clubActivity", description = "社团活动接口")
public class ClubActivitiesController extends BaseController {

    private final ClubActivitiesService ClubActivitiesServiceImpl;


    @GetMapping("countClubActivity")
    @Operation(summary = "社团活动统计")
    public AjaxResult countClubActivity() {
        return success(ClubActivitiesServiceImpl.countClubActivity());
    }

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubActivitiesByPage(ClubActivitiesRequest request) {
        startPage();
        return success(getDataTable(ClubActivitiesServiceImpl.queryClubActivitiess(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "查询详情")
    public AjaxResult findClubActivities(@PathVariable Long id) {
        return success(ClubActivitiesServiceImpl.findClubActivities(id));
    }

    @PostMapping
    @Operation(summary = "新增数据")
    public AjaxResult saveClubActivities(@RequestBody ClubActivitiesForm form) {
        return success(ClubActivitiesServiceImpl.saveClubActivities(form));
    }

    @PostMapping("update")
    @Operation(summary = "修改数据")
    public AjaxResult updateClubActivities(@RequestBody ClubActivitiesForm form) {
        return success(ClubActivitiesServiceImpl.updateClubActivities(form));
    }

    @PostMapping("delete")
    @Operation(summary = "删除数据")
    public AjaxResult removeClubActivities(@RequestBody IdRequest id) {
        return success(ClubActivitiesServiceImpl.removeClubActivities(id.getId()));
    }

    @PostMapping("/export")
    @Operation(summary = "导出")
    public void export(HttpServletResponse response,@RequestBody  ClubActivitiesRequest request) {
        List<ClubActivitiesVO> list = ClubActivitiesServiceImpl.queryClubActivitiess(request);
        List<ClubActivitiesExcelVO> excelVOList = DataTransfer.transferList(list, ClubActivitiesExcelVO.class);
        ExcelUtil<ClubActivitiesExcelVO> util = new ExcelUtil<>(ClubActivitiesExcelVO.class);
        util.exportExcel(response, excelVOList, "社团活动数据");
    }
}
