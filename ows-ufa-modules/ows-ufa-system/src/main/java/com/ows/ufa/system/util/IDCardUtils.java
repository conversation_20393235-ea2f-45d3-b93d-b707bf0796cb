package com.ows.ufa.system.util;

import com.ows.ufa.common.core.utils.uuid.UUID;
import com.ows.ufa.system.api.factory.RemoteUserFallbackFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class IDCardUtils {

    private static final Logger log = LoggerFactory.getLogger(IDCardUtils.class);

    /**
     * 提取身份证中的出生日期
     *
     * @param idCard 身份证号码字符串
     * @return 出生日期字符串，格式为 yyyy-MM-dd
     * @throws IllegalArgumentException 如果身份证号码格式不正确
     */
    public static String extractBirthDate(String idCard) {
        if (idCard == null || idCard.isEmpty()) {
            log.error("身份证号码不能为空");
            return "";
        }

        String birthDate;

        if (idCard.length() == 18) {
            // 18位身份证号码
            birthDate = idCard.substring(6, 14);
        } else if (idCard.length() == 15) {
            // 15位身份证号码
            birthDate = "19" + idCard.substring(6, 12);
        } else {
            log.error("身份证号码长度不正确: {}", idCard.length());
            return "";
        }

        // 验证日期格式是否正确
        if (!isValidDate(birthDate)) {
            log.error("身份证号码中的出生日期格式不正确: {}", birthDate);
            return "";
        }

        // 格式化为 yyyy-MM-dd
        return birthDate.substring(0, 4) + "." + birthDate.substring(4, 6) + "." + birthDate.substring(6, 8);
    }


    /**
     * 提取身份证中的性别
     *
     * @param idCard 身份证号码字符串
     * @return 性别字符串，"男" 或 "女"
     * @throws IllegalArgumentException 如果身份证号码格式不正确
     */
    public static String extractGender(String idCard) {
        if (idCard == null || idCard.isEmpty()) {
            throw new IllegalArgumentException("身份证号码不能为空");
        }

        int genderIndex;
        if (idCard.length() == 18) {
            genderIndex = 16;
        } else if (idCard.length() == 15) {
            genderIndex = 14;
        } else {
            log.error("身份证号码长度不正确: {}", idCard.length());
            return "2";
        }

        char genderDigit = idCard.charAt(genderIndex);

        if (Character.isDigit(genderDigit)) {
            int genderCode = Character.getNumericValue(genderDigit);
            return genderCode % 2 == 0 ? "0" : "1";
        } else {
            log.error("身份证号码中的性别标识位不是数字: {}", genderDigit);
            return "2";
        }
    }

    /**
     * 验证日期字符串是否符合 yyyyMMdd 格式并且是一个有效的日期
     *
     * @param dateStr 日期字符串
     * @return 如果日期有效则返回true，否则返回false
     */
    private static boolean isValidDate(String dateStr) {
        try {
            java.time.LocalDate.parse(dateStr, java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // 示例用法
    public static void main(String[] args) {
        String idCard18 = "220122198912168174";
        String idCard15 = "110102800101123";

        try {
            UUID uuid = UUID.randomUUID();
            System.out.println("UUID: " + uuid.toString(true));
            String birthDate18 = extractBirthDate(idCard18);
            System.out.println("18位身份证出生日期: " + birthDate18); // 输出: 1980-01-01

            String birthDate15 = extractBirthDate(idCard15);
            System.out.println("15位身份证出生日期: " + birthDate15); // 输出: 1980-01-01
            String gender18 = extractGender(idCard18);
            System.out.println("18位身份证性别: " + gender18);

            String gender15 = extractGender(idCard15);
            System.out.println("15位身份证性别: " + gender15);
        } catch (IllegalArgumentException e) {
            System.err.println(e.getMessage());
        }
    }
}