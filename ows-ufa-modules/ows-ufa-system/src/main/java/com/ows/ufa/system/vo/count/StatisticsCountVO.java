package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="问卷统计VO")
public class StatisticsCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "提交份数")
    private long filledQuantity;

    @Schema(description = "提交人数")
    private long unfilledQuantity;

}