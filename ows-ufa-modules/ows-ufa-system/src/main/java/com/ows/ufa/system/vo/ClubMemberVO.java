package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ows.ufa.common.core.annotation.Sensitive;
import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.system.entity.ClubInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="VO")
public class ClubMemberVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团ID")
    private Long clubInfoId;

    @Schema(description = "社团名称")
    private String clubInfoName;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "性别:0-男;1-女;")
    private Integer sex;

    @Schema(description = "联系电话")
    private String phoneNumber;

    @Schema(description = "加密联系电话")
    private String phoneEncrypt;

    @Schema(description = "申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime applicationDate;

    @Schema(description = "审核日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime reviewDate;

    @Schema(description = "审核人")
    private String reviewAt;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(description = "删除标志:0-删除;1-有效")
    private Integer delFlag;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "数据权限部门id")
    private String deptId;

    @Schema(description = "社团照片URL或文件路径")
    private String clubPhoto;

    @Schema(description = "社团简介")
    private String clubDescp;

    private ClubInfo clubinfo;
}