package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.utils.poi.ExcelUtil;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubInfoService;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.ClubInfoVO;
import com.ows.ufa.system.vo.excel.ClubInfoExcelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "clubInfoReview")
@Tag(name = "clubInfoReview", description = "社团审核接口")
public class ClubInfoReviewController extends BaseController {

    private final ClubInfoService ClubInfoServiceImpl;

    @GetMapping("countReview")
    @Operation(summary = "统计")
    public AjaxResult countClubReview() {
        return success(ClubInfoServiceImpl.countReview());
    }

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubInfoByPage(ClubInfoRequest request) {
        startPage();
        return success(getDataTable(ClubInfoServiceImpl.queryClubInfos(request)));
    }

    @PostMapping("review")
    @Operation(summary = "审核")
    public AjaxResult review(@RequestBody ReviewForm form) {
        return success(ClubInfoServiceImpl.review(form));
    }
}
