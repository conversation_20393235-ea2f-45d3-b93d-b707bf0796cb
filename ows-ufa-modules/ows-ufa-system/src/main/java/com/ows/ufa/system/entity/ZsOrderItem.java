package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 缴费（订单）表
 *
 */
@Data
@TableName("zs_order_item")
@Schema(description ="缴费（订单）表")
public class ZsOrderItem implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "计划id")
    private String planId;

    @Schema(description = "支付状态")
    private Integer payStatus;

    @Schema(description = "是否撤销")
    private Integer isBack;

    @Schema(description = "支付时间")
    private Date payTime;

    @Schema(description = "支付金额")
    private BigDecimal orderAmount;

}