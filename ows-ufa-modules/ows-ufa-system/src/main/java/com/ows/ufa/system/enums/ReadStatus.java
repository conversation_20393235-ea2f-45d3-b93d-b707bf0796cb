package com.ows.ufa.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ReadStatus implements IEnum<Integer> {
    //状态:0-未读;1-已读
    UnRead(0, "未读"),
    Read(1, "已读");
    ReadStatus(Integer code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final Integer code;
    @JsonValue
    private final String descp;

    @Override
    public Integer getValue() {
        return this.code;
    }

}
