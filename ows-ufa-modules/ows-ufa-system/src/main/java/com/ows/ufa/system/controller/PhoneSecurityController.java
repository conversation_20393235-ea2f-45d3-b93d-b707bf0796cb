package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.form.AbilityLabelForm;
import com.ows.ufa.system.request.AbilityLabelRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.AbilityLabelService;
import com.ows.ufa.system.service.PhoneSecurityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 通用手机号加解密 前端控制器
 * </p>
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "phone")
@Tag(name = "phone", description = "通用手机号加解密接口")
public class PhoneSecurityController extends BaseController {

    private final PhoneSecurityService phoneSecurityService;

    @GetMapping("updatePhone")
    @Operation(summary = "更新手机号加密，脱敏")
    public AjaxResult updatePhone() {
        return success(phoneSecurityService.updatePhone());
    }

    @GetMapping("getPhone")
    @Operation(summary = "获取明文手机号")
    public AjaxResult getPhone(@RequestParam(value = "phoneEncrypt") String phoneEncrypt) {
        return success(phoneSecurityService.getPhone(phoneEncrypt));
    }

}
