package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="问卷统计VO")
public class BarChartVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "标题")
    private List<String> title;

    @Schema(description = "提交份数")
    private List<Long> data;

}