package com.ows.ufa.system.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class DataTransfer {
    private static final Logger log = LoggerFactory.getLogger(DataTransfer.class);
    private static final Set<Class<?>> wrapperTypeSet = new HashSet<>();
    public static final String MULTIPLE_DELIM = "&&&";
    public static final String MULTIPLE_DELIM_COMMON = ",";

    static {
        wrapperTypeSet.add(LocalDateTime.class);
        wrapperTypeSet.add(Date.class);
        wrapperTypeSet.add(String.class);
        wrapperTypeSet.add(List.class);
    }

    public static Object transfer(Object source, Class classType) {
        if (null == source) {
            return null;
        }
        Object target = null;
        try {
            target = classType.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            log.error(e.getMessage());
        }
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(target.getClass());
        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
            if (null == writeMethod || null == sourcePd || null == sourcePd.getReadMethod()) {
                continue;
            }
            Method readMethod = sourcePd.getReadMethod();
            Class sourcePropertyClass = readMethod.getReturnType();
            Class targetPropertyClass = writeMethod.getParameterTypes()[0];
            try {
                Object value = readMethod.invoke(source);
                if (ClassUtils.isAssignable(sourcePropertyClass, targetPropertyClass) || null == value) {
                    writeMethod.invoke(target, value);
                } else if (wrapperTypeSet.contains(sourcePropertyClass)) {
                    extracted(target, writeMethod, sourcePropertyClass, targetPropertyClass, value);
                }
            } catch (Throwable ex) {
                throw new FatalBeanException(
                        "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
            }
        }
        return target;
    }

    private static void extracted(Object target, Method writeMethod, Class<?> sourcePropertyClass, Class<?> targetPropertyClass, Object value)
            throws IllegalAccessException, InvocationTargetException {

        if (sourcePropertyClass.equals(String.class)) {
            String sourceString = (String) value;

            if (targetPropertyClass.equals(List.class)) {
                Type[] genericParameterTypes = writeMethod.getGenericParameterTypes();
                if (genericParameterTypes.length > 0 && genericParameterTypes[0] instanceof ParameterizedType) {
                    ParameterizedType listType = (ParameterizedType) genericParameterTypes[0];
                    Type[] actualTypeArguments = listType.getActualTypeArguments();

                    if (actualTypeArguments.length == 1) {
                        Class<?> listElementType = (Class<?>) actualTypeArguments[0];

                        if (listElementType.equals(Long.class)) {
                            String splitStr = sourceString.contains(MULTIPLE_DELIM) ? MULTIPLE_DELIM : ",";
                            if (null == StringUtils.delimitedListToStringArray(sourceString, splitStr)) {
                                List<Long> longList = Arrays.asList(Long.parseLong(sourceString));
                                writeMethod.invoke(target, longList);
                                return;
                            }
                            List<Long> longList = Arrays.stream(StringUtils.delimitedListToStringArray(sourceString, splitStr))
                                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                                    .map(s -> {
                                        try {
                                            return Long.parseLong(s);
                                        } catch (NumberFormatException e) {
                                            throw new IllegalArgumentException("无法将字符串转换为 Long: " + s, e);
                                        }
                                    })
                                    .collect(Collectors.toList());
                            writeMethod.invoke(target, longList);
                            return;
                        } else if (listElementType.equals(String.class)) {
                            String splitStr = sourceString.contains(MULTIPLE_DELIM) ? MULTIPLE_DELIM : ",";
                            if (null == StringUtils.delimitedListToStringArray(sourceString, splitStr)) {
                                List<String> stringList = Arrays.asList(sourceString);
                                writeMethod.invoke(target, stringList);
                                return;
                            }
                            List<String> stringList = Arrays.stream(StringUtils.delimitedListToStringArray(sourceString, splitStr))
                                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            writeMethod.invoke(target, stringList);
                            return;
                        } else {
                            throw new IllegalArgumentException("Unsupported List element type: " + listElementType);
                        }
                    }
                }
                throw new IllegalArgumentException("Unsupported target type: " + targetPropertyClass);
            } else if (targetPropertyClass.equals(LocalDateTime.class)) {
                LocalDateTime dateTime = LocalDateTime.parse(sourceString);
                writeMethod.invoke(target, dateTime);
            }
        } else if (sourcePropertyClass.equals(List.class)) {
            List<?> list = (List<?>) value;
            String targetString;

            if (list == null || list.isEmpty()) {
                targetString = "";
            } else {
                targetString = org.apache.commons.lang3.StringUtils.join(list, MULTIPLE_DELIM);
            }
            writeMethod.invoke(target, targetString);
        } else if (sourcePropertyClass.equals(LocalDateTime.class)) {
            LocalDateTime dateTime = (LocalDateTime) value;
            String targetString = dateTime.toString();
            writeMethod.invoke(target, targetString);
        } else if (sourcePropertyClass.equals(Date.class)) {
            if (targetPropertyClass.equals(LocalDateTime.class)) {
                LocalDateTime dateTime = ((Date) value).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                writeMethod.invoke(target, dateTime);
            }
        } else if (sourcePropertyClass.equals(LocalDateTime.class)) {
            if (targetPropertyClass.equals(Date.class)) {
                Date date = Date.from(((LocalDateTime) value).atZone(ZoneId.systemDefault()).toInstant());
                writeMethod.invoke(target, date);
            }
        }
    }

    public static List transferList(List sources, Class classType) {
        if (null == sources) {
            return null;
        }
        List targetList = new ArrayList();
        for (Object source : sources) {
            Object target = transfer(source, classType);
            targetList.add(target);
        }
        return targetList;
    }

    public static List transferStringToList(String source) {
        String splitStr = MULTIPLE_DELIM_COMMON;
        if (source.contains(MULTIPLE_DELIM)) {
            splitStr = MULTIPLE_DELIM;
        }
        List list = Arrays.asList(StringUtils.delimitedListToStringArray(source, splitStr));
        return list;
    }
    public static String transferListToString(List<String> source, String splitStr) {
        if(null == splitStr){
            splitStr = MULTIPLE_DELIM_COMMON;
        }
        return StringUtils.collectionToDelimitedString(source, splitStr);
    }
}