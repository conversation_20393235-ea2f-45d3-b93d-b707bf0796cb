package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.SensitiveUtil;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.BackupTeacher;
import com.ows.ufa.system.entity.ClubActivities;
import com.ows.ufa.system.entity.ClubInfo;
import com.ows.ufa.system.entity.ClubMember;
import com.ows.ufa.system.enums.DelFlag;
import com.ows.ufa.system.enums.ReviewStatus;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubActivitiesService;
import com.ows.ufa.system.service.ClubMemberService;
import com.ows.ufa.system.vo.ClubInfoVO;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.ClubInfoMapper;
import com.ows.ufa.system.service.ClubInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.system.vo.count.ClubInfoCountVO;
import com.ows.ufa.system.vo.count.ClubReviewCountVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.time.LocalDateTime;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ClubInfoServiceImpl extends ServiceImpl<ClubInfoMapper, ClubInfo> implements ClubInfoService {

    private final ClubActivitiesService clubActivitiesService;
    private final ClubMemberService clubMemberService;

    @Override
    public List<ClubInfoVO> queryClubInfos(ClubInfoRequest req) {
       /* LambdaQueryWrapper<ClubInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ClubInfo::getDelFlag, DelFlag.VALID.getCode());
        queryWrapper.like(StringUtils.isNotBlank(req.getClubName()), ClubInfo::getClubName, req.getClubName());
        queryWrapper.like(StringUtils.isNotBlank(req.getClubDescp()), ClubInfo::getClubName, req.getClubName());
        //成立日期
        if (null != req.getBeginTime() && null != req.getEndTime()) {
            queryWrapper.between(null != req.getBeginTime(), ClubInfo::getCreateTime,
                    DateUtils.startOfDay(req.getBeginTime()), DateUtils.endOfDay(req.getEndTime()));
        }
        queryWrapper.orderByDesc(ClubInfo::getCreateTime);
        return this.list(queryWrapper);*/
        req.setAncestors(SecurityUtils.getAncestors());
        return this.baseMapper.queryClubInfos(req);
    }

    @Override
    public ClubInfoVO findClubInfo(Long id,Integer type) {
        ClubInfoVO vo = this.baseMapper.findClubInfo(id);
        if(type == 1){
            vo.setPhoneNumber(SensitiveUtil.decrypt(vo.getPhoneEncrypt()));
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveClubInfo(ClubInfoForm form) {
        ClubInfo entity = (ClubInfo) com.ows.ufa.common.core.utils.DataTransfer.transfer(form, ClubInfo.class);
        // 增加判断 名称不能重复
        if (this.lambdaQuery().eq(ClubInfo::getClubName, entity.getClubName())
                .eq(ClubInfo::getDelFlag, DelFlag.VALID.getCode()).count() > 0) {
            throw new ServiceException("社团名称已存在");
        }
        entity.setApplicationDate(LocalDateTime.now());
        entity.setCreateTime(LocalDateTime.now());
        String loginUserId = SecurityUtils.getThirdUserid();
        String deptId = SecurityUtils.getThirdDeptId();
        entity.setCreateAt(loginUserId);
        entity.setDeptId(deptId);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(loginUserId);
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateClubInfo(ClubInfoForm form) {
        ClubInfo entity = (ClubInfo) com.ows.ufa.common.core.utils.DataTransfer.transfer(form, ClubInfo.class);
        // 增加判断 名称不能重复
        if (this.lambdaQuery().eq(ClubInfo::getClubName, entity.getClubName()).ne(ClubInfo::getId, form.getId())
                .eq(ClubInfo::getDelFlag, DelFlag.VALID.getCode()).count() > 0) {
            throw new ServiceException("社团名称已存在");
        }
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(SecurityUtils.getThirdUserid());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeClubInfo(IdRequest id) {
        if (null != id.getId()) {
            clubMemberService.lambdaUpdate().eq(ClubMember::getClubInfoId,id.getId()).set(ClubMember::getDelFlag,DelFlag.DELETE.getCode()).update();
            return this.lambdaUpdate().eq(ClubInfo::getId, id.getId()).set(ClubInfo::getDelFlag, DelFlag.DELETE.getCode()).update();
        }
        if (null != id.getIds()) {
            clubMemberService.lambdaUpdate().in(ClubMember::getClubInfoId,id.getIds()).set(ClubMember::getDelFlag,DelFlag.DELETE.getCode()).update();
            return this.lambdaUpdate().in(ClubInfo::getId, id.getIds()).set(ClubInfo::getDelFlag, DelFlag.DELETE.getCode()).update();
        }
        return false;
    }

    @Override
    public ClubInfoCountVO countClub() {
        ClubInfoCountVO count = new ClubInfoCountVO();
        Long clubCount = this.lambdaQuery().eq(ClubInfo::getDelFlag, DelFlag.VALID.getCode()).eq(ClubInfo::getStatus, ReviewStatus.PASS.getCode())
                .in(ClubInfo::getDeptId, SecurityUtils.getAncestors()).count();
        Long activityCount = clubActivitiesService.lambdaQuery().eq(ClubActivities::getDelFlag, DelFlag.VALID.getCode()).eq(ClubActivities::getStatus, ReviewStatus.PASS.getCode())
                .in(ClubActivities::getDeptId, SecurityUtils.getAncestors()).count();
        Long memberCount = clubMemberService.lambdaQuery().eq(ClubMember::getDelFlag, DelFlag.VALID.getCode()).eq(ClubMember::getStatus, ReviewStatus.PASS.getCode())
                .in(ClubMember::getDeptId, SecurityUtils.getAncestors()).count();
        count.setClubCount(clubCount);
        count.setActivityCount(activityCount);
        count.setMemberCount(memberCount);
        return count;
    }

    @Override
    public boolean recruitment(ClubInfoForm form) {
        ClubInfo entity = this.getById(form.getId());
        if (null == entity) {
            throw new ServiceException("未找到该社团信息");
        }
        if (!entity.getStatus().equals(ReviewStatus.PASS.getCode())) {
            throw new ServiceException("该社团未审核通过，不能发起招募");
        }
        //还在招募期，不能点击
        if (!(null == entity.getRecruitmentEndDate() || (null != entity.getRecruitmentEndDate() && entity.getRecruitmentEndDate().isBefore(LocalDateTime.now())))) {
            throw new ServiceException("该社团正在招募中，不能重复发起招募");
        }
        return this.lambdaUpdate().eq(ClubInfo::getId, form.getId()).set(ClubInfo::getRecruitmentStartDate, form.getRecruitmentStartDate())
                .set(ClubInfo::getRecruitmentEndDate, form.getRecruitmentEndDate()).update();
    }

    @Override
    public ClubReviewCountVO countReview() {
        ClubReviewCountVO count = new ClubReviewCountVO();
        List<ClubInfo> list = this.lambdaQuery().select(ClubInfo::getStatus).eq(ClubInfo::getDelFlag, DelFlag.VALID.getCode())
                .in(ClubInfo::getDeptId, SecurityUtils.getAncestors()).list();
        if (!list.isEmpty()) {
            count.setPendingAuditCount(list.stream().filter(item -> item.getStatus().equals(ReviewStatus.PENDING.getCode())).count());
            count.setApprovedAuditCount(list.size() - count.getPendingAuditCount());
        }
        return count;
    }

    @Override
    public boolean review(ReviewForm form) {
        if (null != form.getId()) {
            ClubInfo entity = this.getById(form.getId());
            if (null == entity) {
                throw new ServiceException("未找到该社团信息");
            }
            if (!entity.getStatus().equals(ReviewStatus.PENDING.getCode())) {
                throw new ServiceException("该社团已审核，不能重复审核");
            }
            return this.lambdaUpdate().eq(ClubInfo::getId, form.getId()).set(ClubInfo::getStatus, form.getStatus())
                    .set(ClubInfo::getReviewAt, SecurityUtils.getThirdUserid())
                    .set(ClubInfo::getReviewDate, LocalDateTime.now()).update();
        }
        if (null != form.getIds()) {
            return this.lambdaUpdate().in(ClubInfo::getId, form.getIds()).set(ClubInfo::getStatus, form.getStatus())
                    .set(ClubInfo::getReviewAt, SecurityUtils.getThirdUserid())
                    .set(ClubInfo::getReviewDate, LocalDateTime.now()).update();
        }
        return true;
    }

    @Override
    public List<ClubInfo> queryAll() {
        return this.lambdaQuery().select(ClubInfo::getId, ClubInfo::getClubName).eq(ClubInfo::getDelFlag, DelFlag.VALID.getCode())
                .eq(ClubInfo::getStatus, ReviewStatus.PASS.getCode()).le(ClubInfo::getRecruitmentStartDate,LocalDateTime.now())
                .ge(ClubInfo::getRecruitmentEndDate,LocalDateTime.now())
                .in(ClubInfo::getDeptId, SecurityUtils.getAncestors()).list();
    }

    @Override
    public List<ClubInfo> queryReviewAll() {
        return this.lambdaQuery().select(ClubInfo::getId, ClubInfo::getClubName).eq(ClubInfo::getDelFlag, DelFlag.VALID.getCode())
                .eq(ClubInfo::getStatus, ReviewStatus.PASS.getCode())
                .in(ClubInfo::getDeptId, SecurityUtils.getAncestors()).list();
    }

    @Override
    public List<ClubInfoVO> h5queryClubInfos(ClubInfoRequest req) {
        req.setAncestors(SecurityUtils.getAncestors());
        return this.baseMapper.h5queryClubInfos(req);
    }

    @Override
    public ClubInfoVO h5findClubInfo(Long id) {
        ClubInfo clubInfo=this.getById(id);
        ClubInfoVO vo = (ClubInfoVO) DataTransfer.transfer(clubInfo, ClubInfoVO.class);
        List<ClubMember> list = clubMemberService.lambdaQuery()
                .eq(ClubMember::getClubInfoId, id)
                .eq(ClubMember::getCreateAt, SecurityUtils.getThirdUserid())
                .eq(ClubMember::getDelFlag, DelFlag.VALID.getCode())
                .orderByDesc(ClubMember::getCreateTime).list();
        if(!list.isEmpty()){
            vo.setClubMember(list.get(0));
        }
        return vo;
    }

    @Override
    public String updatePhone(){
        LambdaQueryWrapper<ClubInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ClubInfo::getId, ClubInfo::getPhoneNumber);
        List<ClubInfo> allList = this.list(queryWrapper);
        //将所有数据手机号加密脱敏
        for(ClubInfo clubInfo : allList){
            if(StringUtils.isEmpty(clubInfo.getPhoneNumber()) || clubInfo.getPhoneNumber().indexOf("****") != -1){
                continue;
            }
            //脱敏
            String desPhone = com.ows.ufa.common.core.utils.DataTransfer.maskValue(clubInfo.getPhoneNumber(), SensitiveType.PHONE);
            //加密
            String encrypt = com.ows.ufa.common.core.utils.DataTransfer.encryptValue(clubInfo.getPhoneNumber());
            clubInfo.setPhoneNumber(desPhone);
            clubInfo.setPhoneEncrypt(encrypt);
            this.updateById(clubInfo);
        }
        return "1";
    }
}