package com.ows.ufa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.entity.BaseTeacher;
import com.ows.ufa.system.request.BaseTeacherRequest;
import com.ows.ufa.system.vo.BaseTeacherVO;
import com.ows.ufa.system.vo.count.BaseTeacherCountVO;

import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface BaseTeacherService extends IService<BaseTeacher> {

    List<BaseTeacherVO> querySchoolTeachers(BaseTeacherRequest request);

    String hasTeacher(String idNo);

    String saveBaseTeacher(BaseTeacher entity);

    BaseTeacherCountVO countBaseTeacher();

    public String updatePhone();
}
