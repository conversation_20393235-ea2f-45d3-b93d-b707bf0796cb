package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.entity.BaseSchoolTeacher;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.mapper.BaseSchoolTeacherMapper;
import com.ows.ufa.system.service.BaseSchoolTeacherService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class BaseSchoolTeacherServiceImpl extends ServiceImpl<BaseSchoolTeacherMapper, BaseSchoolTeacher> implements BaseSchoolTeacherService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    @DataSource(value = DataSourceType.SLAVE)
    public boolean saveBaseSchoolTeacher(BaseSchoolTeacher entity) {
        return this.save(entity);
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public boolean isHire(String teacherId,String schoolId, String deptId) {
        return this.lambdaQuery().eq(BaseSchoolTeacher::getTchId, teacherId)
                .eq(BaseSchoolTeacher::getSchoolId, schoolId).count() > 0;
    }
}