package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 在籍学员表
 *
 */
@Data
@TableName("zs_plan_student")
@Schema(description ="在籍学员表")
public class ZsPlanStudent implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "学员id")
    private String stuId;

    @Schema(description = "课程id")
    private String classId;

    @Schema(description = "是否支付")
    private String isPay;

    @Schema(description = "类型")
    private String changeType;

    private String isFlag;

}