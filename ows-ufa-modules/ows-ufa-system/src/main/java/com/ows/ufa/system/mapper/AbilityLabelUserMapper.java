package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.AbilityLabelUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.vo.AbilityLabelUserVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 标签人员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Mapper
public interface AbilityLabelUserMapper extends BaseMapper<AbilityLabelUser> {

    List<AbilityLabelUserVO> queryStudentLabels(@Param("userIds") List<String> userIds);
}