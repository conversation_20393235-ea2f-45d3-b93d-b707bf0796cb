package com.ows.ufa.system.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 在籍学员政治面貌统计Vo
 *
 */
@Data
public class StudentStatisticsVo {

    public StudentStatisticsVo(){}

    public StudentStatisticsVo(String data,Long num){
        this.data = data;
        this.num = num;
    }

    // 政治面貌[1党员 2团员 3民主党派 4无党派 5群众]
    private String data;

    //数量
    private Long num;

    //占比
    private String percentage;

    //其他数据（前端使用）
    private Long other;
}
