package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 生源学生表
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@TableName("base_student")
@Schema(description ="生源学生表实体")
public class BaseStudent implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "学员基础表-主键(UUID)")
    private String id;

    @Schema(description = "姓名")
    private String stuName;

    @Schema(description = "性别[1男 2女]通过身份证获取")
    private String stuSex;

    @Schema(description = "身份证号")
    private String stuCard;

    @Schema(description = "政治面貌[1党员 2团员 3民主党派 4无党派 5群众]")
    private String stuPolitical;

    @Schema(description = "联系电话(脱敏)")
    @TableField("tm_stu_phone")
    private String stuPhone;

    @Schema(description = "联系电话(明文)")
    @TableField("stu_phone")
    private String phone;

    @Schema(description = "加密联系电话")
    private String jmStuPhone;

    @Schema(description = "现居住地址")
    private String stuAddr;

    @Schema(description = "健康状态能否坚持学校[0不能 1能]")
    private String stuHealthState;

    @Schema(description = "原工作单位")
    private String stuHisCompany;

    @Schema(description = "原岗位[1销售 2会计 3职员  9其他]")
    private String stuHisPost;

    @Schema(description = "原职务[1经理 2部长 3局长 4处长 5主管 6科长 9其他]")
    private String stuHisJob;

    @Schema(description = "原职称[1初级 2中级 3高级]")
    private String stuHisTitle;

    @Schema(description = "居住情况[1与配偶居住 2与子女居住 3独居 9其他]")
    private String stuLiveState;

    @Schema(description = "文化程度[1初中以下 2初中至高中 3大专及以上]")
    private String stuEduLevel;

    @Schema(description = "离退状态[1离休 2机关单位退休 3事业单位退休 4企业退休 5个体]")
    private String stuRetireState;

    @Schema(description = "家人是否同意[0不同意 1同意]")
    private String isAgree;

    @Schema(description = "是否上过老年大学[0否 1是]")
    private String isHaveElderCollege;

    @Schema(description = "是否有既往病史[0无 1有]")
    private String isHaveMedical;

    @Schema(description = "是否购买人身意外保险[0否 1是]")
    private String isBuyInsure;

    @Schema(description = "是否红名单[0否 1是]")
    private String isRed;

    @Schema(description = "既往病史说明")
    private String medicalDesc;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "状态[1正常]")
    private String status;

    private String stuCardType;

    private String stuBirth;


}