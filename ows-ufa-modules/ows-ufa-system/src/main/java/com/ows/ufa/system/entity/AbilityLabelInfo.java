package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 标签信息表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@TableName("t_ability_label_info")
@Schema(description ="标签信息表实体")
public class AbilityLabelInfo implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "能力标签ID")
    private Long abilityId;

    @Schema(description = "标签名")
    private String name;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


}