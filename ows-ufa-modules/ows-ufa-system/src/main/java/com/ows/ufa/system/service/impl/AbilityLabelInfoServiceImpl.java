package com.ows.ufa.system.service.impl;

import com.ows.ufa.system.entity.AbilityLabelInfo;
import com.ows.ufa.system.vo.AbilityLabelInfoVO;
import com.ows.ufa.system.form.AbilityLabelInfoForm;
import com.ows.ufa.system.request.AbilityLabelInfoRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.AbilityLabelInfoMapper;
import com.ows.ufa.system.service.AbilityLabelInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.time.LocalDateTime;
import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 标签信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class AbilityLabelInfoServiceImpl extends ServiceImpl<AbilityLabelInfoMapper, AbilityLabelInfo> implements AbilityLabelInfoService {

    @Override
    public List<AbilityLabelInfo> queryAbilityLabelInfos(AbilityLabelInfoRequest request) {
        AbilityLabelInfo entity = (AbilityLabelInfo) DataTransfer.transfer(request, AbilityLabelInfo.class);
        LambdaQueryWrapper<AbilityLabelInfo> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public AbilityLabelInfoVO findAbilityLabelInfo(Long id) {
        AbilityLabelInfo entity = this.getById(id);
        AbilityLabelInfoVO vo = (AbilityLabelInfoVO) DataTransfer.transfer(entity, AbilityLabelInfoVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveAbilityLabelInfo(AbilityLabelInfoForm form) {
        AbilityLabelInfo entity = (AbilityLabelInfo) DataTransfer.transfer(form, AbilityLabelInfo.class);
        entity.setCreateTime(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateAbilityLabelInfo(AbilityLabelInfoForm form) {
        AbilityLabelInfo entity = (AbilityLabelInfo) DataTransfer.transfer(form, AbilityLabelInfo.class);
        entity.setUpdateTime(LocalDateTime.now());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeAbilityLabelInfo(Long id) {
        return this.lambdaUpdate().eq(AbilityLabelInfo::getId,id).remove();
    }
}