package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.redis.service.RedisService;
import com.ows.ufa.system.entity.Area;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.AreaMapper;
import com.ows.ufa.system.service.AreaService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class AreaServiceImpl extends ServiceImpl<AreaMapper, Area> implements AreaService {

    private final RedisService redisService;

    @Override
    public List<Area> queryAreas() {
        String key = "teacher_area";
        if (redisService.hasKey(key)) {
            return redisService.getCacheList(key);
        }
        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        List<Area> list = this.list(queryWrapper);
        redisService.setCacheList(key, list);
        return list;
    }

    @Override
    public String getAreaName(String idStr) {
        if (idStr == null || idStr.isEmpty()) {
            return "";
        }
        List<Area> areas = queryAreas();
        StringBuilder result = new StringBuilder();
        String[] ids = idStr.split(",");
        for (String id : ids) {
            for (Area area : areas) {
                if ((area.getId()+"").equals(id)) {
                    result.append(area.getName());
                    break;
                }
            }
        }
        return result.toString();
    }
}