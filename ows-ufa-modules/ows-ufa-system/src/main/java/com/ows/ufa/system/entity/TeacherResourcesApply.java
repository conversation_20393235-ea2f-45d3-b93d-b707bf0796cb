package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 师资资源申请表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@TableName("t_teacher_resources_apply")
@Schema(description="师资资源申请表实体")
public class TeacherResourcesApply implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "申请用户ID")
    private Long userId;

    @Schema(description = "事件标题")
    private String title;

    @Schema(description = "事件描述")
    private String descp;

    @Schema(description = "状态:0-待处理;1-已处理")
    private Integer status;

    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description = "用户学校名称")
    private String schoolName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "事件单ID")
    private String eventNum;

    @Schema(description = "数据权限部门id")
    private String deptId;
}