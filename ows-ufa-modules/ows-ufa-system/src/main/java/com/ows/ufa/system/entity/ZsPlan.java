package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 招生计划表
 *
 */
@Data
@TableName("zs_plan")
@Schema(description ="招生计划表")
public class ZsPlan implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "学院id")
    private String schoolId;

    private String id;

}