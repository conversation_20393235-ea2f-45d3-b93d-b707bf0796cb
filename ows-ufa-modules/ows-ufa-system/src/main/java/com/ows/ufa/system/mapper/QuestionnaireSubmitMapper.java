package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.QuestionnaireSubmit;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.vo.count.StatisticsTrendsCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 问卷调查信息填报表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Mapper
public interface QuestionnaireSubmitMapper extends BaseMapper<QuestionnaireSubmit> {

    long countFilledQuantity(@Param("req") QuestionnaireRequest request);

    long countUnfilledQuantity(@Param("questionnaireId") Long questionnaireId);

    List<StatisticsTrendsCountVO> submitTrendsCount(@Param("questionnaireId") Long questionnaireId);
}