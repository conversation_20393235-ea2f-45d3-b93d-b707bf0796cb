package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.ClubInfo;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.vo.ClubInfoVO;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.count.ClubInfoCountVO;
import com.ows.ufa.system.vo.count.ClubReviewCountVO;

import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
public interface ClubInfoService extends IService<ClubInfo> {

    List<ClubInfoVO> queryClubInfos(ClubInfoRequest request);

    ClubInfoVO findClubInfo(Long id,Integer type);

    Long saveClubInfo(ClubInfoForm form);

    boolean updateClubInfo(ClubInfoForm form);

    boolean removeClubInfo(IdRequest id);

    ClubInfoCountVO countClub();

    boolean recruitment(ClubInfoForm form);

    ClubReviewCountVO countReview();

    boolean review(ReviewForm form);

    List<ClubInfo> queryAll();

    List<ClubInfo> queryReviewAll();

    List<ClubInfoVO> h5queryClubInfos(ClubInfoRequest request);

    ClubInfoVO h5findClubInfo(Long id);

    public String updatePhone();
}
