package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.utils.poi.ExcelUtil;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.ClubMemberActivityRequest;
import com.ows.ufa.system.service.ClubMemberActivityService;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.ClubMemberActivityVO;
import com.ows.ufa.system.vo.excel.ClubMemberActivityExcelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 * 社团成员活动信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "clubActivityReview")
@Tag(name = "clubActivityReview", description = "社团成员活动申请审核接口")
public class ClubMemberActivityController extends BaseController {

    private final ClubMemberActivityService ClubMemberActivityServiceImpl;

    @GetMapping("countReview")
    @Operation(summary = "社团成员活动申请统计")
    public AjaxResult countReview() {
        return success(ClubMemberActivityServiceImpl.countReview());
    }

    @GetMapping("list")
    @Operation(summary = "社团成员活动信息表分页查询")
    public AjaxResult listClubMemberActivityByPage(ClubMemberActivityRequest request) {
        startPage();
        return success(getDataTable(ClubMemberActivityServiceImpl.queryClubMemberActivitys(request)));
    }

    @PostMapping("review")
    @Operation(summary = "审核")
    public AjaxResult review(@RequestBody ReviewForm form) {
        return success(ClubMemberActivityServiceImpl.review(form));
    }

    @PostMapping("/export")
    @Operation(summary = "社团成员活动申请数据导出")
    public void export(HttpServletResponse response,@RequestBody ClubMemberActivityRequest request) {
        List<ClubMemberActivityVO> list = ClubMemberActivityServiceImpl.queryClubMemberActivitys(request);
        List<ClubMemberActivityExcelVO> excelVOList = DataTransfer.transferList(list, ClubMemberActivityExcelVO.class);
        ExcelUtil<ClubMemberActivityExcelVO> util = new ExcelUtil<>(ClubMemberActivityExcelVO.class);
        util.exportExcel(response, excelVOList, "社团成员活动申请数据");
    }
}
