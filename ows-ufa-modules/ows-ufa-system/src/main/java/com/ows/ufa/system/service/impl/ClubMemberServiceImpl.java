package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.DataTransfer;
import com.ows.ufa.common.core.utils.SensitiveUtil;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.ClubInfo;
import com.ows.ufa.system.entity.ClubMember;
import com.ows.ufa.system.enums.ReviewStatus;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.mapper.ClubInfoMapper;
import com.ows.ufa.system.util.DataUtil;
import com.ows.ufa.system.vo.ClubMemberVO;
import com.ows.ufa.system.form.ClubMemberForm;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.mapper.ClubMemberMapper;
import com.ows.ufa.system.service.ClubMemberService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.system.vo.UserVO;
import com.ows.ufa.system.vo.count.ClubMemberCountVO;
import com.ows.ufa.system.vo.count.ClubReviewCountVO;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.time.LocalDateTime;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ClubMemberServiceImpl extends ServiceImpl<ClubMemberMapper, ClubMember> implements ClubMemberService {

    private final ClubInfoMapper clubInfoMapper;

    @Override
    public List<ClubMemberVO> queryClubMembers(ClubMemberRequest request) {
        request.setStatus(ReviewStatus.PASS.getCode());
        request.setAncestors(SecurityUtils.getAncestors());
        if(StringUtils.isNotEmpty(request.getPhoneNumber()) && request.getPhoneNumber().length() == 11){
            request.setPhoneNumber(SensitiveUtil.encrypt(request.getPhoneNumber()));
        }else{
            request.setPhoneNumber(null);
        }
        return this.baseMapper.queryClubMembers(request);
    }

    @Override
    public List<ClubMemberVO> queryClubMemberReviews(ClubMemberRequest request) {
        request.setAncestors(SecurityUtils.getAncestors());
        return this.baseMapper.queryClubMembers(request);
    }

    @Override
    public ClubMemberVO findClubMember(Long id) {
        ClubMemberVO memberVO= this.baseMapper.findClubMember(id);
        memberVO.setClubinfo(clubInfoMapper.selectById(memberVO.getClubInfoId()));
        memberVO.setPhoneNumber(SensitiveUtil.decrypt(memberVO.getPhoneEncrypt()));
        return memberVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveClubMember(ClubMemberForm form) {
        ClubMember entity = (ClubMember) DataTransfer.transfer(form, ClubMember.class);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        entity.setApplicationDate(LocalDateTime.now());
        entity.setStatus(ReviewStatus.PASS.getCode());
        entity.setReviewAt(SecurityUtils.getThirdUserid());
        entity.setReviewDate(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateClubMember(ClubMemberForm form) {
        ClubMember entity = (ClubMember) DataTransfer.transfer(form, ClubMember.class);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(SecurityUtils.getThirdUserid());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeClubMember(Long id) {
        return this.lambdaUpdate().eq(ClubMember::getId, id).set(ClubMember::getDelFlag, DelFlag.DELETE.getCode()).update();
    }

    @Override
    public ClubMemberCountVO countClubMember() {
        ClubMemberCountVO count = new ClubMemberCountVO();
        List<ClubMember> list = this.lambdaQuery().select(ClubMember::getSex).eq(ClubMember::getDelFlag, DelFlag.VALID.getCode())
                .eq(ClubMember::getStatus, ReviewStatus.PASS.getCode()).in(ClubMember::getDeptId, SecurityUtils.getAncestors()).list();
        if (!list.isEmpty()) {
            Integer memberCount = list.size();
            count.setMemberCount(memberCount);
            long male = list.stream().filter(item -> item.getSex() == 0).count();
//            count.setMaleRatio(DataUtil.rateToDouble(male, memberCount));
//            count.setFemaleRatio(100-count.getMaleRatio());

            // 计算男性比例，保留两位小数
            BigDecimal maleRate = BigDecimal.valueOf(male)
                    .divide(BigDecimal.valueOf(memberCount),
                            4, RoundingMode.HALF_UP); // 计算时保留更多位，避免后续舍入误差
            BigDecimal rateValue = maleRate.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP);
            count.setMaleRatio(rateValue.doubleValue());
            // 计算女性比例
            BigDecimal femaleRateValue = BigDecimal.valueOf(100).subtract(rateValue);
            count.setFemaleRatio(femaleRateValue.doubleValue());
        }
        return count;
    }

    @Override
    public boolean review(ReviewForm form) {
        if (null != form.getId()) {
            ClubMember entity = this.getById(form.getId());
            if (null == entity) {
                throw new ServiceException("未找到该社团成员信息");
            }
            if (!entity.getStatus().equals(ReviewStatus.PENDING.getCode())) {
                throw new ServiceException("该社团成员已审核，不能重复审核");
            }
            return this.lambdaUpdate().eq(ClubMember::getId, form.getId()).set(ClubMember::getStatus, form.getStatus())
                    .set(ClubMember::getReviewAt, SecurityUtils.getThirdUserid())
                    .set(ClubMember::getReviewDate, LocalDateTime.now()).update();
        }
        if (null != form.getIds()) {
            return this.lambdaUpdate().in(ClubMember::getId, form.getIds()).set(ClubMember::getStatus, form.getStatus())
                    .set(ClubMember::getReviewAt, SecurityUtils.getThirdUserid())
                    .set(ClubMember::getReviewDate, LocalDateTime.now()).update();
        }
        return true;
    }

    @Override
    public ClubReviewCountVO countReview() {
        ClubReviewCountVO count = new ClubReviewCountVO();
        List<ClubMember> list = this.lambdaQuery().select(ClubMember::getStatus).eq(ClubMember::getDelFlag, DelFlag.VALID.getCode())
                .in(ClubMember::getDeptId, SecurityUtils.getAncestors()).list();
        if (!list.isEmpty()) {
            count.setPendingAuditCount(list.stream().filter(item -> item.getStatus().equals(ReviewStatus.PENDING.getCode())).count());
            count.setApprovedAuditCount(list.size() - count.getPendingAuditCount());
        }
        return count;
    }

    @Override
    public Long joinClub(UserVO user, Long clubId) {
        Long count = this.lambdaQuery()
                .eq(ClubMember::getClubInfoId, clubId)
                .eq(ClubMember::getCreateAt, SecurityUtils.getThirdUserid())
                .eq(ClubMember::getDelFlag, DelFlag.VALID.getCode())
                .ne(ClubMember::getStatus, ReviewStatus.REJECT.getCode()).count();
        if (count > 0) {
            throw new ServiceException("您已加入该社团，请勿重复申请");
        }
        ClubInfo clubInfo = clubInfoMapper.selectById(clubId);
        if (null == clubInfo) {
            throw new ServiceException("未找到该社团信息");
        }
        // 判断是否在社团招新时间范围内
        if (clubInfo.getRecruitmentStartDate().isAfter(LocalDateTime.now()) || clubInfo.getRecruitmentEndDate().isBefore(LocalDateTime.now())) {
            throw new ServiceException("不在社团招新时间范围内");
        }
        int clubStatus = ReviewStatus.PENDING.getCode();
        if (clubInfo.getIsExamRequired() == 0) {
            clubStatus = ReviewStatus.PASS.getCode();
        }
        ClubMember entity = new ClubMember();
        entity.setClubInfoId(clubId);
        entity.setName(user.getNickName());
        entity.setPhoneNumber(DataTransfer.maskValue(user.getPhonenumber(),SensitiveType.PHONE));
        entity.setPhoneEncrypt(SensitiveUtil.encrypt(user.getPhonenumber()));
        if (StringUtils.isNoneBlank(user.getSex())) {
            entity.setSex(user.getSex().equals("1") ? 0 : 1);
        }
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(user.getUserId() + "");
        entity.setDeptId(user.getDeptId());
        entity.setApplicationDate(LocalDateTime.now());
        entity.setStatus(clubStatus);
        entity.setReviewAt(SecurityUtils.getThirdUserid());
        entity.setReviewDate(LocalDateTime.now());
        this.save(entity);
        return entity.getId();
    }

    @Override
    public List<ClubMemberVO> myClubInfos(ClubMemberRequest request) {
        request.setCreateAt(SecurityUtils.getThirdUserid());
        return this.baseMapper.queryClubMembers(request);
    }

    @Override
    public String updatePhone(){
        LambdaQueryWrapper<ClubMember> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ClubMember::getId, ClubMember::getPhoneNumber);
        List<ClubMember> allList = this.list(queryWrapper);
        //将所有数据手机号加密脱敏
        for(ClubMember clubMember : allList){
            if(StringUtils.isEmpty(clubMember.getPhoneNumber()) || clubMember.getPhoneNumber().indexOf("****") != -1){
                continue;
            }
            //脱敏
            String desPhone = com.ows.ufa.common.core.utils.DataTransfer.maskValue(clubMember.getPhoneNumber(), SensitiveType.PHONE);
            //加密
            String encrypt = com.ows.ufa.common.core.utils.DataTransfer.encryptValue(clubMember.getPhoneNumber());
            clubMember.setPhoneNumber(desPhone);
            clubMember.setPhoneEncrypt(encrypt);
            this.updateById(clubMember);
        }
        return "1";
    }
}