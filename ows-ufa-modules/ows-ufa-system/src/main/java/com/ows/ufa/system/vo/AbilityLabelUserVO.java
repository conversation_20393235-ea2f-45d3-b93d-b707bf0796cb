package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 标签人员表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@Schema(description ="标签人员表VO")
public class AbilityLabelUserVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "能力标签ID")
    private Long abilityId;

    @Schema(description = "标签ID")
    private Long labelId;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    private String labelName;

}