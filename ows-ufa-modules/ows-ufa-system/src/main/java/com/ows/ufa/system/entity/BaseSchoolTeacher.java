package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Data
@TableName("base_school_teacher")
@Schema(description ="实体")
public class BaseSchoolTeacher implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "教师学校关系表")
    private String id;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "院系ID")
    private String deptId;

    @Schema(description = "教师ID")
    private String tchId;

    @Schema(description = "教师工号")
    private String tchNo;

    @Schema(description = "录用日期")
    private LocalDateTime tchEmployDate;

    @Schema(description = "工作年限")
    private Integer tchWorkTime;

    @Schema(description = "在职状态[1在职|2退休]")
    private String tchWorkState;

    @Schema(description = "头像地址")
    private String headPath;

    @Schema(description = "教师简介")
    private String tchDesc;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "来源")
    private String tchSource="1";

}