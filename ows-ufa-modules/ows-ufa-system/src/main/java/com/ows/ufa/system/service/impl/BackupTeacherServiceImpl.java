package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.SensitiveUtil;
import com.ows.ufa.common.core.utils.uuid.UUID;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.aspect.MyDataScope;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.entity.BackupTeacher;
import com.ows.ufa.system.entity.BaseSchoolTeacher;
import com.ows.ufa.system.entity.BaseTeacher;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.enums.DelFlag;
import com.ows.ufa.system.enums.TeacherStatus;
import com.ows.ufa.system.form.BackupTeacherForm;
import com.ows.ufa.system.mapper.BackupTeacherMapper;
import com.ows.ufa.system.request.BackupTeacherRequest;
import com.ows.ufa.system.service.AreaService;
import com.ows.ufa.system.service.BackupTeacherService;
import com.ows.ufa.system.service.BaseSchoolTeacherService;
import com.ows.ufa.system.service.BaseTeacherService;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.util.IDCardUtils;
import com.ows.ufa.system.vo.BackupTeacherVO;
import com.ows.ufa.system.vo.count.BackupTeacherCountVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

import static com.ows.ufa.system.enums.TeacherStatus.NOT_PUSHED;

/**
 * <p>
 * 备份师资资源 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class BackupTeacherServiceImpl extends ServiceImpl<BackupTeacherMapper, BackupTeacher> implements BackupTeacherService {
    private static final Logger log = LoggerFactory.getLogger(BackupTeacherServiceImpl.class);

    private final AreaService areaServiceImpl;
    private final BaseTeacherService baseTeacherServiceImpl;
    private final BaseSchoolTeacherService baseSchoolTeacherServiceImpl;

    @Override
    @MyDataScope(alias="back",dataId="dept_id")
    public List<BackupTeacher> queryBackupTeachers(BackupTeacherRequest request) {
        LambdaQueryWrapper<BackupTeacher> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BackupTeacher::getId, BackupTeacher::getTchName, BackupTeacher::getDeptName, BackupTeacher::getTchAddr, BackupTeacher::getTchPhone, BackupTeacher::getSource, BackupTeacher::getTchPhoneEncrypt);
        queryWrapper.eq(BackupTeacher::getStatus, NOT_PUSHED.getCode());
        queryWrapper.eq(BackupTeacher::getDelFlag, DelFlag.VALID.getCode());
        queryWrapper.like(StringUtils.isNoneBlank(request.getTchName()), BackupTeacher::getTchName, request.getTchName());
        queryWrapper.like(StringUtils.isNoneBlank(request.getDeptName()), BackupTeacher::getDeptName, request.getDeptName());
        queryWrapper.like(null != request.getTchAddrAreaId(), BackupTeacher::getTchAddrAreaId, request.getTchAddrAreaId());
        queryWrapper.eq(null != request.getSource(), BackupTeacher::getSource, request.getSource());
        queryWrapper.in(BackupTeacher::getDeptId,SecurityUtils.getAncestors());
        queryWrapper.orderByDesc(BackupTeacher::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public BackupTeacherVO findBackupTeacher(Long id) {
        BackupTeacher entity = this.getById(id);
        BackupTeacherVO vo = (BackupTeacherVO) com.ows.ufa.common.core.utils.DataTransfer.transfer(entity, BackupTeacherVO.class);
        //解密返回明文
        vo.setTchPhone(SensitiveUtil.decrypt(entity.getTchPhoneEncrypt()));
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveBackupTeacher(BackupTeacherForm form) {
        BackupTeacher entity = (BackupTeacher) com.ows.ufa.common.core.utils.DataTransfer.transfer(form, BackupTeacher.class);
        entity.setTchAddr(areaServiceImpl.getAreaName(form.getTchAddrAreaId()));
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBackupTeacher(BackupTeacherForm form) {
        BackupTeacher entity = (BackupTeacher) com.ows.ufa.common.core.utils.DataTransfer.transfer(form, BackupTeacher.class);
        entity.setTchAddr(areaServiceImpl.getAreaName(form.getTchAddrAreaId()));
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(SecurityUtils.getThirdUserid());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBackupTeacher(Long id) {
        return this.lambdaUpdate().eq(BackupTeacher::getId, id).set(BackupTeacher::getDelFlag, DelFlag.DELETE.getCode())
                .set(BackupTeacher::getUpdateTime, LocalDateTime.now()).set(BackupTeacher::getUpdateAt, SecurityUtils.getLoginUser().getThirdUserid()).update();
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public BackupTeacher hireBackupTeacher(BackupTeacherVO vo) {
        BackupTeacher entity = (BackupTeacher) DataTransfer.transfer(vo, BackupTeacher.class);
        entity.setUpdateTime(LocalDateTime.now());
        String loginUserId = SecurityUtils.getLoginUser().getThirdUserid();
        entity.setUpdateAt(loginUserId);
        String schoolId = SecurityUtils.getThirdDeptId();
        String baseTeacherId = baseTeacherServiceImpl.hasTeacher(vo.getTchCard());
        if (null != baseTeacherId) {
            log.error("该教师已存在,tchCard:{}", vo.getTchCard());
            boolean hire = baseSchoolTeacherServiceImpl.isHire(baseTeacherId, schoolId , vo.getDeptId());
            if (hire) {
                log.error("该教师已录用,baseTeacherId:{},deptId:{}", baseTeacherId, vo.getDeptId());
                throw new ServiceException("所填写的身份证号已经存在！");
            }
        }
        //TODO 推送到baseTeacher\baseSchoolTeacher
        if (null == baseTeacherId) {
            UUID uuid = UUID.randomUUID();
            baseTeacherId = uuid.toString(true);
            BaseTeacher baseTeacher = (BaseTeacher) DataTransfer.transfer(entity, BaseTeacher.class);
            baseTeacher.setId(baseTeacherId);
            baseTeacher.setTchBirthday(IDCardUtils.extractBirthDate(vo.getTchCard()));
            baseTeacher.setTchSex(IDCardUtils.extractGender(vo.getTchCard()));
            baseTeacher.setStatus("1");
            baseTeacher.setCreateBy(loginUserId);
            baseTeacher.setCreateTime(LocalDateTime.now());
            baseTeacher.setUpdateBy(loginUserId);
            baseTeacher.setUpdateTime(LocalDateTime.now());
            baseTeacherServiceImpl.save(baseTeacher);
        }
        BaseSchoolTeacher baseSchoolTeacher = (BaseSchoolTeacher) DataTransfer.transfer(entity, BaseSchoolTeacher.class);
        UUID uuid = UUID.randomUUID();
        baseSchoolTeacher.setId(uuid.toString(true));
        baseSchoolTeacher.setSchoolId(schoolId + "");
        baseSchoolTeacher.setTchId(baseTeacherId);
        baseSchoolTeacher.setCreateBy(loginUserId);
        baseSchoolTeacher.setCreateTime(LocalDateTime.now());
        baseSchoolTeacher.setUpdateBy(loginUserId);
        baseSchoolTeacher.setUpdateTime(LocalDateTime.now());
        boolean flag = baseSchoolTeacherServiceImpl.saveBaseSchoolTeacher(baseSchoolTeacher);
        if (!flag) {
            log.error("推送到baseSchoolTeacher失败,baseSchoolTeacher:{}", baseSchoolTeacher);
            throw new ServiceException("推送到baseSchoolTeacher失败");
        }
        entity.setStatus(TeacherStatus.PUSHED.getCode());
        entity.setTchAddrDetail(entity.getTchAddr());
        entity.setTchAddr(null);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTeacher(BackupTeacher entity) {
        return this.updateById(entity);
    }

    @Override
    public BackupTeacherCountVO countBackupTeacher() {
        BackupTeacherCountVO countVO = new BackupTeacherCountVO();
        List<BackupTeacher> list = this.lambdaQuery().select(BackupTeacher::getSource).eq(BackupTeacher::getDelFlag, DelFlag.VALID.getCode())
                .eq(BackupTeacher::getStatus, NOT_PUSHED.getCode()).in(BackupTeacher::getDeptId,SecurityUtils.getAncestors()).list();
        if (!list.isEmpty()) {
            countVO.setTotalCount(list.size());
            countVO.setIndAddCount(list.stream().filter(item -> item.getSource() == 0).count());
            countVO.setCouAddCount(countVO.getTotalCount() - countVO.getIndAddCount());
        }
        return countVO;
    }

    @Override
    public String updatePhone() {
        LambdaQueryWrapper<BackupTeacher> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BackupTeacher::getId, BackupTeacher::getTchPhone);
        List<BackupTeacher> allList = this.list(queryWrapper);
        //将所有数据手机号加密脱敏
        for(BackupTeacher backupTeacher : allList){
            if(StringUtils.isEmpty(backupTeacher.getTchPhone()) || backupTeacher.getTchPhone().indexOf("****") != -1){
                continue;
            }
            //脱敏
            String desPhone = com.ows.ufa.common.core.utils.DataTransfer.maskValue(backupTeacher.getTchPhone(), SensitiveType.PHONE);
            //加密
            String encrypt = com.ows.ufa.common.core.utils.DataTransfer.encryptValue(backupTeacher.getTchPhone());
            backupTeacher.setTchPhone(desPhone);
            backupTeacher.setTchPhoneEncrypt(encrypt);
            this.updateById(backupTeacher);
        }
        return "1";
    }

}