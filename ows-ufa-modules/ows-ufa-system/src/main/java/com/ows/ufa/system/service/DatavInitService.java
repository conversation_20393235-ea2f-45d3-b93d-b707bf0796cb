package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.DistrictYkzUid;

/**
 * <p>
 *  数据大屏初始实现类
 * </p>
 *
 */
public interface DatavInitService {

    /**
     * 根据token查询当前对应区县名称/市级分类名称
     *
     * @return
     */
    public DistrictYkzUid getDistrictYkzUidByToken(String token);

    /**
     * 校验token是否过期
     *
     * @param token
     * @return
     */
    public Boolean verifyToken(String token);
}
