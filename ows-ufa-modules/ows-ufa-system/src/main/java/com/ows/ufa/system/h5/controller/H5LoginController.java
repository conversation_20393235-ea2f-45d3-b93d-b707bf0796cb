package com.ows.ufa.system.h5.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.domain.request.H5EditMobileUserRequest;
import com.ows.ufa.system.service.H5LoginService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "h5/login")
@Tag(name = "login", description = "h5登录")
public class H5LoginController extends BaseController {

    private final H5LoginService h5LoginService;

    @GetMapping("getTokenByCode")
    @Operation(summary = "根据code换取token")
    public AjaxResult getTokenByCode(@RequestParam(value = "authCode") String authCode) {
        return success(h5LoginService.getTokenByCode(authCode));
    }

    @GetMapping("getMobileLoginInfo")
    @Operation(summary = "根据token获取用户权限等信息")
    public AjaxResult getMobileLoginInfo(@RequestHeader("token") String token) {
        return success(h5LoginService.getMobileLoginInfo(token));
    }

    @GetMapping("userProfile")
    @Operation(summary = "查看用户信息")
    public AjaxResult userProfile(@RequestHeader("token") String token) {
        return success(h5LoginService.userProfile(token));
    }

    @PostMapping("editMobileUser")
    @Operation(summary = "用户修改邮箱和昵称")
    public AjaxResult editMobileUser(@RequestHeader("token") String token,
                                     @RequestBody H5EditMobileUserRequest editMobileUserRequest) {
        return success(h5LoginService.editMobileUser(editMobileUserRequest,token));
    }

}
