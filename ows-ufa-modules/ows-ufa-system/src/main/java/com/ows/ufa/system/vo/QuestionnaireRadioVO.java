package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 问卷调查信息填报表
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@Schema(description ="问卷调查信息组件VO")
public class QuestionnaireRadioVO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "组件唯一标识")
    private Long id;

    @Schema(description = "组件类型")
    private String type;

    @Schema(description = "组件名称")
    private String name;

    @Schema(description = "用户选择的值")
    private Object value;

    @Schema(description = "其他描述信息")
    private String otherDesc;

    @Schema(description = "组件属性配置")
    private Props props;

    @Data
    @Schema(description = "组件属性详细信息")
    public static class Props implements Serializable {

        private static final long serialVersionUID = 1L;

        @Schema(description = "是否允许填写其他内容")
        private Boolean other;

        @Schema(description = "是否处于编辑状态")
        private Boolean isEdit;

        @Schema(description = "选项列表")
        private List<OptionItem> options;

        @Schema(description = "条件满足标识")
        private String conditionMet;

        @Schema(description = "条件限制列表")
        private List<?> conditionLimitList;

        @Schema(description = "输入提示文本")
        private String placeholder;

        @Schema(description = "是否必填项")
        private Boolean required;
    }

    @Data
    @Schema(description = "选项项详细信息")
    public static class OptionItem implements Serializable {
        private static final long serialVersionUID = 1L;
        @Schema(description = "显示标签")
        private String label;

        @Schema(description = "实际值")
        private String value;
    }
}