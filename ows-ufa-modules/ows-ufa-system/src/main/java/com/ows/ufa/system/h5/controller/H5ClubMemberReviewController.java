package com.ows.ufa.system.h5.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.service.ClubMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "h5/clubMemberReview")
@Tag(name = "h5clubMemberReview", description = "h5社团成员申请审核接口")
public class H5ClubMemberReviewController extends BaseController {

    private final ClubMemberService ClubMemberServiceImpl;

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubMemberByPage(ClubMemberRequest request) {
        startPage();
        return success(getDataTable(ClubMemberServiceImpl.queryClubMemberReviews(request)));
    }

    @PostMapping("review")
    @Operation(summary = "审核")
    public AjaxResult review(@RequestBody ReviewForm form) {
        return success(ClubMemberServiceImpl.review(form));
    }

}
