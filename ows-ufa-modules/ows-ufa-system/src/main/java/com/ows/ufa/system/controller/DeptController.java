package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.DeptService;
import com.ows.ufa.system.form.DeptForm;
import com.ows.ufa.system.request.DeptRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 部门表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "baseDept")
@Tag(name = "部门表接口", description = "部门表接口")
public class DeptController extends BaseController {

    private final DeptService DeptServiceImpl;

    @GetMapping("list")
    @Operation(summary = "部门表分页查询")
    public AjaxResult listDeptByPage() {
        return success(DeptServiceImpl.queryDepts());
    }
}
