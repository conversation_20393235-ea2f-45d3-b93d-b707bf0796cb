package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.ClubMember;
import com.ows.ufa.system.entity.TeacherResourcesApply;
import com.ows.ufa.system.entity.TeacherResourcesApplyHandle;
import com.ows.ufa.system.enums.ResourcesStatus;
import com.ows.ufa.system.form.TeacherResourcesApplyForm;
import com.ows.ufa.system.service.OpenService;
import com.ows.ufa.system.service.TeacherResourcesApplyHandleService;
import com.ows.ufa.system.util.DateUtils;
import com.ows.ufa.system.vo.TeacherResourcesApplyVO;
import com.ows.ufa.system.request.TeacherResourcesApplyRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.TeacherResourcesApplyMapper;
import com.ows.ufa.system.service.TeacherResourcesApplyService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.system.vo.count.TeacherApplyCountVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 师资资源申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class TeacherResourcesApplyServiceImpl extends ServiceImpl<TeacherResourcesApplyMapper, TeacherResourcesApply> implements TeacherResourcesApplyService {

    private final TeacherResourcesApplyHandleService teacherResourcesApplyHandleService;
    private final OpenService openService;

    @Override
    public List<TeacherResourcesApply> queryTeacherResourcesApplys(TeacherResourcesApplyRequest req) {
        LambdaQueryWrapper<TeacherResourcesApply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(req.getTitle()), TeacherResourcesApply::getTitle, req.getTitle());
        queryWrapper.like(StringUtils.isNotBlank(req.getUserName()), TeacherResourcesApply::getUserName, req.getUserName());
        queryWrapper.like(null != req.getStatus(), TeacherResourcesApply::getStatus, req.getStatus());
        if (null != req.getBeginTime() && null != req.getEndTime()) {
            queryWrapper.between(null != req.getBeginTime(), TeacherResourcesApply::getCreateTime,
                    DateUtils.startOfDay(req.getBeginTime()), DateUtils.endOfDay(req.getEndTime()));
        }
        queryWrapper.in(TeacherResourcesApply::getDeptId, SecurityUtils.getAncestors());
        queryWrapper.orderByDesc(TeacherResourcesApply::getCreateTime);
        return this.list(queryWrapper);
    }

    @Override
    public TeacherResourcesApplyVO findTeacherResourcesApply(Long id) {
        TeacherResourcesApply entity = this.getById(id);
        TeacherResourcesApplyVO vo = (TeacherResourcesApplyVO) DataTransfer.transfer(entity, TeacherResourcesApplyVO.class);
        List<TeacherResourcesApplyHandle> list =
                teacherResourcesApplyHandleService.lambdaQuery().eq(TeacherResourcesApplyHandle::getTeacherResourcesApplyId, id).list();
        vo.setHandles(list);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveTeacherResourcesApply(TeacherResourcesApplyForm vo) {
        TeacherResourcesApply entity = (TeacherResourcesApply) DataTransfer.transfer(vo, TeacherResourcesApply.class);
        entity.setUserId(Long.parseLong(SecurityUtils.getThirdUserid()));
        entity.setUserName(SecurityUtils.getLoginUser().getSysUser().getNickName());
        entity.setSchoolName(SecurityUtils.getLoginUser().getSysUser().getDept().getDeptName());
        entity.setCreateTime(LocalDateTime.now());
        entity.setStatus(ResourcesStatus.PENDING.getCode());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        //同步数据给三级治理中心
        String eventNum = openService.syncDataToCenter(entity, vo.getAreaCode());
        entity.setEventNum(eventNum);
        this.save(entity);
        TeacherResourcesApplyHandle handle = new TeacherResourcesApplyHandle();
        //TODO 根据学校地址判断
        handle.setTeacherResourcesApplyId(entity.getId());
        handle.setHandleUserName("三级治理中心");
        handle.setStatus(ResourcesStatus.PENDING.getCode());
        handle.setCreateTime(LocalDateTime.now());
        handle.setEventNum(eventNum);
        handle.setOrgName(SecurityUtils.getLoginUser().getSysUser().getDept().getParentName());
        teacherResourcesApplyHandleService.save(handle);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTeacherResourcesApply(TeacherResourcesApplyVO vo) {
        TeacherResourcesApply entity = (TeacherResourcesApply) DataTransfer.transfer(vo, TeacherResourcesApply.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeTeacherResourcesApply(Long id) {
        return this.removeById(id);
    }

    @Override
    public TeacherApplyCountVO countTeacherApply() {
        TeacherApplyCountVO count = new TeacherApplyCountVO();
        List<TeacherResourcesApply> list = this.lambdaQuery().select(TeacherResourcesApply::getStatus).in(TeacherResourcesApply::getDeptId, SecurityUtils.getAncestors()).list();
        if (!list.isEmpty()) {
            count.setApplyCount(list.size());
            count.setPendingCount(list.stream().filter(e -> e.getStatus().equals(ResourcesStatus.PENDING.getCode())).count());
            count.setProcessedCount(count.getApplyCount() - count.getPendingCount());
        }
        return count;
    }

    @Override
    public TeacherApplyCountVO applyStatics() {
        TeacherApplyCountVO count = new TeacherApplyCountVO();
        List<TeacherResourcesApply> list = this.lambdaQuery().select(TeacherResourcesApply::getStatus).list();
        if (!list.isEmpty()) {
            count.setApplyCount(list.size());
            count.setPendingCount(list.stream().filter(e -> e.getStatus().equals(ResourcesStatus.PENDING.getCode())).count());
            count.setProcessedCount(count.getApplyCount() - count.getPendingCount());
            count.setRate(String.format("%.2f", (double) count.getProcessedCount() / count.getApplyCount() * 100) + "%");
        }else{
            count.setApplyCount(0);
            count.setPendingCount(0L);
            count.setProcessedCount(0L);
            count.setRate("0.00%");
        }
        return count;
    }
}