package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="Request")
public class ClubMemberRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "社团名称")
    private String clubInfoName;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "联系电话")
    private String phoneNumber;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private LocalDateTime beginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description= "结束日期")
    private LocalDateTime endTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "申请开始时间")
    private LocalDateTime applicationBeginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description= "申请结束日期")
    private LocalDateTime applicationEndTime;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(hidden = true)
    private List<String> ancestors;

    @Schema(hidden = true)
    private String createAt;
}