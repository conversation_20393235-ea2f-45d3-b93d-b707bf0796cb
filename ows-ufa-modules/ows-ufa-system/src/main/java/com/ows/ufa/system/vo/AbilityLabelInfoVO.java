package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.ows.ufa.system.entity.AbilityLabelUser;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 标签信息表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@Schema(description ="标签信息表VO")
public class AbilityLabelInfoVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "能力标签ID")
    private Long abilityId;

    @Schema(description = "标签名")
    private String name;

    private List<AbilityLabelUser> labelUsers;
}