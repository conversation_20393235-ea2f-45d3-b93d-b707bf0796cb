package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 缴费订单信息表
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Schema(description ="缴费订单信息表VO")
public class AdmissionNoticeVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @Schema(description = "缴费订单编号")
    @NotNull(message = "缴费订单编号不能为空")
    private String orderNo;

    @Schema(description = "缴费时间")
    @NotNull(message = "缴费时间不能为空")
    private LocalDateTime payTime;

    @Schema(description = "学员姓名")
    @NotNull(message = "学员姓名不能为空")
    private String userName;

    @Schema(description = "学校图片")
    @NotNull(message = "学校图片不能为空")
    private String logUrl;

    @Schema(description = "学校名称")
    @NotNull(message = "学校名称不能为空")
    private String schoolName;

    @Schema(description = "系名称")
    @NotNull(message = "系名称不能为空")
    private String deptName;

    @Schema(description = "专业名称")
    @NotNull(message = "专业名称不能为空")
    private String majorName;

    @Schema(description = "课程名称")
    @NotNull(message = "课程名称不能为空")
    private String courseName;

    @Schema(description = "退费时间")
    @NotNull(message = "退费时间不能为空")
    private LocalDateTime refundTime;

    @Schema(description = "状态:0-缴费;1-退费")
    @NotNull(message = "状态:0-缴费;1-退费不能为空")
    private Integer status;

    @Schema(description = "状态:0-未读;1-已读")
    @NotNull(message = "状态:0-未读;1-已读不能为空")
    private Integer readStatus;

    @Schema(description = "通知书图片地址")
    @NotNull(message = "通知书图片地址不能为空")
    private String noticeUrl;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime updateTime;

    @Schema(description = "开课时间")
    @NotNull(message = "开课时间不能为空")
    private LocalDateTime courseStartTime;

    @Schema(description = "学校地址")
    @NotNull(message = "学校地址不能为空")
    private String schoolAddr;

}