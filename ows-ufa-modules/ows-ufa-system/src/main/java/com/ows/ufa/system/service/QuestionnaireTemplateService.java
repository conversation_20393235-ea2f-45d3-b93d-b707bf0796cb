package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.QuestionnaireTemplate;
import com.ows.ufa.system.vo.QuestionnaireTemplateVO;
import com.ows.ufa.system.form.QuestionnaireTemplateForm;
import com.ows.ufa.system.request.QuestionnaireTemplateRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 * 问卷调查信息模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
public interface QuestionnaireTemplateService extends IService<QuestionnaireTemplate> {

    List<QuestionnaireTemplate> queryQuestionnaireTemplates(QuestionnaireTemplateRequest request);

    QuestionnaireTemplateVO findQuestionnaireTemplate(Long id);

    Long saveQuestionnaireTemplate(QuestionnaireTemplateForm form);

    boolean updateQuestionnaireTemplate(QuestionnaireTemplateForm form);

    boolean removeQuestionnaireTemplate(Long id);
}
