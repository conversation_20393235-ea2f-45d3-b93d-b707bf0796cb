package com.ows.ufa.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ProgressStatus implements IEnum<Integer> {
    //活动状态:0-未开始;1-报名中;2-进行中;3-已结束
    NOT_STARTED(0, "未开始"),
    SIGN_UP(1, "报名中"),
    IN_PROGRESS(2, "进行中"),
    FINISHED(3, "已结束");

    ProgressStatus(Integer code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final Integer code;
    @JsonValue
    private final String descp;

    @Override
    public Integer getValue() {
        return this.code;
    }

}
