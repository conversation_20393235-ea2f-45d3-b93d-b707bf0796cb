package com.ows.ufa.system.vo.excel;

import com.ows.ufa.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
public class ClubInfoExcelVO implements Serializable {

    private static final long serialVersionUID=1L;

    @Excel(name = "社团名称", type = Excel.Type.ALL)
    private String clubName;

    @Excel(name = "社团简介", type = Excel.Type.ALL)
    private String clubDescp;

    @Excel(name = "负责人", type = Excel.Type.ALL)
    private String leader;

    @Excel(name = "联系方式", type = Excel.Type.ALL)
    private String phoneNumber;

    @Excel(name = "社团状态",readConverterExp = "0=待审核,1=已成立,2=审核不通过", type = Excel.Type.ALL)
    private Integer status;

    @Excel(name = "成员数量", type = Excel.Type.ALL)
    private Integer memberCount;

    @Excel(name = "活动数量", type = Excel.Type.ALL)
    private Integer activityCount;

    @Excel(name = "成立日期",dateFormat = "yyyy-MM-dd", type = Excel.Type.ALL)
    private LocalDateTime reviewDate;
}