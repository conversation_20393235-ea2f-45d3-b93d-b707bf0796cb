package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.ows.ufa.system.entity.AbilityLabelInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 能力标签表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@Schema(description ="能力标签表VO")
public class AbilityLabelVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "标签主题")
    private String title;

    @Schema(description = "标签信息")
    List<AbilityLabelInfoVO> abilityLabelInfos;
}