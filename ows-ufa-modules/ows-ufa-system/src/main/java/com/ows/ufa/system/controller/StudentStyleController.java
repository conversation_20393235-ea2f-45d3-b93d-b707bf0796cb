package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.service.OpenService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 学员风采
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "student")
@Tag(name = "学员风采接口", description = "外部推送接口")
public class StudentStyleController extends BaseController {

    private final OpenService openService;

    @GetMapping("queryUser")
    @Operation(summary = "查询学员信息")
    public AjaxResult queryUser(String userId) {
        return success(openService.queryUser(userId));
    }

    @GetMapping("queryStyle")
    @Operation(summary = "查询学员风采")
    public AjaxResult findStudentStyle(String userId, Integer styleNo) {
        return success(openService.findStudentStyle(userId, styleNo));
    }
}
