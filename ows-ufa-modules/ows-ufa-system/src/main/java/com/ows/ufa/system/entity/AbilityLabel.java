package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 能力标签表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@TableName("t_ability_label")
@Schema(description ="能力标签表实体")
public class AbilityLabel implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "标签主题")
    private String title;

    @Schema(description = "删除标志：0-删除；1-有效")
    private Integer delFlag;

    @Schema(description = "数据权限部门id")
    private String deptId;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


}