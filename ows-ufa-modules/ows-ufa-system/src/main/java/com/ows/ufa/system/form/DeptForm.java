package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 部门表
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@Schema(description ="部门表VO")
public class DeptForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "父部门ID")
    @NotNull(message = "父部门ID不能为空")
    private String parentId;

    @Schema(description = "祖级列表")
    @NotNull(message = "祖级列表不能为空")
    private String ancestors;

    @Schema(description = "部门名称")
    @NotNull(message = "部门名称不能为空")
    private String deptName;

    @Schema(description = "显示顺序")
    @NotNull(message = "显示顺序不能为空")
    private Integer orderNum;

    @Schema(description = "负责人")
    @NotNull(message = "负责人不能为空")
    private String leader;

    @Schema(description = "联系电话")
    @NotNull(message = "联系电话不能为空")
    private String phone;

    @Schema(description = "创建者")
    @NotNull(message = "创建者不能为空")
    private String createBy;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    @NotNull(message = "更新者不能为空")
    private String updateBy;

    @Schema(description = "更新时间")
    @NotNull(message = "更新时间不能为空")
    private LocalDateTime updateTime;

    @Schema(description = "部门状态[1正常]")
    @NotNull(message = "部门状态[1正常]不能为空")
    private String status;

    @Schema(description = "机构地址")
    @NotNull(message = "机构地址不能为空")
    private String deptAddress;

    @Schema(description = "机构传真")
    @NotNull(message = "机构传真不能为空")
    private String deptFax;

    @Schema(description = "机构邮编")
    @NotNull(message = "机构邮编不能为空")
    private String deptPostcode;

    @Schema(description = "机构邮箱")
    @NotNull(message = "机构邮箱不能为空")
    private String deptEmail;

    @Schema(description = "是否学校[1学校2其他机构3行政区划]")
    @NotNull(message = "是否学校[1学校2其他机构3行政区划]不能为空")
    private String isSchool;

    @Schema(description = "id字段唯一键")
    private String deptId;

}