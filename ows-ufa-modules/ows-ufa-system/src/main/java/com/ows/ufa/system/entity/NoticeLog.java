package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知书推送信息日志记录表
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@TableName("t_notice_log")
@Schema(description ="通知书推送信息日志记录表实体")
public class NoticeLog implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long logId;

    @Schema(description = "推送类型:0-缴费;1-退费")
    private Integer noticeType;

    @Schema(description = "状态：0-无需处理，1-待处理，2-已处理")
    private Integer status;

    @Schema(description = "JSON内容")
    private String content;

    @Schema(description = "时间")
    private LocalDateTime createTime;


}