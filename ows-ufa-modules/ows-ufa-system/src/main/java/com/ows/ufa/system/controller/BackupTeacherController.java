package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.entity.BackupTeacher;
import com.ows.ufa.system.form.BackupTeacherForm;
import com.ows.ufa.system.request.BackupTeacherRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.BackupTeacherService;
import com.ows.ufa.system.vo.BackupTeacherVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 备份师资资源 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "backupTeacher")
@Tag(name = "backupTeacher", description = "备份师资资源接口")
public class BackupTeacherController extends BaseController {

    private final BackupTeacherService BackupTeacherServiceImpl;

    @GetMapping("count")
    @Operation(summary = "统计")
    public AjaxResult countBackupTeacher() {
        return success(BackupTeacherServiceImpl.countBackupTeacher());
    }

    @GetMapping("list")
    @Operation(summary = "备份师资资源分页查询")
    public AjaxResult listBackupTeacherByPage(BackupTeacherRequest request) {
        startPage();
        return success(getDataTable(BackupTeacherServiceImpl.queryBackupTeachers(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "备份师资资源查询详情")
    public AjaxResult findBackupTeacher(@PathVariable Long id) {
        return success(BackupTeacherServiceImpl.findBackupTeacher(id));
    }

    @PostMapping
    @Operation(summary = "备份师资资源新增数据")
    public AjaxResult saveBackupTeacher(@RequestBody BackupTeacherForm form) {
        return success(BackupTeacherServiceImpl.saveBackupTeacher(form));
    }

    @PostMapping("update")
    @Operation(summary = "备份师资资源修改数据")
    public AjaxResult updateBackupTeacher(@RequestBody BackupTeacherForm form) {
        return success(BackupTeacherServiceImpl.updateBackupTeacher(form));
    }

    @PostMapping("hire")
    @Operation(summary = "录用师资资源")
    public AjaxResult hireBackupTeacher(@RequestBody BackupTeacherVO vo) {
        BackupTeacher backupTeacher = BackupTeacherServiceImpl.hireBackupTeacher(vo);
        return success(BackupTeacherServiceImpl.updateTeacher(backupTeacher));
    }

    @PostMapping("delete")
    @Operation(summary = "备份师资资源删除数据")
    public AjaxResult removeBackupTeacher(@RequestBody IdRequest id) {
        return success(BackupTeacherServiceImpl.removeBackupTeacher(id.getId()));
    }
}
