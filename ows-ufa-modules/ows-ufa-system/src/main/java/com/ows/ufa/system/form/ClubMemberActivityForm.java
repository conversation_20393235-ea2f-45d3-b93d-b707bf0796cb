package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 社团成员活动信息表
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="社团成员活动信息表VO")
public class ClubMemberActivityForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团ID")
    @NotNull(message = "社团ID不能为空")
    private Long clubInfoId;

    @Schema(description = "活动ID")
    @NotNull(message = "活动ID不能为空")
    private Long clubActivityId;

    @Schema(description = "姓名")
    @NotNull(message = "姓名不能为空")
    private String name;

    @Schema(description = "性别:0-男;1-女;")
    @NotNull(message = "性别:0-男;1-女;不能为空")
    private Integer sex;

    @Schema(description = "联系电话")
    @NotNull(message = "联系电话不能为空")
    private String phoneNumber;

    @Schema(description = "申请日期")
    @NotNull(message = "申请日期不能为空")
    private LocalDateTime applicationDate;

    @Schema(description = "审核日期")
    @NotNull(message = "审核日期不能为空")
    private LocalDateTime reviewDate;

    @Schema(description = "审核人")
    @NotNull(message = "审核人不能为空")
    private String reviewAt;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    @NotNull(message = "状态:0-待审核;1-审核通过;2-审核不通过不能为空")
    private Integer status;

    @Schema(description = "删除标志:0-删除;1-有效")
    @NotNull(message = "删除标志:0-删除;1-有效不能为空")
    private Integer delFlag;

    @Schema(description = "创建人")
    @NotNull(message = "创建人不能为空")
    private String createAt;

    @Schema(description = "更新人")
    @NotNull(message = "更新人不能为空")
    private String updateAt;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @NotNull(message = "更新时间不能为空")
    private LocalDateTime updateTime;

    @Schema(description = "部门id")
    @NotNull(message = "部门id不能为空")
    private Long deptId;

}