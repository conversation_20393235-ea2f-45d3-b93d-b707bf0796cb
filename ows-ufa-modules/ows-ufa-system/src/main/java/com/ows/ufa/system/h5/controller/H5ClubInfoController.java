package com.ows.ufa.system.h5.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "h5/clubInfo")
@Tag(name = "h5clubInfo", description = "h5社团信息接口")
public class H5ClubInfoController extends BaseController {

    private final ClubInfoService ClubInfoServiceImpl;

    @GetMapping("countClub")
    @Operation(summary = "社团统计")
    public AjaxResult countClub() {
        return success(ClubInfoServiceImpl.countClub());
    }

    @GetMapping("queryAll")
    @Operation(summary = "查询发布招募的社团")
    public AjaxResult queryAll() {
        return success(ClubInfoServiceImpl.queryAll());
    }

    @GetMapping("queryReviewAll")
    @Operation(summary = "查询审核通过的社团")
    public AjaxResult queryReviewAll() {
        return success(ClubInfoServiceImpl.queryReviewAll());
    }

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubInfoByPage(ClubInfoRequest request) {
        startPage();
        return success(getDataTable(ClubInfoServiceImpl.h5queryClubInfos(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "查询详情")
    public AjaxResult findClubInfo(@PathVariable Long id) {
        return success(ClubInfoServiceImpl.h5findClubInfo(id));
    }


    @PostMapping
    @Operation(summary = "新增数据")
    public AjaxResult saveClubInfo(@RequestBody ClubInfoForm form) {
        return success(ClubInfoServiceImpl.saveClubInfo(form));
    }

    @PostMapping("update")
    @Operation(summary = "修改数据")
    public AjaxResult updateClubInfo(@RequestBody ClubInfoForm form) {
        return success(ClubInfoServiceImpl.updateClubInfo(form));
    }

    @PostMapping("delete")
    @Operation(summary = "删除数据")
    public AjaxResult removeClubInfo(@RequestBody IdRequest id) {
        return success(ClubInfoServiceImpl.removeClubInfo(id));
    }

    @PostMapping("recruitment")
    @Operation(summary = "发起招募")
    public AjaxResult recruitment(@RequestBody ClubInfoForm form) {
        return success(ClubInfoServiceImpl.recruitment(form));
    }
}
