package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@Schema(description ="Request")
public class BaseSchoolDeptRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "院系编号")
    private String deptCode;

    @Schema(description = "院系名称")
    private String deptName;

    @Schema(description = "院系主任ID")
    private String deptHeadId;

    @Schema(description = "院系排序")
    private Integer deptSort;

    @Schema(description = "院系说明")
    private String deptDesc;

    @Schema(description = "课程费用参考(单位元)")
    private Integer courseFees;

    @Schema(description = "所属学校")
    private String schoolId;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "状态[1正常]")
    private String status;

    @Schema(description = "校区ID")
    private String campusId;

    @Schema(description = "是否启用[1是0否]")
    private String isEnable;

}