package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 愉快政UID对应市级/区县表
 *
 */
@Data
@TableName("t_district_ykz_uid")
@Schema(description ="愉快政UID对应市级/区县表")
public class DistrictYkzUid implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "愉快政UID")
    private String uid;

    @Schema(description = "愉快政账号名称")
    private String ykzAccountName;

    @Schema(description = "区县名称/市级分类名称")
    private String district;

    @Schema(description = "类型：0-区县 1-市级")
    private String districtType;

}