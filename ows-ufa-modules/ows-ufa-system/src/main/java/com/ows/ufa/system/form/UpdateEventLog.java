package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@TableName("t_update_event_log")
public class UpdateEventLog {
    
    @TableId(type = IdType.AUTO)
    private Long id;
    
    @Schema(description="事件编号")
    private String eventNum;
    
    @Schema(description="日志编号")
    private String logId;
    
    @Schema(description="更新时间")
    private Long updateTime;
} 