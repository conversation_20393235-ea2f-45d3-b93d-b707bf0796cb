package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.NoticeLog;
import com.ows.ufa.system.vo.NoticeLogVO;
import com.ows.ufa.system.request.NoticeLogRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 * 通知书推送信息日志记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
public interface NoticeLogService extends IService<NoticeLog> {

    List<NoticeLog> queryNoticeLogs(NoticeLogRequest request);

    NoticeLogVO findNoticeLog(Long id);

    Long saveNoticeLog(NoticeLogVO vo);

    boolean updateNoticeLog(NoticeLogVO vo);

    boolean removeNoticeLog(Long id);
}
