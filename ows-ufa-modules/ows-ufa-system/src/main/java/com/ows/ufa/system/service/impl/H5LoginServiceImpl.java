package com.ows.ufa.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.StringUtils;
import com.ows.ufa.system.domain.request.H5EditMobileUserRequest;
import com.ows.ufa.system.service.H5LoginService;
import com.ows.ufa.system.service.OpenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 数据概览 服务层处理
 *
 */
@Slf4j
@Service
public class H5LoginServiceImpl implements H5LoginService
{

    @Autowired
    private OpenService openService;

    @Override
    public String getTokenByCode(String authCode) {
        String body = openService.speedLoginMobileByAuthCode(authCode);
        log.debug("获取token的返回体是："+body);
        if(StringUtils.isNotEmpty(body)){
            JSONObject result = JSON.parseObject(body);
            String status = result.getString("status");
            if(status.equals("1")){
                //成功
                return result.getString("token");
            }else{
                //失败
                String msg = result.getString("msg");
                if(StringUtils.isNotEmpty(msg)){
                    throw new ServiceException(result.getString("msg"));
                }else{
                    throw new ServiceException("获取token失败");
                }
            }
        }else{
            throw new ServiceException("获取token失败");
        }
    }

    /**
     * 根据token获取用户权限等信息
     *
     * @param token
     * @return
     */
    @Override
    public Map<String,Object> getMobileLoginInfo(String token) {
        Map<String,Object> res = openService.getMobileLoginInfo(token);
        if(res != null){
            String code = String.valueOf(res.get("code"));
            if(code != null && code.equals("200")){
                res.remove("code");
                res.remove("msg");
                return res;
            }else{
                throw new ServiceException("获取用户权限信息失败");
            }
        }else{
            throw new ServiceException("获取用户权限信息失败");
        }
    }

    /**
     * 查看用户信息
     *
     * @param token
     * @return
     */
    @Override
    public Object userProfile(String token) {
        Map<String,Object> res = openService.userProfile(token);
        if(res != null){
            String code = String.valueOf(res.get("code"));
            if(code != null && code.equals("200")){
                return res.get("data");
            }else{
                throw new ServiceException("获取用户信息失败");
            }
        }else{
            throw new ServiceException("获取用户信息失败");
        }
    }

    /**
     * 用户修改邮箱和昵称
     *
     * @param editMobileUserRequest
     * @param token
     * @return
     */
    @Override
    public String editMobileUser(H5EditMobileUserRequest editMobileUserRequest,String token) {
        String body = openService.editMobileUser(editMobileUserRequest.getUserId(),
                editMobileUserRequest.getEmail(),
                editMobileUserRequest.getNickName(),
                token);
        if (StringUtils.isNotEmpty(body)) {
            JSONObject result = JSON.parseObject(body);
            String code = result.getString("code");
            if (code.equals("200")) {
                //成功
                return "1";
            } else {
                //失败
                String msg = result.getString("msg");
                if (StringUtils.isNotEmpty(msg)) {
                    throw new ServiceException(result.getString("msg"));
                } else {
                    throw new ServiceException("修改失败");
                }
            }
        } else {
            throw new ServiceException("修改失败");
        }
    }
}
