package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 备份师资资源
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Schema(description ="备份师资资源VO")
public class BackupTeacherVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "身份证号")
    private String tchCard;

    @Schema(description = "姓名")
    private String tchName;

    @Schema(description = "学历")
    private String tchEdu;

    @Schema(description = "手机号")
    private String tchPhone;

    @Schema(description = "家庭住址地区ID")
    private String tchAddrAreaId;

    @Schema(description = "家庭住址")
    private String tchAddr;

    @Schema(description = "家庭详细住址")
    private String tchAddrDetail;

    @Schema(description = "毕业学校")
    private String tchGraduatedSchool;

    @Schema(description = "教师头像路径")
    private String headPath;

    @Schema(description = "教师简介")
    private String tchDesc;

    @Schema(description = "工号")
    private String tchNo;

    @Schema(description = "录用日期")
    private LocalDateTime tchEmployDate;

    @Schema(description = "工作年限")
    private Integer tchWorkTime;

    @Schema(description = "院系ID")
    private String deptId;

    @Schema(description = "所属院系")
    private String deptName;

    @Schema(description = "在职状态")
    private String tchWorkState;

    @Schema(description = "师资来源:0-自主新增;1-区县教委")
    private Integer source;
}