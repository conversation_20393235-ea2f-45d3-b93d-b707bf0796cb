package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 社团成员活动信息表
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="社团成员活动信息表Request")
public class ClubMemberActivityRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "社团名称")
    private String clubInfoName;

    @Schema(description = "活动名称")
    private String clubActivityName;

    @Schema(description = "姓名")
    private String memberName;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "申请开始时间")
    private LocalDateTime applicationBeginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(name= "申请结束日期")
    private LocalDateTime applicationEndTime;

    @Schema(hidden = true)
    private List<String> ancestors;

    @Schema(hidden = true)
    private String createAt;
}