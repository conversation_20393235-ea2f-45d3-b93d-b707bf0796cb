package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="备份师资库统计VO")
public class BackupTeacherCountVO implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "备用师资总数")
    private int totalCount;

    @Schema(description = "自主新增师资总数")
    private long indAddCount;

    @Schema(description = "区县教委师资总数")
    private long couAddCount;

}