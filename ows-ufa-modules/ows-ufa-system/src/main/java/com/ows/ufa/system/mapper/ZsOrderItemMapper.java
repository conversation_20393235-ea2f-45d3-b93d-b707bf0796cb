package com.ows.ufa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.entity.ZsOrderItem;
import com.ows.ufa.system.entity.ZsPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 缴费（订单） Mapper 接口
 * </p>
 *
 */
@Mapper
public interface ZsOrderItemMapper extends BaseMapper<ZsOrderItem> {

    public BigDecimal selectOrderAmountByPayTime(@Param(value = "planId") String planId,
                                                 @Param(value = "startPayTime") Date startPayTime,
                                                 @Param(value = "endPayTime") Date endPayTime);

}