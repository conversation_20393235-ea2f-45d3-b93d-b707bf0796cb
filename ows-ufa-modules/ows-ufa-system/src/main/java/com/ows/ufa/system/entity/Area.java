package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@TableName("t_area")
@Schema(description ="实体")
public class Area implements Serializable {

    private static final long serialVersionUID=1L;

    private Long id;

    private String name;

    private Long parentId;

    private String sequence;


}