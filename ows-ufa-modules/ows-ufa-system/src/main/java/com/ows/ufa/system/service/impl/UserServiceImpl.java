package com.ows.ufa.system.service.impl;

import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.entity.User;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.service.BaseSchoolDeptService;
import com.ows.ufa.system.vo.UserVO;
import com.ows.ufa.system.form.UserForm;
import com.ows.ufa.system.request.UserRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.UserMapper;
import com.ows.ufa.system.service.UserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.time.LocalDateTime;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 用户信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final BaseSchoolDeptService baseSchoolDeptServiceImpl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DataSource(value = DataSourceType.SLAVE)
    public UserVO findUser(Long id) {
        UserVO userVO = this.baseMapper.findUser(id);
        if (null == userVO) {
            return null;
        }

        return userVO;
    }
}