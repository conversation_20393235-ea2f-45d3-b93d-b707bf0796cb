package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="社团成员统计VO")
public class ClubMemberCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "社团成员数量")
    private Integer memberCount;

    @Schema(description = "性别比例:男")
    private Double maleRatio;

    @Schema(description = "性别比例:女")
    private Double femaleRatio;

}