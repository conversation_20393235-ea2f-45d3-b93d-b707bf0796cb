package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.BaseSchoolDeptService;
import com.ows.ufa.system.form.BaseSchoolDeptForm;
import com.ows.ufa.system.request.BaseSchoolDeptRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "baseSchoolDept")
@Tag(name = "baseSchoolDept", description = "院系接口")
public class BaseSchoolDeptController extends BaseController {

    private final BaseSchoolDeptService BaseSchoolDeptServiceImpl;

    @GetMapping("list")
    @Operation(summary = "院系列表")
    public AjaxResult listBaseSchoolDeptByPage() {
        return success(BaseSchoolDeptServiceImpl.queryBaseSchoolDepts());
    }

    @GetMapping("querySchools")
    @Operation(summary = "学校列表")
    public AjaxResult querySchools() {
        return success(BaseSchoolDeptServiceImpl.querySchools());
    }
}
