package com.ows.ufa.system.service;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.StringUtils;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.redis.service.RedisService;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.api.model.LoginUser;
import com.ows.ufa.system.domain.vo.ImageVO;
import com.ows.ufa.system.entity.AdmissionNotice;
import com.ows.ufa.system.entity.AreaCode;
import com.ows.ufa.system.entity.NoticeLog;
import com.ows.ufa.system.entity.TeacherResourcesApply;
import com.ows.ufa.system.enums.NoticeType;
import com.ows.ufa.system.form.AdmissionNoticeForm;
import com.ows.ufa.system.form.ReqBody;
import com.ows.ufa.system.util.RSAHelper;
import com.ows.ufa.system.vo.ApiResponse;
import com.ows.ufa.system.vo.StudentVO;
import lombok.RequiredArgsConstructor;
import org.apache.commons.codec.digest.DigestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class OpenService {
    private final AdmissionNoticeService admissionNoticeService;
    private final NoticeLogService noticeLogService;
    private final RestTemplate restTemplate;
    private final ImageService imageService;
    private final RedisService redisService;
    private final IAreaCodeService areaCodeService;
    private static final String EVENT_TOKEN_KEY = "event:token";
    private static final long TOKEN_EXPIRE_MINUTES = 8L;

    @Value("${yulaoai.url}")
    private String url;
    @Value("${yulaoai.publicKeyStr}")
    private String publicKeyStr;
    @Value("${yulaoai.privateKeyStr}")
    private String privateKeyStr;
    @Value("${projectImg}")
    private String projectUrl;

    @Value("${event.reportUrl}")
    private String eventReportUrl;

    @Value("${event.enable}")
    private boolean enable;

    @Value("${event.appCode}")
    private String appCode;

    @Value("${event.eventType}")
    private String eventType;

    @Value("${event.authKey}")
    private String authKey;

    @Value("${event.authSecret}")
    private String authSecret;

    @Value("${event.username}")
    private String username;

    @Value("${event.appSecret}")
    private String appSecret;

    @Value("${event.queryUrl}")
    private String queryUrl;

    @Value("${event.tokenUrl}")
    private String tokenUrl;

    @Value("${event.feedbackUrl}")
    private String feedbackUrl;

    @Value("${ykz.verifyToken}")
    private String verifyToken;

    @Value("${ykz.accessTokenUrl}")
    private String accessTokenUrl;

    @Value("${ykz.keyTokenByKeyUrl}")
    private String keyTokenByKeyUrl;

    @Value("${ykz.accountIdUrl}")
    private String accountIdUrl;

    @Value("${ykz.appKey}")
    private String ykzAppKey;

    @Value("${ykz.appSecret}")
    private String ykzAppSecret;

    @Value("${ykz.username}")
    private String ykzUsername;

    @Value("${ufa-mobile.speedLoginMobileByAuthCode}")
    private String speedLoginMobileByAuthCode;

    @Value("${ufa-mobile.getMobileLoginInfo}")
    private String getMobileLoginInfo;

    @Value("${ufa-mobile.userProfile}")
    private String userProfile;

    @Value("${ufa-mobile.editMobileUser}")
    private String editMobileUser;

    @Value("${ufa-mobile.url}")
    private String ufaMobileUrl;

    private static final Logger log = LoggerFactory.getLogger(OpenService.class);

    @Transactional(rollbackFor = Exception.class)
    public Boolean saveAdmissionNotice(String json) {
        log.info("通知书推送信息加密串{}", json);
        //记录日志
        List<AdmissionNotice> list = null;
        try {
            String decrypt = RSAHelper.decrypt(json, privateKeyStr);
            list = JSONArray.parseArray(decrypt, AdmissionNotice.class);
        } catch (Exception e) {
            throw new ServiceException("解密失败" + e.getMessage());
        }
        log.info("通知书推送信息:{}", list.size());
        boolean flag = false;
        if (null != list && !list.isEmpty()) {
            NoticeLog log = new NoticeLog();
            log.setNoticeType(NoticeType.PAYMENT.getCode());
            log.setContent(JSONArray.toJSONString(list));
            log.setCreateTime(LocalDateTime.now());
            noticeLogService.save(log);
            flag = admissionNoticeService.saveBatch(list);
        }
        log.info("通知书推送信息返回{}", flag);
        return flag;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean refundAdmissionNotice(String json) {
        log.info("通知书退费信息加密串{}", json);
        AdmissionNoticeForm vo = null;
        try {
            String decrypt = RSAHelper.decrypt(json, privateKeyStr);
            vo = JSONObject.parseObject(decrypt, AdmissionNoticeForm.class);
        } catch (Exception e) {
            throw new ServiceException("解密失败" + e.getMessage());
        }
        //记录日志
        log.info("通知书退费推送信息:{}", JSONObject.toJSONString(vo));
        NoticeLog log = new NoticeLog();
        log.setNoticeType(NoticeType.REFUND.getCode());
        log.setContent(JSONObject.toJSONString(vo));
        log.setCreateTime(LocalDateTime.now());
        noticeLogService.save(log);
        admissionNoticeService.refundAdmissionNotice(vo);
        return true;
    }

    public String findStudentStyle(String userId, Integer styleNo) {
        StudentVO student = getStudent(userId);
        if (null == student) {
            throw new ServiceException("用户不存在");
        }
        ImageVO imageVO = generateImageVO(userId, styleNo, student);
        String base64 = null;
        switch (styleNo) {
            case 1:
                base64 = imageService.generateImage1(imageVO);
                break;
            case 2:
                base64 = imageService.generateImage2(imageVO);
                break;
            case 3:
                base64 = imageService.generateImage3(imageVO);
                break;
            default:
                throw new ServiceException("不支持的学员风采样式");
        }
        if (null == base64) {
            throw new ServiceException("生成学员风采失败,请联系管理员处理");
        }
        return base64;
    }

    public ImageVO generateImageVO(String userId, Integer styleNo, StudentVO one) {
        ImageVO image = new ImageVO();
        image.setStudent(one);
        String imageUrl = "studentStyle/" + userId + "_" + styleNo + ".jpg";
        image.setUrl(imageUrl);
        image.setFileUrl(projectUrl + imageUrl);
        return image;
    }

    private StudentVO getStudent(String userId) {
        if (redisService.hasKey("student_" + userId)) {
            return redisService.getCacheObject("student_" + userId);
        }
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(url);
        // 创建RestTemplate实例
        // 准备请求体
        String requestBody = "{ \"userId\": \"" + userId + "\" }";

        // 设置HTTP头
        HttpHeaders headers = new HttpHeaders();
        headers.add("Referer", url);
        headers.setContentType(MediaType.APPLICATION_JSON);
        // 如果需要认证，可以在这里添加，例如：
        // headers.set("Authorization", "Bearer your_token_here");

        // 创建HttpEntity对象，包含请求体和头信息
        try {
            String encrypt = RSAHelper.encrypt(requestBody, publicKeyStr);
            String requestRQ = "{ \"json\": \"" + encrypt + "\" }";
            HttpEntity<String> entity = new HttpEntity<>(requestRQ, headers);
            // 发送POST请求
            ResponseEntity<ReqBody> response = restTemplate.postForEntity(builder.toUriString(), entity, ReqBody.class);
            log.info("获取getStudent,userId:{},result:{}", userId, JSONObject.toJSONString(response));
            // 处理响应
            if (response.getStatusCode().is2xxSuccessful()) {
                log.info("请求成功，响应内容: " + response.getBody());
                String decrypt = RSAHelper.decrypt(response.getBody().getJson(), privateKeyStr);
                ApiResponse res = JSONObject.parseObject(decrypt, ApiResponse.class);
                if (res.getCode() == 1) {
                    ObjectMapper objectMapper = new ObjectMapper();
                    objectMapper.configure(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES, true);
                    StudentVO studentVO = objectMapper.convertValue(res.getData(), StudentVO.class);
                    redisService.setCacheObject("student_" + userId, studentVO, 30l, TimeUnit.MINUTES);
                    return studentVO;
                }
            } else {
                log.error("请求失败，状态码: " + response.getStatusCodeValue());
                log.error("响应内容: " + response.getBody());
            }
        } catch (Exception e) {
            // 处理异常
            log.error("查询失败: " + e.getMessage());
        }
        return null;
    }

    public StudentVO queryUser(String userId) {
        StudentVO student = getStudent(userId);
        if (null == student) {
            throw new ServiceException("用户不存在");
        }
        return student;
    }

    private static final String AREA_CODE_CACHE_PREFIX = "area_code:";

    /**
     * 根据区域名称获取区域编码
     */
    private String getAreaCode(String areaName) {
        if (areaName == null || areaName.trim().isEmpty()) {
            return null;
        }
        // 先从缓存获取
        String cacheKey = AREA_CODE_CACHE_PREFIX + areaName;
        String areaCode = redisService.getCacheObject(cacheKey);
        if (areaCode != null) {
            return areaCode;
        }
        // 缓存未命中,从数据库查询
        LambdaQueryWrapper<AreaCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AreaCode::getAreaName, areaName.trim());
        AreaCode areaEntity = areaCodeService.getOne(queryWrapper);
        if (areaEntity != null) {
            // 写入缓存,24小时过期
            redisService.setCacheObject(cacheKey, areaEntity.getAreaCode(), 24L, TimeUnit.HOURS);
            return areaEntity.getAreaCode();
        }
        return null;
    }

    /**
     * 同步数据到三级治理中心
     *
     * @return
     */
    public String syncDataToCenter(TeacherResourcesApply apply, String areaCode) {
        try {
            String eventNum = generateEventNum();
            Map<String, Object> params = new HashMap<>();
            params.put("sourceSystemCode", appCode);
            params.put("eventNum", eventNum);
            params.put("eventTitle", apply.getTitle());
            params.put("eventType", eventType);
            params.put("eventContent", apply.getDescp());
            params.put("happenTime", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(apply.getCreateTime()));

            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (null == areaCode) {
                areaCode = getAreaCode(loginUser.getSysUser().getDept().getParentName());
            }
            if (areaCode != null) {
                params.put("belongCounty", areaCode);
            } else {
                log.error("区域编码不存在,机构名称={}", loginUser.getSysUser().getDept().getParentName());
                throw new ServiceException("区域编码不存在");
            }
            List<Map<String, Object>> taskOrderOutPuts = new ArrayList<>();
            Map<String, Object> taskOrderOutPut = new HashMap<>();
            taskOrderOutPut.put("taskCode", UUID.randomUUID().toString());
            taskOrderOutPut.put("taskName", "事件上报");
            taskOrderOutPut.put("taskOrderId", UUID.randomUUID().toString());
            List<Map<String, Object>> taskOrderOutputArray = new ArrayList<>();
            Map<String, Object> output = new HashMap<>();
            output.put("fieldCode", "startType");
            output.put("fieldName", "预警等级");
            List<String> fieldValue = new ArrayList<>();
            fieldValue.add(areaCode);
            output.put("fieldValue", fieldValue);
            taskOrderOutputArray.add(output);

            taskOrderOutPut.put("taskOrderOutputArray", taskOrderOutputArray);
            taskOrderOutPuts.add(taskOrderOutPut);
            params.put("taskOrderOutPuts", taskOrderOutPuts);
            // 构建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            String timestamp = String.valueOf(System.currentTimeMillis());
            String signContent = timestamp + appSecret;
            String sign = DigestUtils.sha256Hex(signContent);

            headers.set("appCode", appCode);
            headers.set("timestamp", timestamp);
            headers.set("signature", sign);
            log.info("事件ID：{}，报文：{}", eventNum, JSON.toJSONString(params));
            if (!enable) {
                log.info("三级治理中心开关已关闭，不上报事件,eventNum:{}", eventNum);
                return eventNum;
            }
            HttpEntity<Map<String, Object>> request = new HttpEntity<>(params, headers);
            ResponseEntity<String> response = restTemplate.postForEntity(
                    eventReportUrl,
                    request,
                    String.class
            );

            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("同步数据到三级治理中心失败: {}", response.getBody());
                throw new ServiceException("同步数据失败");
            }
            log.info("同步数据到三级治理中心成功，{}", response.getBody());
            return eventNum;
        } catch (Exception e) {
            log.error("同步数据到三级治理中心异常", e);
            throw new ServiceException("同步数据异常");
        }
    }

    private String generateEventNum() {
        String today = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String prefix = "DJDJYLAX" + today;

        // 使用Redis递增计数器，设置24小时过期
        String redisKey = "event_num:" + today.substring(0, 8);
        Long sequence = redisService.increment(redisKey);
        if (sequence == 1) {
            redisService.expire(redisKey, 24, TimeUnit.HOURS);
        }

        // 格式化6位序列号，不足前面补0
        return prefix + String.format("%06d", sequence);
    }

    /**
     * 获取认证token
     */
    private String getToken() {
        // 先从Redis获取token
        String token = redisService.getCacheObject(EVENT_TOKEN_KEY);
        if (token != null) {
            return token;
        }

        // Redis中没有token，重新获取
        try {
            Map<String, String> params = new HashMap<>();
            params.put("authKey", authKey);
            params.put("authSecret", authSecret);
            params.put("username", username);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<Map<String, String>> request = new HttpEntity<>(params, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(tokenUrl, request, String.class);

            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("获取token失败: {}", response.getStatusCode());
                return null;
            }
            JSONObject result = JSON.parseObject(response.getBody());
            token = result.getString("data");
            redisService.setCacheObject(EVENT_TOKEN_KEY, token, TOKEN_EXPIRE_MINUTES - 1, TimeUnit.MINUTES);
            return token;

        } catch (Exception e) {
            log.error("获取token异常", e);
            return null;
        }
    }

    /**
     * 查询事件处理记录
     *
     * @param eventNum   事件编号
     * @param logId      日志ID
     * @param updateTime 更新时间
     * @return 事件处理记录
     */
    public JSONObject queryEventLog(String eventNum, String logId, String updateTime) {
        try {
            // 获取token
            String token = getToken();
            if (token == null) {
                log.error("获取token失败");
                return null;
            }
            Map<String, String> params = new HashMap<>();
            params.put("eventNum", eventNum);
            params.put("logId", logId);
            params.put("updateTime", updateTime);

            // 构建请求头，添加token认证
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("API-TOKEN", token);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(params, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(queryUrl, request, String.class);
            if (!response.getStatusCode().is2xxSuccessful()) {
                log.error("调用事件查询接口失败: {}", response.getStatusCode());
                return null;
            }

            JSONObject result = JSON.parseObject(response.getBody());
            if (result == null) {
                log.error("事件查询接口返回错误: {}", response.getBody());
                return null;
            }

            return result;

        } catch (Exception e) {
            log.error("查询事件处理记录异常", e);
            return null;
        }
    }

    /**
     * 事件反馈
     *
     * @param feedback
     */
    public void sendFeedback(Map<String, Object> feedback) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signContent = timestamp + appSecret;
        String sign = DigestUtils.sha256Hex(signContent);

        headers.set("appCode", appCode);
        headers.set("timestamp", timestamp);
        headers.set("signature", sign);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(feedback, headers);

        ResponseEntity<String> response = restTemplate.postForEntity(feedbackUrl, request, String.class);

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new ServiceException("发送事件反馈失败: " + response.getBody());
        } else {
            log.info("发送事件反馈成功，{}", response.getBody());
        }
    }

    /**
     * 获取渝快政api-token
     *
     * @return
     */
    public String getYkzApiToken(){
        String url = keyTokenByKeyUrl;
        HttpHeaders headers = new HttpHeaders();
        Map<String,Object> body = new HashMap<>();
        body.put("authKey",ykzAppKey);
        body.put("authSecret",ykzAppSecret);
        body.put("username",ykzUsername);
        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
        ResponseEntity<Map> response = restTemplate.postForEntity(url,request,Map.class);
        log.info("getYkzApiToken:"+JSON.toJSONString(response));
        if(StringUtils.isNotEmpty(response.getBody())){
            Map resMap = response.getBody();
            Object code = resMap.get("code");
            if(ObjectUtil.isNotEmpty(code) && code.toString().equals("200")){
                //请求成功
                return resMap.get("data").toString();
            }else{
                throw new ServiceException("获取api-token失败："+JSON.toJSONString(response.getBody()));
            }
        }else{
            throw new ServiceException("获取api-token失败");
        }
    }

    /**
     * 获取渝快政accessToken
     *
     * @return
     */
    public Map<String,Object> getYkzAccessToken(String apiToken){
        String url = accessTokenUrl+"?secret="+ykzAppSecret;
        HttpHeaders headers = new HttpHeaders();
        String timestamp = String.valueOf(System.currentTimeMillis());

        headers.add("API-TOKEN", apiToken);
        headers.add("app-key", ykzAppKey);
        headers.add("request-id", timestamp);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(new HashMap<>(), headers);
        ResponseEntity<Map> response = restTemplate.exchange(url,HttpMethod.GET,request,Map.class);
        log.info("getYkzAccessToken:"+JSON.toJSONString(response));
        return getYkzResponseData(response);
    }

    /**
     * 校验token是否过期
     *
     * @return
     */
    public Map verifyToken(String token){
        String url = verifyToken+token;
        HttpHeaders headers = new HttpHeaders();

        headers.add("accept", "application/json, text/plain, */*");

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(new HashMap<>(), headers);
        try {
            ResponseEntity<Map> response = restTemplate.exchange(url, HttpMethod.GET, request, Map.class);
            log.info("verifyToken:" + JSON.toJSONString(response));
            return response.getBody();
        }catch (Exception e){
            //这里请求因为是http://xxxx/xxxx/xxx/token,所以token手写当位数不足时会返回404，所以这里同意返回false
            return null;
        }
    }

    /**
     * 获取渝快政UID
     *
     * @param token
     * @return
     */
    public Map<String,Object> getYkzUid(String token,String accessToken,String apiToken){
        String url = accountIdUrl+"?accessToken="+accessToken+"&token="+token;
        HttpHeaders headers = new HttpHeaders();
        String timestamp = String.valueOf(System.currentTimeMillis());

        headers.add("API-TOKEN", apiToken);
        headers.add("app-key", ykzAppKey);
        headers.add("request-id", timestamp);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(new HashMap<>(), headers);
        ResponseEntity<Map> response = restTemplate.
                exchange(url, HttpMethod.GET, request, Map.class);
        log.info("getYkzUid:"+JSON.toJSONString(response));
        return getYkzResponseData(response);
    }

    /**
     * 渝快政返回体解析
     * @param response
     * @return
     */
    private Map<String,Object> getYkzResponseData(ResponseEntity<Map> response){
        ObjectMapper mapper = new ObjectMapper();
        if(StringUtils.isNotEmpty(response.getBody())){
            Map resMap = response.getBody();
            Object code = resMap.get("code");
            if(ObjectUtil.isNotEmpty(code) && code.toString().equals("200")){
                //请求成功
                Map<String,Object> dataMap = mapper.convertValue(JSON.parse(resMap.get("data").toString()),Map.class);
                Object code1 = dataMap.get("code");
                if(ObjectUtil.isNotEmpty(code1) && code1.toString().equals("200")){
                    //请求成功
                    Map<String,Object> dataMap1 = mapper.convertValue(dataMap.get("data"),Map.class);
                    return dataMap1;
                }else{
                    throw new ServiceException("渝快政接口调用失败,"+JSON.toJSONString(response.getBody()));
                }
            }else{
                throw new ServiceException("渝快政接口调用失败,"+JSON.toJSONString(response.getBody()));
            }
        }else{
            throw new ServiceException("渝快政接口调用失败");
        }
    }


    /**
     * 根据code获取token接口
     *
     * @param authCode
     * @return
     */
    public String speedLoginMobileByAuthCode(String authCode) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("authCode", authCode);

            // 构建请求头，添加token认证
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(params, headers);

            String url = ufaMobileUrl + speedLoginMobileByAuthCode;

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            log.info("根据code获取token："+JSON.toJSONString(response));
            return response.getBody();
        } catch (Exception e) {
            log.error("根据code获取token接口异常", e);
            return null;
        }
    }

    /**
     * 根据token获取用户权限等信息
     *
     * @param token
     * @return
     */
    public Map<String,Object> getMobileLoginInfo(String token) {
        String url = ufaMobileUrl + getMobileLoginInfo;
        HttpHeaders headers = new HttpHeaders();
        headers.add("authorization", "Bearer " + token);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(new HashMap<>(), headers);
        ResponseEntity<Map> response = restTemplate.
                exchange(url, HttpMethod.GET, request, Map.class);
        log.info("根据token获取用户权限等信息："+JSON.toJSONString(response));
        return response.getBody();
    }

    /**
     * 查看用户信息
     *
     * @param token
     * @return
     */
    public Map<String,Object> userProfile(String token) {
        String url = ufaMobileUrl + userProfile;
        HttpHeaders headers = new HttpHeaders();
        headers.add("authorization", "Bearer " + token);

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(new HashMap<>(), headers);
        ResponseEntity<Map> response = restTemplate.
                exchange(url, HttpMethod.GET, request, Map.class);
        log.info("查看用户信息："+JSON.toJSONString(response));
        return response.getBody();
    }

    /**
     * 用户修改邮箱和昵称
     *
     * @param userId
     * @param email
     * @param nickName
     * @return
     */
    public String editMobileUser(String userId,String email,String nickName,String token) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("userId", userId);
            if(StringUtils.isNotEmpty(nickName)){
                params.put("nickName", nickName);
            }
            if(StringUtils.isNotEmpty(email)){
                params.put("email", email);
            }

            // 构建请求头，添加token认证
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("authorization","Bearer " + token);

            HttpEntity<Map<String, String>> request = new HttpEntity<>(params, headers);

            String url = ufaMobileUrl + editMobileUser;

            ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);
            log.info("用户修改邮箱和昵称接口："+JSON.toJSONString(response));
            return response.getBody();
        } catch (Exception e) {
            log.error("用户修改邮箱和昵称接口异常", e);
            return null;
        }
    }
}
