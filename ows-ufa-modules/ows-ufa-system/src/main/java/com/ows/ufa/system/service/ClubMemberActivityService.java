package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.ClubMemberActivity;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.vo.ClubMemberActivityVO;
import com.ows.ufa.system.form.ClubMemberActivityForm;
import com.ows.ufa.system.request.ClubMemberActivityRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.UserVO;
import com.ows.ufa.system.vo.count.ClubReviewCountVO;

import java.util.List;
/**
 * <p>
 * 社团成员活动信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface ClubMemberActivityService extends IService<ClubMemberActivity> {

    List<ClubMemberActivityVO> queryClubMemberActivitys(ClubMemberActivityRequest request);

    ClubMemberActivityVO findClubMemberActivity(Long id);

    Long saveClubMemberActivity(ClubMemberActivityForm form);

    boolean updateClubMemberActivity(ClubMemberActivityForm form);

    boolean removeClubMemberActivity(Long id);

    boolean review(ReviewForm form);

    ClubReviewCountVO countReview();

    Long joinActivity(Long id);

    boolean cancelActivity(Long id);

    List<ClubMemberActivityVO> myActivities(ClubMemberActivityRequest request);

}
