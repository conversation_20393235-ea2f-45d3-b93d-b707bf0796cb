package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.Questionnaire;
import com.ows.ufa.system.enums.QuestionnaireStatus;
import com.ows.ufa.system.vo.QuestionnaireVO;
import com.ows.ufa.system.form.QuestionnaireForm;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.QuestionnaireMapper;
import com.ows.ufa.system.service.QuestionnaireService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.system.vo.count.QuestionnaireCountVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.time.LocalDateTime;

import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 问卷调查信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class QuestionnaireServiceImpl extends ServiceImpl<QuestionnaireMapper, Questionnaire> implements QuestionnaireService {

    @Override
    public List<QuestionnaireVO> queryQuestionnaires(QuestionnaireRequest req) {
        /*LambdaQueryWrapper<Questionnaire> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(req.getTitle()), Questionnaire::getTitle, req.getTitle());
        queryWrapper.eq(null != req.getStatus(), Questionnaire::getStatus, req.getStatus());
        queryWrapper.in(Questionnaire::getDeptId, SecurityUtils.getAncestors());
        queryWrapper.orderByDesc(Questionnaire::getCreateTime);*/
        req.setAncestors(SecurityUtils.getAncestors());
        return this.baseMapper.queryQuestionnaires(req);
    }

    @Override
    public QuestionnaireVO findQuestionnaire(Long id) {
        Questionnaire entity = this.getById(id);
        QuestionnaireVO vo = (QuestionnaireVO) DataTransfer.transfer(entity, QuestionnaireVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveQuestionnaire(QuestionnaireForm form) {
        Questionnaire entity = (Questionnaire) DataTransfer.transfer(form, Questionnaire.class);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        entity.setInitiator(SecurityUtils.getThirdDeptName());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestionnaire(QuestionnaireForm form) {
        Questionnaire byId = this.getById(form.getId());
        if (null == byId) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (byId.getStatus() != QuestionnaireStatus.UNPUBLISHED.getCode()) {
            throw new ServiceException("该问卷调查信息已发布或已结束，无法修改");
        }
        Questionnaire entity = (Questionnaire) DataTransfer.transfer(form, Questionnaire.class);
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(SecurityUtils.getThirdUserid());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeQuestionnaire(Long id) {
        return this.lambdaUpdate().eq(Questionnaire::getId, id).set(Questionnaire::getDelFlag, DelFlag.DELETE.getCode()).update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean releaseQuestionnaire(QuestionnaireForm form) {
        Questionnaire byId = this.getById(form.getId());
        releaseValidate(byId);
        String deptIds = "";
        if (null != form.getFillPermissionDeptIds() && !form.getFillPermissionDeptIds().isEmpty()) {
            deptIds = DataTransfer.transferListToString(form.getFillPermissionDeptIds(),"&&&");
        }
        return this.lambdaUpdate().eq(Questionnaire::getId, byId.getId())
                .set(null != form.getFillPermissionDeptIds(), Questionnaire::getFillPermission, form.getFillPermission())
                .set(StringUtils.isNoneBlank(deptIds), Questionnaire::getFillPermissionDeptIds, deptIds)
                .set(Questionnaire::getStatus, QuestionnaireStatus.COLLECTING.getCode()).update();
    }

    private static void releaseValidate(Questionnaire byId) {
        if (null == byId) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (null == byId.getStartTime()) {
            throw new ServiceException("收集开始时间不能为空");
        }
        if (null == byId.getEndTime()) {
            throw new ServiceException("收集结束时间不能为空");
        }
        if (byId.getStatus() != QuestionnaireStatus.UNPUBLISHED.getCode()) {
            throw new ServiceException("该问卷调查信息已发布或已结束，无法发布");
        }
        if (byId.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("该问卷调查信息结束时间已过，无法发布");
        }
    }

    private static void releaseValidate(QuestionnaireForm byId) {
        if (null == byId) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (null == byId.getStartTime()) {
            throw new ServiceException("收集开始时间不能为空");
        }
        if (null == byId.getEndTime()) {
            throw new ServiceException("收集结束时间不能为空");
        }
        if (byId.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("该问卷调查信息结束时间已过，无法发布");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean stopQuestionnaire(Long id) {
        Questionnaire byId = this.getById(id);
        if (null == byId) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (byId.getStatus() != QuestionnaireStatus.COLLECTING.getCode()) {
            throw new ServiceException("该问卷调查信息未发布或已结束，无法停止");
        }
        if (byId.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("该问卷调查信息结束时间已过，不用停止");
        }
        return this.lambdaUpdate().eq(Questionnaire::getId, id).set(Questionnaire::getStatus, QuestionnaireStatus.ENDED.getCode()).update();
    }

    @Override
    public QuestionnaireCountVO listCount() {
        QuestionnaireCountVO count = new QuestionnaireCountVO();
        List<Questionnaire> list = this.lambdaQuery().select(Questionnaire::getStatus).eq(Questionnaire::getDelFlag, DelFlag.VALID.getCode())
                .in(Questionnaire::getDeptId, SecurityUtils.getAncestors()).list();
        count.setUnpublishedCount(list.stream().filter(item -> item.getStatus().equals(QuestionnaireStatus.UNPUBLISHED.getCode())).count());
        count.setCollectingCount(list.stream().filter(item -> item.getStatus().equals(QuestionnaireStatus.COLLECTING.getCode())).count());
        count.setEndedCount(list.stream().filter(item -> item.getStatus().equals(QuestionnaireStatus.ENDED.getCode())).count());
        return count;
    }

    @Override
    public boolean releaseAllQuestionnaire(QuestionnaireForm form) {
        releaseValidate(form);
        Questionnaire entity = (Questionnaire) DataTransfer.transfer(form, Questionnaire.class);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        entity.setInitiator(SecurityUtils.getThirdDeptName());
        entity.setStatus(QuestionnaireStatus.COLLECTING.getCode());
        return this.saveOrUpdate(entity);

    }

    @Override
    public List<QuestionnaireVO> queryOtherQuestionnaires(QuestionnaireRequest req) {
        return this.baseMapper.queryQuestionnaires(req);
    }

    @Override
    public Long createQuestionnaire(QuestionnaireForm form) {
        releaseValidate(form);
        Questionnaire entity = (Questionnaire) DataTransfer.transfer(form, Questionnaire.class);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt("第三方");
        entity.setDeptId("0");
        entity.setStatus(QuestionnaireStatus.COLLECTING.getCode());
        this.save(entity);
        return entity.getId();
    }

    @Override
    public boolean stopQuestionnaire(Long id, String appName) {
        Questionnaire byId = this.getById(id);
        if (null == byId || !appName.equals(byId.getAppName())) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (byId.getStatus() != QuestionnaireStatus.COLLECTING.getCode()) {
            throw new ServiceException("该问卷调查信息未发布或已结束，无法停止");
        }
        if (byId.getEndTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("该问卷调查信息结束时间已过，不用停止");
        }
        return this.lambdaUpdate().eq(Questionnaire::getId, id).set(Questionnaire::getStatus, QuestionnaireStatus.ENDED.getCode()).update();
    }

}