package com.ows.ufa.system.vo.excel;

import com.ows.ufa.common.core.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
public class ClubMemberExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Excel(name = "社团名称", type = Excel.Type.ALL)
    private String clubInfoName;

    @Excel(name = "姓名", type = Excel.Type.ALL)
    private String name;

    @Excel(name = "性别", readConverterExp = "0=男,1=女", type = Excel.Type.ALL)
    private Integer sex;

    @Excel(name = "联系电话", type = Excel.Type.ALL)
    private String phoneNumber;

    @Excel(name = "加入日期", dateFormat = "yyyy-MM-dd", type = Excel.Type.ALL)
    private LocalDateTime reviewDate;

}