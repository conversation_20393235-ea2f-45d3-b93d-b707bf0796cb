package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 生源学生表
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@Schema(description ="生源学生表VO")
public class BaseStudentForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "学员基础表-主键(UUID)")
    private String id;

    @Schema(description = "姓名")
    @NotNull(message = "姓名不能为空")
    private String stuName;

    @Schema(description = "性别[1男 2女]通过身份证获取")
    @NotNull(message = "性别[1男 2女]通过身份证获取不能为空")
    private String stuSex;

    @Schema(description = "身份证号")
    @NotNull(message = "身份证号不能为空")
    private String stuCard;

    @Schema(description = "政治面貌[1党员 2团员 3民主党派 4无党派 5群众]")
    @NotNull(message = "政治面貌[1党员 2团员 3民主党派 4无党派 5群众]不能为空")
    private String stuPolitical;

    @Schema(description = "联系电话")
    @NotNull(message = "联系电话不能为空")
    private String stuPhone;

    @Schema(description = "现居住地址")
    @NotNull(message = "现居住地址不能为空")
    private String stuAddr;

    @Schema(description = "健康状态能否坚持学校[0不能 1能]")
    @NotNull(message = "健康状态能否坚持学校[0不能 1能]不能为空")
    private String stuHealthState;

    @Schema(description = "原工作单位")
    @NotNull(message = "原工作单位不能为空")
    private String stuHisCompany;

    @Schema(description = "原岗位[1销售 2会计 3职员  9其他]")
    @NotNull(message = "原岗位[1销售 2会计 3职员  9其他]不能为空")
    private String stuHisPost;

    @Schema(description = "原职务[1经理 2部长 3局长 4处长 5主管 6科长 9其他]")
    @NotNull(message = "原职务[1经理 2部长 3局长 4处长 5主管 6科长 9其他]不能为空")
    private String stuHisJob;

    @Schema(description = "原职称[1初级 2中级 3高级]")
    @NotNull(message = "原职称[1初级 2中级 3高级]不能为空")
    private String stuHisTitle;

    @Schema(description = "居住情况[1与配偶居住 2与子女居住 3独居 9其他]")
    @NotNull(message = "居住情况[1与配偶居住 2与子女居住 3独居 9其他]不能为空")
    private String stuLiveState;

    @Schema(description = "文化程度[1初中以下 2初中至高中 3大专及以上]")
    @NotNull(message = "文化程度[1初中以下 2初中至高中 3大专及以上]不能为空")
    private String stuEduLevel;

    @Schema(description = "离退状态[1离休 2机关单位退休 3事业单位退休 4企业退休 5个体]")
    @NotNull(message = "离退状态[1离休 2机关单位退休 3事业单位退休 4企业退休 5个体]不能为空")
    private String stuRetireState;

    @Schema(description = "家人是否同意[0不同意 1同意]")
    @NotNull(message = "家人是否同意[0不同意 1同意]不能为空")
    private String isAgree;

    @Schema(description = "是否上过老年大学[0否 1是]")
    @NotNull(message = "是否上过老年大学[0否 1是]不能为空")
    private String isHaveElderCollege;

    @Schema(description = "是否有既往病史[0无 1有]")
    @NotNull(message = "是否有既往病史[0无 1有]不能为空")
    private String isHaveMedical;

    @Schema(description = "是否购买人身意外保险[0否 1是]")
    @NotNull(message = "是否购买人身意外保险[0否 1是]不能为空")
    private String isBuyInsure;

    @Schema(description = "是否红名单[0否 1是]")
    @NotNull(message = "是否红名单[0否 1是]不能为空")
    private String isRed;

    @Schema(description = "既往病史说明")
    @NotNull(message = "既往病史说明不能为空")
    private String medicalDesc;

    @Schema(description = "创建人")
    @NotNull(message = "创建人不能为空")
    private String createBy;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @NotNull(message = "修改人不能为空")
    private String updateBy;

    @Schema(description = "修改时间")
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime updateTime;

    @Schema(description = "状态[1正常]")
    @NotNull(message = "状态[1正常]不能为空")
    private String status;

    @Schema(description = "${field.comment}")
    @NotNull(message = "${field.comment}不能为空")
    private String stuCardType;

    @Schema(description = "${field.comment}")
    @NotNull(message = "${field.comment}不能为空")
    private String stuBirth;

}