package com.ows.ufa.system.mapper;

import java.util.List;
import com.ows.ufa.system.domain.SysNotice;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 通知公告表 数据层
 * 
 * <AUTHOR>
 */
public interface SysNoticeMapper
{
    /**
     * 查询公告信息
     * 
     * @param noticeId 公告ID
     * @return 公告信息
     */
    public SysNotice selectNoticeById(Long noticeId);

    /**
     * 查询公告列表
     * 
     * @param notice 公告信息
     * @return 公告集合
     */
    public List<SysNotice> selectNoticeList(SysNotice notice);

    /**
     * 新增公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    public int insertNotice(SysNotice notice);

    /**
     * 修改公告
     * 
     * @param notice 公告信息
     * @return 结果
     */
    public int updateNotice(SysNotice notice);

    /**
     * 批量删除公告
     * 
     * @param noticeId 公告ID
     * @return 结果
     */
    public int deleteNoticeById(Long noticeId);

    /**
     * 批量删除公告信息
     * 
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    public int deleteNoticeByIds(Long[] noticeIds);

    /**
     * 获取ai聊天记录
     * @param userId
     * @return
     */
    String getChatHistory(String userId);

    /**
     * 新增ai聊天记录
     * @param userId
     * @param content
     */
    void saveChatHistory(@Param("userId") String userId, @Param("content") String content);

    void insertChatHistory(@Param("userId") String userId, @Param("content") String content);

    Integer getChatHistoryCount(String userId);

}