package com.ows.ufa.system.datav.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.service.DataOverviewService;
import com.ows.ufa.system.service.DatavInitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "datav/init")
@Tag(name = "datav", description = "数据大屏初始接口")
public class DatavInitController extends BaseController {

    private final DatavInitService datavInitService;

    @GetMapping("getDistrictYkzUidByToken")
    @Operation(summary = "数据大屏愉快政token获取区县名称/市级分类名称")
    public AjaxResult getDistrictYkzUidByToken(@RequestParam(value = "token") String token) {
        return success(datavInitService.getDistrictYkzUidByToken(token));
    }

    @GetMapping("verifyToken")
    @Operation(summary = "校验token是否过期")
    public AjaxResult verifyToken(@RequestParam(value = "token") String token) {
        return success(datavInitService.verifyToken(token));
    }


}
