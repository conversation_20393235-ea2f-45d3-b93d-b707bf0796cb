package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="Request")
public class ClubInfoRequest implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "社团名称")
    private String clubName;

    @Schema(description = "社团简介")
    private String clubDescp;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private LocalDateTime beginTime;

    @Schema(name= "结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "申请开始时间")
    private LocalDateTime applicationBeginTime;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(name= "申请结束日期")
    private LocalDateTime applicationEndTime;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(hidden = true)
    private List<String> ancestors;

    @Schema(description = "h5查询条件")
    private String searchText;

    @Schema(description = "0-未招募;1-招募中")
    private Integer recruitmentStatus;
}