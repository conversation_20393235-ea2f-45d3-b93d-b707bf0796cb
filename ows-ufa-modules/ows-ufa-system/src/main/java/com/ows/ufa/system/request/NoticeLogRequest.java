package com.ows.ufa.system.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知书推送信息日志记录表
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Schema(description ="通知书推送信息日志记录表Request")
public class NoticeLogRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "推送类型:0-缴费;1-退费")
    private Integer noticeType;

    @Schema(description = "JSON内容")
    private String content;

    @Schema(description = "时间")
    private LocalDateTime createTime;

}