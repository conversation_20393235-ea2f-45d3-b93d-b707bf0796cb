package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.ows.ufa.common.core.annotation.Sensitive;
import com.ows.ufa.common.core.enums.SensitiveType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 备份师资资源
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@TableName("t_backup_teacher")
@Schema(description ="备份师资资源实体")
public class BackupTeacher implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "身份证号")
    private String tchCard;

    @Schema(description = "姓名")
    private String tchName;

    @Schema(description = "学历")
    private String tchEdu;

    @Schema(description = "手机号")
    @Sensitive(type = SensitiveType.PHONE,cipherField = "tchPhoneEncrypt")
    private String tchPhone;

    @Schema(description = "加密手机号")
    private String tchPhoneEncrypt;

    @Schema(description = "家庭住址地区ID")
    private String tchAddrAreaId;

    @Schema(description = "家庭住址")
    private String tchAddr;

    @Schema(description = "家庭详细住址")
    private String tchAddrDetail;

    @Schema(description = "毕业学校")
    private String tchGraduatedSchool;

    @Schema(description = "教师头像路径")
    private String headPath;

    @Schema(description = "教师简介")
    private String tchDesc;

    @Schema(description = "工号")
    private String tchNo;

    @Schema(description = "录用日期")
    private LocalDateTime tchEmployDate;

    @Schema(description = "工作年限")
    private Integer tchWorkTime;

    @Schema(description = "院系ID")
    private String deptId;

    @Schema(description = "教学类型")
    private String deptName;

    @Schema(description = "在职状态")
    private String tchWorkState;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "师资来源:0-自主新增;1-区县教委")
    private Integer source;

    @Schema(description = "状态:0-未推送;1-已推送")
    private Integer status;

    @Schema(description = "删除标志:0-删除;1-有效")
    private Integer delFlag;
}