package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Schema(description ="VO")
public class BaseTeacherForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "教师表")
    private String id;

    @Schema(description = "教师名称")
    @NotNull(message = "教师名称不能为空")
    private String tchName;

    @Schema(description = "身份证号")
    @NotNull(message = "身份证号不能为空")
    private String tchCard;

    @Schema(description = "生日")
    @NotNull(message = "生日不能为空")
    private String tchBirthday;

    @Schema(description = "性别")
    @NotNull(message = "性别不能为空")
    private String tchSex;

    @Schema(description = "手机号")
    @NotNull(message = "手机号不能为空")
    private String tchPhone;

    @Schema(description = "家庭住址")
    @NotNull(message = "家庭住址不能为空")
    private String tchAddr;

    @Schema(description = "毕业学校")
    @NotNull(message = "毕业学校不能为空")
    private String tchGraduatedSchool;

    @Schema(description = "学历")
    @NotNull(message = "学历不能为空")
    private String tchEdu;

    @Schema(description = "创建人")
    @NotNull(message = "创建人不能为空")
    private String createBy;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @NotNull(message = "修改人不能为空")
    private String updateBy;

    @Schema(description = "修改时间")
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime updateTime;

    @Schema(description = "状态")
    @NotNull(message = "状态不能为空")
    private String status;

}