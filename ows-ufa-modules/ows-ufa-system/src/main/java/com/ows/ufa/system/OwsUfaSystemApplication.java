package com.ows.ufa.system;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import com.ows.ufa.common.security.annotation.EnableCustomConfig;
import com.ows.ufa.common.security.annotation.EnableRyFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 系统模块
 * 
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableRyFeignClients
@SpringBootApplication
@EnableScheduling
public class OwsUfaSystemApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(OwsUfaSystemApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  系统模块启动成功   ლ(´ڡ`ლ)ﾞ;");
    }
}
