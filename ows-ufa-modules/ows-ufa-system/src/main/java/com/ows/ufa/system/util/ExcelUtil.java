package com.ows.ufa.system.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ows.ufa.system.entity.QuestionnaireSubmit;
import com.ows.ufa.system.vo.QuestionnaireRadioVO;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

public class ExcelUtil {
    private static final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * 将问卷调查信息导出到 HttpServletResponse
     *
     * @param questionnaireSubmits 用户填报的内容列表
     * @param response             HttpServletResponse 对象
     * @throws IOException 如果发生 I/O 错误
     */
    public void exportToExcel(HttpServletResponse response, List<QuestionnaireSubmit> questionnaireSubmits, List<QuestionnaireRadioVO> titles) {
        Workbook workbook = new XSSFWorkbook(); // 使用 XSSFWorkbook 支持 .xlsx 格式
        Sheet sheet = workbook.createSheet("问卷情况");
        try {
            if (questionnaireSubmits == null || questionnaireSubmits.isEmpty()) {
                // 如果没有数据，创建一个空行并写入提示信息
                Row emptyRow = sheet.createRow(0);
                Cell cell = emptyRow.createCell(0);
                cell.setCellValue("暂无数据");
                workbook.write(response.getOutputStream());
                workbook.close();
                return;
            }

            // 获取所有可能的标题（去重）

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            Cell cell1 = headerRow.createCell(0);
            cell1.setCellValue("提交人");
            Cell cell2 = headerRow.createCell(1);
            cell2.setCellValue("部门");
            int colNum = 2;
            for (QuestionnaireRadioVO title : titles) {
                Cell cell = headerRow.createCell(colNum++);
                cell.setCellValue(title.getName());
            }

            // 填充数据行
            int rowNum = 1;
            for (QuestionnaireSubmit submit : questionnaireSubmits) {
                Row row = sheet.createRow(rowNum++);
                Cell cell3 = row.createCell(0);
                cell3.setCellValue(submit.getUserName());
                Cell cell4 = row.createCell(1);
                cell4.setCellValue(submit.getDeptName());
                colNum = 2;
                for (QuestionnaireRadioVO title : titles) {
                    Cell cell = row.createCell(colNum++);
                    List<QuestionnaireRadioVO> contentJson = JSONArray.parseArray(submit.getContentJson(), QuestionnaireRadioVO.class);
                    Object value = getValueByTitle(contentJson, title.getId());
                    cell.setCellValue(value != null ? value.toString() : "");
                }
            }

            // 自动调整列宽
            for (int i = 0; i < titles.size(); i++) {
                sheet.autoSizeColumn(i);
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
//            response.setHeader("Content-Disposition", "attachment; filename=questionnaires.xlsx");

            // 将 Workbook 写入响应输出流
            workbook.write(response.getOutputStream());
            workbook.close();
        } catch (Exception e) {
            log.error("导出Excel异常{}", e.getMessage());
        } finally {
            IOUtils.closeQuietly(workbook);
        }

    }

    /**
     * 根据标题获取对应的值
     *
     * @param contentJson 用户填报的内容列表
     * @return 对应的值，如果不存在则返回 null
     */
    private String getValueByTitle(List<QuestionnaireRadioVO> contentJson, Long titleId) {
        QuestionnaireRadioVO radio = contentJson.stream()
                .filter(item -> item.getId().equals(titleId))
                .findFirst().get();
        Object object = radio.getValue();
        if(null==object){
            return "";
        }
        if (radio.getType().equalsIgnoreCase("DragDivider")) {
            return "";
        }
        if("DragUploadFile".equalsIgnoreCase(radio.getType())
                || "DragUploadImg".equalsIgnoreCase(radio.getType())){
            JSONArray jsons = (JSONArray) radio.getValue();
            String result="";
            for (Object json : jsons) {
                JSONObject jsonObject = (JSONObject) json;
                result+= jsonObject.getString("url")+";";
            }
            return result;
        }
        if (null == object || StringUtils.isBlank(object.toString())) {
            return "";
        }
        if (object instanceof String) {
            String s = (String) object;
            return "*other*".equals(s) ? "其他(" + radio.getOtherDesc() + ")" : s;
        } else if (object instanceof Integer) {
            return (Integer) object + "";
        } else if (object instanceof Collection<?>) {
            Collection<?> collection = (Collection<?>) object;
            if (radio.getType().equalsIgnoreCase("DragAddress")) {
                return collection.stream()
                        .filter(String.class::isInstance)
                        .map(String.class::cast)
                        .collect(Collectors.toList()).stream()
                        .map(s -> "*other*".equals(s) ? "其他(" + radio.getOtherDesc() + ")" : s)
                        .collect(Collectors.joining("/")) + radio.getOtherDesc();
            }
            return collection.stream()
                    .filter(String.class::isInstance)
                    .map(String.class::cast)
                    .collect(Collectors.toList()).stream()
                    .map(s -> "*other*".equals(s) ? "其他(" + radio.getOtherDesc() + ")" : s)
                    .collect(Collectors.joining(";"));
        }
        return "无法识别，请联系管理员!";
    }
}
