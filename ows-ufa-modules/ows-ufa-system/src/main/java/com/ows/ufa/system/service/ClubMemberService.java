package com.ows.ufa.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.entity.ClubMember;
import com.ows.ufa.system.form.ClubMemberForm;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.vo.ClubMemberVO;
import com.ows.ufa.system.vo.UserVO;
import com.ows.ufa.system.vo.count.ClubMemberCountVO;
import com.ows.ufa.system.vo.count.ClubReviewCountVO;

import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
public interface ClubMemberService extends IService<ClubMember> {

    List<ClubMemberVO> queryClubMembers(ClubMemberRequest request);

    List<ClubMemberVO> queryClubMemberReviews(ClubMemberRequest request);

    ClubMemberVO findClubMember(Long id);

    Long saveClubMember(ClubMemberForm form);

    boolean updateClubMember(ClubMemberForm form);

    boolean removeClubMember(Long id);

    ClubMemberCountVO countClubMember();

    boolean review(ReviewForm form);

    ClubReviewCountVO countReview();

    Long joinClub(UserVO user,Long clubId);

    List<ClubMemberVO> myClubInfos(ClubMemberRequest request);

    public String updatePhone();
}
