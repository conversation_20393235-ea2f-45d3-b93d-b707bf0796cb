package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 缴费订单信息表
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@TableName("t_admission_notice")
@Schema(description ="缴费订单信息表实体")
public class AdmissionNotice implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "缴费订单编号")
    private String orderNo;

    @Schema(description = "缴费时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    @Schema(description = "学员姓名")
    private String userName;

    @Schema(description = "学校图片")
    private String logUrl;

    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "系名称")
    private String deptName;

    @Schema(description = "专业名称")
    private String majorName;

    @Schema(description = "课程名称")
    private String courseName;

    @Schema(description = "退费时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;

    @Schema(description = "状态:0-缴费;1-退费")
    private Integer status;

    @Schema(description = "状态:0-未读;1-已读")
    private Integer readStatus;

    @Schema(description = "通知书图片地址")
    private String noticeUrl;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "开课时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime courseStartTime;

    @Schema(description = "学校地址")
    private String schoolAddr;


}