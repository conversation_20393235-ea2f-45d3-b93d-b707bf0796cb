package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 标签人员表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@Schema(description ="标签人员表VO")
public class AbilityLabelUserForm implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "能力标签ID")
    @NotNull(message = "能力标签ID不能为空")
    private Long abilityId;

    @Schema(description = "标签ID")
    @NotNull(message = "标签ID不能为空")
    private Long labelId;

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private String userId;

}