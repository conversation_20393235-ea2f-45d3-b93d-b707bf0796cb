package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.QuestionnaireTemplateService;
import com.ows.ufa.system.form.QuestionnaireTemplateForm;
import com.ows.ufa.system.request.QuestionnaireTemplateRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 问卷调查信息模板表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "questionnaireTemplate")
@Tag(name = "questionnaireTemplate", description = "问卷调查信息模板表接口")
public class QuestionnaireTemplateController extends BaseController {

    private final QuestionnaireTemplateService QuestionnaireTemplateServiceImpl;

    @GetMapping("list")
    @Operation(summary = "问卷调查信息模板表查询")
    public AjaxResult listQuestionnaireTemplate(QuestionnaireTemplateRequest request) {
        return success(QuestionnaireTemplateServiceImpl.queryQuestionnaireTemplates(request));
    }

    @GetMapping("{id}")
    @Operation(summary = "问卷调查信息模板表查询详情")
    public AjaxResult findQuestionnaireTemplate(@PathVariable Long id) {
        return success(QuestionnaireTemplateServiceImpl.findQuestionnaireTemplate(id));
    }

    @PostMapping
    @Operation(summary = "问卷调查信息模板表新增数据")
    public AjaxResult saveQuestionnaireTemplate(@RequestBody QuestionnaireTemplateForm form) {
        return success(QuestionnaireTemplateServiceImpl.saveQuestionnaireTemplate(form));
    }

    @PostMapping("update")
    @Operation(summary = "问卷调查信息模板表修改数据")
    public AjaxResult updateQuestionnaireTemplate(@RequestBody QuestionnaireTemplateForm form) {
        return success(QuestionnaireTemplateServiceImpl.updateQuestionnaireTemplate(form));
    }

    @PostMapping("delete")
    @Operation(summary = "问卷调查信息模板表删除数据")
    public AjaxResult removeQuestionnaireTemplate(@RequestBody IdRequest id) {
        return success(QuestionnaireTemplateServiceImpl.removeQuestionnaireTemplate(id.getId()));
    }
}
