package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.AbilityLabel;
import com.ows.ufa.system.entity.AbilityLabelInfo;
import com.ows.ufa.system.vo.AbilityLabelVO;
import com.ows.ufa.system.form.AbilityLabelForm;
import com.ows.ufa.system.request.AbilityLabelRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 * 能力标签表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
public interface AbilityLabelService extends IService<AbilityLabel> {

    List<AbilityLabelVO> queryAbilityLabels(AbilityLabelRequest request);

    AbilityLabelVO findAbilityLabel(Long id);

    Long saveAbilityLabel(AbilityLabelForm form);

    boolean updateAbilityLabel(AbilityLabelForm form);

    boolean removeAbilityLabel(Long id);

    List<AbilityLabelInfo> queryLabelInfos();
}
