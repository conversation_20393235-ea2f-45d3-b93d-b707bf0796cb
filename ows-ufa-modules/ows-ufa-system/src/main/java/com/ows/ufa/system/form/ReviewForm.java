package com.ows.ufa.system.form;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="审核VO")
public class ReviewForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团ID")
    private List<Long> ids;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

}