package com.ows.ufa.system.h5.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.form.ClubMemberForm;
import com.ows.ufa.system.request.ClubMemberActivityRequest;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubMemberActivityService;
import com.ows.ufa.system.service.ClubMemberService;
import com.ows.ufa.system.service.UserService;
import com.ows.ufa.system.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "h5/clubMember")
@Tag(name = "h5clubMember", description = "h5社团成员接口")
public class H5ClubMemberController extends BaseController {

    private final ClubMemberService ClubMemberServiceImpl;
    private final ClubMemberActivityService ClubMemberActivityServiceImpl;
    private final UserService userServiceImpl;

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubMemberByPage(ClubMemberRequest request) {
        startPage();
        return success(getDataTable(ClubMemberServiceImpl.queryClubMembers(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "查询详情")
    public AjaxResult findClubMember(@PathVariable Long id) {
        return success(ClubMemberServiceImpl.findClubMember(id));
    }

    @PostMapping
    @Operation(summary = "新增数据")
    public AjaxResult saveClubMember(@RequestBody ClubMemberForm form) {
        return success(ClubMemberServiceImpl.saveClubMember(form));
    }

    @PostMapping("update")
    @Operation(summary = "修改数据")
    public AjaxResult updateClubMember(@RequestBody ClubMemberForm form) {
        return success(ClubMemberServiceImpl.updateClubMember(form));
    }

    @PostMapping("delete")
    @Operation(summary = "删除数据")
    public AjaxResult removeClubMember(@RequestBody IdRequest id) {
        return success(ClubMemberServiceImpl.removeClubMember(id.getId()));
    }

    @PostMapping("joinClub")
    @Operation(summary = "加入社团")
    public AjaxResult joinClub(@RequestBody IdRequest id) {
        UserVO user = userServiceImpl.findUser(Long.parseLong(SecurityUtils.getThirdUserid()));
        return success(ClubMemberServiceImpl.joinClub(user,id.getId()));
    }

    @PostMapping("joinActivity")
    @Operation(summary = "加入活动")
    public AjaxResult joinActivity(@RequestBody IdRequest id) {
        return success(ClubMemberActivityServiceImpl.joinActivity(id.getId()));
    }

    @PostMapping("cancelActivity")
    @Operation(summary = "取消活动申请")
    public AjaxResult cancelActivity(@RequestBody IdRequest id) {
        return success(ClubMemberActivityServiceImpl.cancelActivity(id.getId()));
    }

    @GetMapping("myClubInfos")
    @Operation(summary = "我的社团分页查询")
    public AjaxResult myClubInfos(ClubMemberRequest request) {
        startPage();
        return success(getDataTable(ClubMemberServiceImpl.myClubInfos(request)));
    }

    @GetMapping("myClubInfo/{id}")
    @Operation(summary = "我的社团详情")
    public AjaxResult myClubInfoById(@PathVariable Long id) {
        return success(ClubMemberServiceImpl.findClubMember(id));
    }

    @GetMapping("myActivities")
    @Operation(summary = "我的活动分页查询")
    public AjaxResult listClubMemberByPage(ClubMemberActivityRequest request) {
        startPage();
        return success(getDataTable(ClubMemberActivityServiceImpl.myActivities(request)));
    }

    @GetMapping("myActivity/{id}")
    @Operation(summary = "我的活动详情")
    public AjaxResult myActivityById(@PathVariable Long id) {
        return success(ClubMemberActivityServiceImpl.findClubMemberActivity(id));
    }
}
