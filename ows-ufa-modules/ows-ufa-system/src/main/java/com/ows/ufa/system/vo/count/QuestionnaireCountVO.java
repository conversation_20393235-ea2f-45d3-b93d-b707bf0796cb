package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="问卷调查信息列表统计VO")
public class QuestionnaireCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "收集中数量")
    private long unpublishedCount;

    @Schema(description = "未发布数量")
    private long collectingCount;

    @Schema(description = "已结束数量")
    private long endedCount;


}