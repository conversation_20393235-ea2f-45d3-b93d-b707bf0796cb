package com.ows.ufa.system.service.impl;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.system.form.UpdateEventLog;
import com.ows.ufa.system.mapper.UpdateEventLogMapper;
import com.ows.ufa.system.service.UpdateEventLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class UpdateEventLogServiceImpl extends ServiceImpl<UpdateEventLogMapper, UpdateEventLog> implements UpdateEventLogService {
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addUpdateEventLog(UpdateEventLog event) {
        log.info("收到事件更新回调数据：{}", JSON.toJSONString(event));
        return this.save(event);
    }
} 