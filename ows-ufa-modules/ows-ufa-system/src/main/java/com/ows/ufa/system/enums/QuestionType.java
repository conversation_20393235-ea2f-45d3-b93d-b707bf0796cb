package com.ows.ufa.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum QuestionType implements IEnum<String> {
    //状态:0-未推送;1-已推送
    SINGLE_CHOICE("DragRadio","单选"),
    MULTIPLE_CHOICE("DragCheckBox","多选"),
    DROP_DOWN("DragSelect","下拉");
    QuestionType(String code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final String code;
    @JsonValue
    private final String descp;

    @Override
    public String getValue() {
        return this.code;
    }

}
