package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.AbilityLabelService;
import com.ows.ufa.system.form.AbilityLabelForm;
import com.ows.ufa.system.request.AbilityLabelRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 能力标签表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "abilityLabel")
@Tag(name = "abilityLabel", description = "能力标签表接口")
public class AbilityLabelController extends BaseController {

    private final AbilityLabelService AbilityLabelServiceImpl;

    @GetMapping("list")
    @Operation(summary = "能力标签表分页查询")
    public AjaxResult listAbilityLabelByPage(AbilityLabelRequest request) {
        startPage();
        TableDataInfo dataTable = getDataTable(AbilityLabelServiceImpl.queryAbilityLabels(request));
        dataTable.setTotal(dataTable.getRows().size());
        return success(dataTable);
    }

    @GetMapping("queryLabels")
    @Operation(summary = "能力标签级联下拉列表查询")
    public AjaxResult queryLabels() {
        return success(AbilityLabelServiceImpl.queryAbilityLabels(new AbilityLabelRequest()));
    }

    @GetMapping("queryLabelInfos")
    @Operation(summary = "能力标签下拉列表查询")
    public AjaxResult queryLabelInfos() {
        return success(AbilityLabelServiceImpl.queryLabelInfos());
    }

    @GetMapping("{id}")
    @Operation(summary = "能力标签表查询详情")
    public AjaxResult findAbilityLabel(@PathVariable Long id) {
        return success(AbilityLabelServiceImpl.findAbilityLabel(id));
    }

    @PostMapping
    @Operation(summary = "能力标签表新增数据")
    public AjaxResult saveAbilityLabel(@RequestBody AbilityLabelForm form) {
        return success(AbilityLabelServiceImpl.saveAbilityLabel(form));
    }

    @PostMapping("update")
    @Operation(summary = "能力标签表修改数据")
    public AjaxResult updateAbilityLabel(@RequestBody AbilityLabelForm form) {
        return success(AbilityLabelServiceImpl.updateAbilityLabel(form));
    }

    @PostMapping("delete")
    @Operation(summary = "能力标签表删除数据")
    public AjaxResult removeAbilityLabel(@RequestBody IdRequest id) {
        return success(AbilityLabelServiceImpl.removeAbilityLabel(id.getId()));
    }
}
