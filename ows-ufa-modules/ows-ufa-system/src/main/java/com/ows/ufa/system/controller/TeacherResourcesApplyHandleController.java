package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.request.TeacherResourcesApplyHandleRequest;
import com.ows.ufa.system.service.TeacherResourcesApplyHandleService;
import com.ows.ufa.system.vo.TeacherResourcesApplyHandleVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 师资资源申请处理流程表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "teacherResourcesApplyHandle")
@Tag(name = "师资资源申请处理流程接口", description = "师资资源申请处理流程接口")
public class TeacherResourcesApplyHandleController extends BaseController {

    private final TeacherResourcesApplyHandleService TeacherResourcesApplyHandleServiceImpl;

    @GetMapping("list")
    @Operation(summary = "师资资源申请处理流程表分页查询")
    public TableDataInfo listTeacherResourcesApplyHandleByPage(TeacherResourcesApplyHandleRequest request) {
        startPage();
        return getDataTable(TeacherResourcesApplyHandleServiceImpl.queryTeacherResourcesApplyHandles(request));
    }

    @GetMapping("{id}")
    @Operation(summary = "师资资源申请处理流程表查询详情")
    public AjaxResult findTeacherResourcesApplyHandle(@PathVariable Long id) {
        return success(TeacherResourcesApplyHandleServiceImpl.findTeacherResourcesApplyHandle(id));
    }

    @PostMapping
    @Operation(summary = "师资资源申请处理流程表新增数据")
    public AjaxResult saveTeacherResourcesApplyHandle(@RequestBody TeacherResourcesApplyHandleVO vo) {
        return success(TeacherResourcesApplyHandleServiceImpl.saveTeacherResourcesApplyHandle(vo));
    }

    @PostMapping("update")
    @Operation(summary = "师资资源申请处理流程表修改数据")
    public AjaxResult updateTeacherResourcesApplyHandle(@RequestBody TeacherResourcesApplyHandleVO vo) {
        return success(TeacherResourcesApplyHandleServiceImpl.updateTeacherResourcesApplyHandle(vo));
    }

    @PostMapping("delete")
    @Operation(summary = "师资资源申请处理流程表删除数据")
    public AjaxResult removeTeacherResourcesApplyHandle(@RequestBody IdRequest id) {
        return success(TeacherResourcesApplyHandleServiceImpl.removeTeacherResourcesApplyHandle(id.getId()));
    }
}
