package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 问卷调查信息填报表
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@TableName("t_questionnaire_submit")
@Schema(description ="问卷调查信息填报表实体")
public class QuestionnaireSubmit implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "问卷调查ID")
    private Long questionnaireId;

    @Schema(description = "提交用户名")
    private String userName;

    @Schema(description = "提交部门名")
    private String deptName;

    @Schema(description = "问卷填写内容")
    private String contentJson;

    @Schema(description = "删除标志：0-删除；1-有效")
    private Integer delFlag;

    @Schema(description = "数据权限部门id")
    private String deptId;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


}