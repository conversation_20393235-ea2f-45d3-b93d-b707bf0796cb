package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.AbilityLabelInfo;
import com.ows.ufa.system.vo.AbilityLabelInfoVO;
import com.ows.ufa.system.form.AbilityLabelInfoForm;
import com.ows.ufa.system.request.AbilityLabelInfoRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 * 标签信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
public interface AbilityLabelInfoService extends IService<AbilityLabelInfo> {

    List<AbilityLabelInfo> queryAbilityLabelInfos(AbilityLabelInfoRequest request);

    AbilityLabelInfoVO findAbilityLabelInfo(Long id);

    Long saveAbilityLabelInfo(AbilityLabelInfoForm form);

    boolean updateAbilityLabelInfo(AbilityLabelInfoForm form);

    boolean removeAbilityLabelInfo(Long id);
}
