package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.ClubActivities;
import com.ows.ufa.system.entity.ClubMemberActivity;
import com.ows.ufa.system.enums.DelFlag;
import com.ows.ufa.system.enums.ReviewStatus;
import com.ows.ufa.system.form.ClubActivitiesForm;
import com.ows.ufa.system.mapper.ClubActivitiesMapper;
import com.ows.ufa.system.mapper.ClubInfoMapper;
import com.ows.ufa.system.request.ClubActivitiesRequest;
import com.ows.ufa.system.service.ClubActivitiesService;
import com.ows.ufa.system.service.ClubMemberActivityService;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.ClubActivitiesVO;
import com.ows.ufa.system.vo.count.ClubActivityCountVO;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class ClubActivitiesServiceImpl extends ServiceImpl<ClubActivitiesMapper, ClubActivities> implements ClubActivitiesService {

    private final ClubMemberActivityService clubMemberActivityServiceImpl;
    private final ClubInfoMapper clubInfoMapper;

    @Override
    public List<ClubActivitiesVO> queryClubActivitiess(ClubActivitiesRequest request) {
        request.setStatus(ReviewStatus.PASS.getCode());
        request.setAncestors(SecurityUtils.getAncestors());
        return this.baseMapper.queryClubActivitiess(request);
    }

    @Override
    public List<ClubActivitiesVO> h5queryClubActivitiess(ClubActivitiesRequest request) {
        request.setAncestors(SecurityUtils.getAncestors());
        List<ClubActivitiesVO> clubActivitiesVOS = this.baseMapper.queryClubActivitiess(request);
       /* clubActivitiesVOS.sort(Comparator.comparingInt(vo -> {
            switch (vo.getProgressStatus()) {
                case 2: return 1; // 报名中 - 最高
                case 3: return 2; // 进行中
                case 1: return 3; // 未开始
                case 4: return 4; // 已结束
                default: return 5; // 其他状态
            }
        }));*/
        return clubActivitiesVOS;
    }

    @Override
    public ClubActivitiesVO findClubActivities(Long id) {
        ClubActivities entity = this.getById(id);
        ClubActivitiesVO vo = (ClubActivitiesVO) DataTransfer.transfer(entity, ClubActivitiesVO.class);
        vo.setClubInfoName(clubInfoMapper.selectById(entity.getClubInfoId()).getClubName());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveClubActivities(ClubActivitiesForm form) {
        checkTime(form);
        ClubActivities entity = (ClubActivities) DataTransfer.transfer(form, ClubActivities.class);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        entity.setApplicationDate(LocalDateTime.now());
        entity.setStatus(ReviewStatus.PASS.getCode());
        entity.setReviewAt(SecurityUtils.getThirdUserid());
        entity.setReviewDate(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateAt(SecurityUtils.getThirdUserid());
        this.save(entity);
        return entity.getId();
    }

    private static void checkTime(ClubActivitiesForm form) {

        // 检查时间字段是否为 null
        if (form.getRegistrationTime() == null) {
            throw new ServiceException("报名开始时间不能为空;");
        }
        if (form.getRegistrationDeadline() == null) {
            throw new ServiceException("报名截止时间不能为空;");
        }
        if (form.getStartTime() == null) {
            throw new ServiceException("活动开始时间不能为空;");
        }
        if (form.getEndTime() == null) {
            throw new ServiceException("活动结束时间不能为空;");
        }

        // 检查时间逻辑
        if (form.getRegistrationTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("报名开始时间不能小于当前时间");
        }
        if (form.getRegistrationTime().isAfter(form.getRegistrationDeadline())) {
            throw new ServiceException("报名截止时间不能大于活动开始时间");
        }
        if (form.getStartTime().isBefore(form.getRegistrationDeadline())) {
            throw new ServiceException("活动开始时间不能早于报名截止时间");
        }
        if (form.getStartTime().isAfter(form.getEndTime())) {
            throw new ServiceException("活动开始时间不能大于活动结束时间");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateClubActivities(ClubActivitiesForm form) {
        ClubActivities byId = this.getById(form.getId());
        if (byId == null) {
            throw new ServiceException("数据不存在");
        }
        if (byId.getRegistrationTime().isBefore(LocalDateTime.now())) {
            throw new ServiceException("活动已经开始，无法修改");
        }
        checkTime(form);
        ClubActivities entity = (ClubActivities) DataTransfer.transfer(form, ClubActivities.class);
        entity.setUpdateTime(LocalDateTime.now());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeClubActivities(Long id) {
        ClubActivities byId = this.getById(id);
        if (byId == null) {
            throw new ServiceException("数据不存在");
        }
        if (!(byId.getRegistrationTime().isAfter(LocalDateTime.now()) || byId.getEndTime().isBefore(LocalDateTime.now()))) {
            throw new ServiceException("活动进行中，无法删除");
        }
        clubMemberActivityServiceImpl.lambdaUpdate().eq(ClubMemberActivity::getClubActivityId, id).set(ClubMemberActivity::getDelFlag, DelFlag.DELETE.getCode()).update();
        return this.lambdaUpdate().eq(ClubActivities::getId, id).set(ClubActivities::getDelFlag, DelFlag.DELETE.getCode()).update();
    }

    @Override
    public ClubActivityCountVO countClubActivity() {
        return this.baseMapper.countClubActivity(SecurityUtils.getAncestors());
    }

    @Override
    public ClubActivitiesVO h5findClubActivities(Long id) {
        ClubActivitiesVO vo = this.baseMapper.findClubActivities(id);
        List<ClubMemberActivity> list = clubMemberActivityServiceImpl.lambdaQuery().eq(ClubMemberActivity::getClubActivityId, id)
                .eq(ClubMemberActivity::getClubMemberId, SecurityUtils.getThirdUserid())
                .orderByDesc(ClubMemberActivity::getCreateTime)
                .eq(ClubMemberActivity::getDelFlag, DelFlag.VALID.getCode()).list();
        if(!list.isEmpty()){
           vo.setClubMemberActivity(list.get(0));
        }
        return vo;
    }
}