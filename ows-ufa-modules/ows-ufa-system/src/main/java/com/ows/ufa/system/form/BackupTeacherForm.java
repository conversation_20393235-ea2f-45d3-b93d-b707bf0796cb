package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 备份师资资源
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Schema(description ="备份师资资源VO")
public class BackupTeacherForm implements Serializable {

    private static final long serialVersionUID=1L;

    private Long id;

    @Schema(description = "姓名")
    @NotNull(message = "姓名不能为空")
    private String tchName;

    @Schema(description = "手机号")
    @NotNull(message = "手机号不能为空")
    private String tchPhone;

    @Schema(description = "家庭住址地区ID")
    private String tchAddrAreaId;

    @Schema(description = "家庭住址")
    @NotNull(message = "家庭住址不能为空")
    private String tchAddr;

    @Schema(description = "教学类型")
    @NotNull(message = "教学类型不能为空")
    private String deptName;

    @Schema(description = "师资来源:0-自主新增;1-区县教委")
    private Integer source;
}