package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="VO")
public class ClubActivitiesForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团ID")
    @NotNull(message = "社团ID不能为空")
    private Long clubInfoId;

    @Schema(description = "活动名称")
    @NotNull(message = "活动名称不能为空")
    private String activityName;

    @Schema(description = "活动地点")
    @NotNull(message = "活动地点不能为空")
    private String activityLocation;

    @Schema(description = "报名时间")
    @NotNull(message = "报名时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registrationTime;

    @Schema(description = "截止报名时间")
    @NotNull(message = "截止报名时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registrationDeadline;

    @Schema(description = "开始时间")
    @NotNull(message = "开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "学员报名是否需要审核:0-否;1-是")
    @NotNull(message = "学员报名是否需要审核:0-否;1-是不能为空")
    private Integer needApproval;

    @Schema(description = "活动介绍")
    @NotNull(message = "活动介绍不能为空")
    private String activityDescp;

    @Schema(description = "附件")
    @NotNull(message = "附件不能为空")
    private String attachmentUrl;

}