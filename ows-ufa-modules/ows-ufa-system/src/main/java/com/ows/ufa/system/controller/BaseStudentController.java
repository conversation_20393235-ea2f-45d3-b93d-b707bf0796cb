package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.BaseStudentService;
import com.ows.ufa.system.form.BaseStudentForm;
import com.ows.ufa.system.request.BaseStudentRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 生源学生表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "baseStudent")
@Tag(name = "baseStudent", description = "生源学生表接口")
public class BaseStudentController extends BaseController {

    private final BaseStudentService BaseStudentServiceImpl;

    @GetMapping("list")
    @Operation(summary = "生源学生表分页查询")
    public AjaxResult listBaseStudentByPage(BaseStudentRequest request) {
        startPage();
        return success(BaseStudentServiceImpl.queryBaseStudents(request));
    }

}
