package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.ows.ufa.system.entity.Questionnaire;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 问卷调查信息填报表
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@Schema(description ="问卷调查信息填报表VO")
public class QuestionnaireSubmitDetailVO implements Serializable {

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "问卷调查ID")
    private Long questionnaireId;

    @Schema(description = "提交用户名")
    private String userName;

    @Schema(description = "提交部门名")
    private String deptName;

    @Schema(description = "问卷填写内容")
    private String contentJson;

    private Questionnaire questionnaire;
}