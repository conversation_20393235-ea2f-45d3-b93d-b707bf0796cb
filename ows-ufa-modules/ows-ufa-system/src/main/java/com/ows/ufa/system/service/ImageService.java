package com.ows.ufa.system.service;

import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.system.domain.vo.ImageVO;
import com.ows.ufa.system.entity.AdmissionNotice;
import com.ows.ufa.system.util.DateUtils;
import com.ows.ufa.system.vo.CourseVO;
import com.ows.ufa.system.vo.StudentVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.font.LineBreakMeasurer;
import java.awt.font.TextAttribute;
import java.awt.font.TextLayout;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.AttributedString;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.Base64;
import java.util.Date;
import java.util.UUID;

@Slf4j
@Service
public class ImageService {

    private static final int MAX_WIDTH = 530; // 最大文本宽度
    private static final float custom_no_font_size = 26f; // 小字体
    private static final float custom_font_size = 30f; // 中号字体
    private static final float custom_bold_font_size = 36f; // 大号字体
    private static final int MAX_CONTENT_WIDTH = 500; // 最大文本宽度
    private static Font customFont = null;
    private static Font customRegularFont = null;
    private static Font customXinWeiFont = null;

    @PostConstruct
    public void init() {
        // 初始化代码
        customRegularFont = loadCustomRegularFont();
        customFont = loadCustomFont();
        customXinWeiFont = loadXinWeiFont();
    }

    public String generateLetterImage(ImageVO vo) {
        log.info("开始生成录取通知书");
        try {
            String basePath = "templates/letter";
            InputStream templateInputStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/bg.png");
            BufferedImage templateImage = ImageIO.read(templateInputStream);
            // 创建一个新的BufferedImage对象，用于存储添加内容后的图片
            BufferedImage image = new BufferedImage(templateImage.getWidth(), templateImage.getHeight(), BufferedImage.TYPE_INT_ARGB);
            // 获取Graphics2D对象以便绘制
            Graphics2D g2d = image.createGraphics();
            // 在新图片上绘制模板图片
            g2d.drawImage(templateImage, 0, 0, null);
            float alpha = 1f;
            AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
            g2d.setComposite(alphaComposite);
            InputStream headStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/head.png");
            BufferedImage headImage = ImageIO.read(headStream);
            if (null != headImage) {
                g2d.drawImage(headImage, 117, 127, 515, 100, null);
            }
            AdmissionNotice notice = vo.getAdmissionNotice();
            InputStream logoStream = null;
            if("重庆市老年大学".equals(notice.getSchoolName())){
                logoStream = getLogoStream(null);
            }else{
                logoStream = getLogoStream(notice.getLogUrl());
            }

            if (logoStream != null) {
                BufferedImage logoImage = ImageIO.read(logoStream);
                if (logoImage != null) {
                    BufferedImage transparentLogo = removeWhiteBackground(logoImage);
                    g2d.drawImage(transparentLogo, 295, 378, 160, 160, null);
                }
            }
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            // 设置编号
            g2d.setFont(customXinWeiFont.deriveFont(Font.BOLD, 46f));
            // 绘制文本
            int y = 575;
            g2d.setColor(new Color(218, 37, 28));
            drawCenteredText(g2d, notice.getSchoolName(), 0, y, templateImage.getWidth(), -6);
            InputStream titleStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/title.png");
            BufferedImage titleImage = ImageIO.read(titleStream);
            if (null != titleImage) {
                g2d.drawImage(titleImage, 177, 660, 391, 75, null);
            }
            g2d.setColor(Color.BLACK);
            g2d.setFont(customFont.deriveFont(Font.PLAIN, custom_font_size));
            FontMetrics fontMetrics = g2d.getFontMetrics(g2d.getFont());
            drawText(g2d, notice.getUserName() + "：", 104, 840, fontMetrics);
            g2d.setFont(customRegularFont.deriveFont(Font.PLAIN, custom_no_font_size));
            fontMetrics = g2d.getFontMetrics(g2d.getFont());
            String text1 = notice.getSchoolName() + "-" + notice.getDeptName() + "-" + notice.getMajorName() + "-" + notice.getCourseName();
            y = drawText(g2d, "         祝贺您被" + text1 + "录取。", 104, 920, fontMetrics);

            String startTime = notice.getCourseStartTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            String payTime = notice.getPayTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));

            y = drawText(g2d, "请您根据课程安排，于上课当日来学校进行报道。", 104, y, fontMetrics);
            drawText(g2d, "学校地点:" + notice.getSchoolAddr() + "。", 104, y, fontMetrics);
            drawTextRightAligned(g2d, notice.getSchoolName(), 630, 1302, fontMetrics);
            drawTextRightAligned(g2d, payTime, 630, 1347, fontMetrics);
            g2d.dispose();
            log.info("生成录取通知书成功");
            return convertImageToBase64(image, "png");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成录取通知书失败,msg:{}", e.getMessage());
            return null;
        }
    }

    private BufferedImage removeWhiteBackground(BufferedImage image) {
       /* BufferedImage result = new BufferedImage(image.getWidth(), image.getHeight(), BufferedImage.TYPE_INT_ARGB);
        Graphics2D g = result.createGraphics();
        // 设置抗锯齿
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        // 创建一个颜色阈值，去除接近白色的颜色
        for (int y = 0; y < image.getHeight(); y++) {
            for (int x = 0; x < image.getWidth(); x++) {
                int rgba = image.getRGB(x, y);
                Color color = new Color(rgba, true);
                // 如果不是接近白色，则保留原色，否则设置为透明
                if (!(color.getRed() > 240 && color.getGreen() > 240 && color.getBlue() > 240)) {
                    result.setRGB(x, y, rgba);
                } else {
                    result.setRGB(x, y, 0x00FFFFFF & rgba); // 设置为透明
                }
            }
        }
        g.dispose();*/
        return image;
    }

    public String generateImage1(ImageVO vo) {
        log.info("开始生成学员风采1");
        try {
            String basePath = "templates/introduce1";
            InputStream templateInputStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/bg.png");
            BufferedImage templateImage = ImageIO.read(templateInputStream);
            // 创建一个新的BufferedImage对象，用于存储添加内容后的图片
            BufferedImage image = new BufferedImage(templateImage.getWidth(), templateImage.getHeight(), BufferedImage.TYPE_INT_ARGB);

            // 获取Graphics2D对象以便绘制
            Graphics2D g2d = image.createGraphics();
            // 在新图片上绘制模板图片
            g2d.drawImage(templateImage, 0, 0, null);
            float alpha = 1f;
            AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
            g2d.setComposite(alphaComposite);
            InputStream avtarBgStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/avtar_bg.png");
            BufferedImage avtarBgImage = ImageIO.read(avtarBgStream);
            int bgWidth = 419;
            int bgHeight = 441;
            int bgx = 179;
            int bgy = 304;
            if (null != avtarBgImage) {
                g2d.drawImage(avtarBgImage, 179, 304, 419, 441, null);
            }
            InputStream avtarStream = getAvtarStream(vo.getStudent());
            if (avtarStream != null) {
                BufferedImage avtarImage = ImageIO.read(avtarStream);
                int width = 333;
                int height = 394;
                int avtarX = bgx + (bgWidth - width) / 2;
                int avtarY = bgy + (bgHeight - height) / 2;
                BufferedImage transparentAvtar = removeWhiteBackground(avtarImage);
                g2d.drawImage(transparentAvtar, avtarX, avtarY, width, height, null);
            }
            InputStream avtarBorderStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/avtar_border.png");
            BufferedImage avtarBorderImage = ImageIO.read(avtarBorderStream);
            if (null != avtarBorderImage) {
                g2d.drawImage(avtarBorderImage, 121, 292, 511, 456, null);
            }
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            // 设置编号
            g2d.setFont(customFont.deriveFont(Font.PLAIN, custom_bold_font_size));
            // 绘制文本
            int y = 860;
            g2d.setColor(new Color(31, 110, 105));
            g2d.setFont(customFont.deriveFont(Font.PLAIN, 60f));
            drawCenteredText(g2d, vo.getStudent().getUserName(), 0, y, templateImage.getWidth(), 8);

            g2d.setColor(Color.BLACK);
            g2d.setFont(customRegularFont.deriveFont(Font.PLAIN, custom_font_size));
            FontMetrics fontMetrics = g2d.getFontMetrics(g2d.getFont());
            y = 905;
            y = drawText(g2d, generateText1(vo.getStudent()), 55, y, fontMetrics, 650, 10);
            drawText(g2d, generateText2(vo.getStudent()), 55, y, fontMetrics, 650, 10);
            g2d.dispose();
            log.info("生成学员风采1成功");
            return convertImageToBase64(image, "png");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成学员风采1失败,msg:{}", e.getMessage());
            return null;
        }
    }

    public String generateImage2(ImageVO vo) {
        log.info("开始生成学员风采2");
        try {
            String basePath = "templates/introduce2";
            InputStream templateInputStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/bg.png");
            BufferedImage templateImage = ImageIO.read(templateInputStream);
            // 创建一个新的BufferedImage对象，用于存储添加内容后的图片
            BufferedImage image = new BufferedImage(templateImage.getWidth(), templateImage.getHeight(), BufferedImage.TYPE_INT_ARGB);

            // 获取Graphics2D对象以便绘制
            Graphics2D g2d = image.createGraphics();
            // 在新图片上绘制模板图片
            g2d.drawImage(templateImage, 0, 0, null);
            float alpha = 1f;
            AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
            g2d.setComposite(alphaComposite);
            InputStream avtarBgStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/avtar_bg.png");
            BufferedImage avtarBgImage = ImageIO.read(avtarBgStream);
            int bgx = 80;
            int bgy = 153;
            int bgWidth = 602;
            int bgHeight = 602;
            if (null != avtarBgImage) {
                g2d.drawImage(avtarBgImage, 80, 153, bgWidth, bgHeight, null);
            }
            InputStream avtarStream = getAvtarStream(vo.getStudent());
            BufferedImage avtarImage = ImageIO.read(avtarStream);
            int width = 333;
            int height = 394;
            int avtarX = bgx + (bgWidth - width) / 2;
            int avtarY = bgy + (bgHeight - height) / 2;
            if (null != avtarImage) {
                g2d.drawImage(avtarImage, avtarX, avtarY, width, height, null);
            }

            InputStream lineStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/line.png");
            BufferedImage lineImage = ImageIO.read(lineStream);
            if (null != lineImage) {
                g2d.drawImage(lineImage, 45, 809, 661, 3, null);
            }
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            // 设置编号
            g2d.setFont(customFont.deriveFont(Font.PLAIN, custom_bold_font_size));
            // 绘制文本
            int y = 808;
            g2d.setColor(Color.white);
            g2d.setFont(customFont.deriveFont(Font.PLAIN, 50f));
            FontMetrics fontMetrics = g2d.getFontMetrics(g2d.getFont());
            if (StringUtils.isNotEmpty(vo.getStudent().getUserName())) {
                String name = vo.getStudent().getUserName();
                InputStream nameBgStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/name_bg.png");
                BufferedImage nameBgImage = ImageIO.read(nameBgStream);
                if (null != nameBgImage) {
                    int x = (templateImage.getWidth() - 80 * name.length()) / 2;
                    for (int i = 0; i < name.length(); i++) {
                        g2d.drawImage(nameBgImage, x, y - 40, 80, 80, null);
                        drawText(g2d, name.substring(i, i + 1), x + (80 - g2d.getFont().getSize()) / 2, y - fontMetrics.getHeight() / 2+10, fontMetrics, 800, 0);
                        x += 80;
                    }
                }
            }

            InputStream contentBgStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/content_bg.png");
            BufferedImage contentBgImage = ImageIO.read(contentBgStream);
            if (null != contentBgImage) {
                g2d.drawImage(contentBgImage, 26, 877, 698, 693, null);
            }

            g2d.setColor(Color.BLACK);
            g2d.setFont(customRegularFont.deriveFont(Font.PLAIN, custom_font_size));
            y = 930;
            y = drawText(g2d, generateText1(vo.getStudent()), 80, y, fontMetrics, 600, 10);
            drawText(g2d, generateText2(vo.getStudent()), 80, y, fontMetrics, 600, 10);
            g2d.dispose();
            log.info("生成学员风采2成功");
            return convertImageToBase64(image, "png");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成学员风采2失败,msg:{}", e.getMessage());
            return null;
        }
    }

    public String generateImage3(ImageVO vo) {
        log.info("开始生成学员风采3 ");
        try {
            String basePath = "templates/introduce3";
            InputStream templateInputStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/bg.png");
            BufferedImage templateImage = ImageIO.read(templateInputStream);
            // 创建一个新的BufferedImage对象，用于存储添加内容后的图片
            BufferedImage image = new BufferedImage(templateImage.getWidth(), templateImage.getHeight(), BufferedImage.TYPE_INT_ARGB);

            // 获取Graphics2D对象以便绘制
            Graphics2D g2d = image.createGraphics();
            // 在新图片上绘制模板图片
            g2d.drawImage(templateImage, 0, 0, null);
            float alpha = 1f;
            AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
            g2d.setComposite(alphaComposite);
            InputStream avtarBgStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/avtar_bg.png");
            BufferedImage avtarBgImage = ImageIO.read(avtarBgStream);
            int bgx = 172;
            int bgy = 101;
            int bgWidth = 431;
            int bgHeight = 495;
            if (null != avtarBgImage) {
                g2d.drawImage(avtarBgImage, 172, 101, 431, 495, null);
            }
            InputStream avtarStream = getAvtarStream(vo.getStudent());
            ;
            BufferedImage avtarImage = ImageIO.read(avtarStream);
            int width = 333;
            int height = 394;
            int avtarX = bgx + (bgWidth - width) / 2;
            int avtarY = bgy + (bgHeight - height) / 2;
            if (null != avtarImage) {
                g2d.drawImage(avtarImage, avtarX, avtarY, width, height, null);
            }
            InputStream avtarBorderStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/avtar_border.png");
            BufferedImage avtarBorderImage = ImageIO.read(avtarBorderStream);
            if (null != avtarBorderImage) {
                g2d.drawImage(avtarBorderImage, 112, 101, 526, 501, null);
            }

            InputStream headStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/head.png");
            BufferedImage headImage = ImageIO.read(headStream);
            if (null != headImage) {
                g2d.drawImage(headImage, 0, 304, 750, 615, null);
            }

            InputStream contentBgStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/content_bg.png");
            BufferedImage contentBgImage = ImageIO.read(contentBgStream);
            if (null != contentBgImage) {
                g2d.drawImage(contentBgImage, 25, 652, 700, 936, null);
            }
            g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            // 设置编号
            g2d.setFont(customFont.deriveFont(Font.PLAIN, custom_bold_font_size));
            // 绘制文本
            int y = 738;
            g2d.setColor(Color.white);
            g2d.setFont(customFont.deriveFont(Font.PLAIN, 50f));
            FontMetrics fontMetrics = g2d.getFontMetrics(g2d.getFont());
            if (StringUtils.isNotEmpty(vo.getStudent().getUserName())) {
                String name = vo.getStudent().getUserName();
                InputStream nameBgStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/name_bg.png");
                BufferedImage nameBgImage = ImageIO.read(nameBgStream);
                if (null != nameBgImage) {
                    int x = (templateImage.getWidth() - 80 * name.length()) / 2;
                    for (int i = 0; i < name.length(); i++) {
                        g2d.drawImage(nameBgImage, x, y - 40, 80, 80, null);
                        drawText(g2d, name.substring(i, i + 1), x + (80 - g2d.getFont().getSize()) / 2, y - fontMetrics.getHeight() / 2+10, fontMetrics, 800, 0);
                        x += 80;
                    }
                }
            }

            g2d.setColor(Color.BLACK);
            g2d.setFont(customRegularFont.deriveFont(Font.PLAIN, custom_font_size));
            y = 805;
            y = drawText(g2d, generateText1(vo.getStudent()), 65, y, fontMetrics, 620, 10);
            drawText(g2d, generateText2(vo.getStudent()), 65, y, fontMetrics, 620, 10);
            g2d.dispose();
            log.info("生成学员风采3成功");
            return convertImageToBase64(image, "png");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("生成学员风采3失败,msg:{}", e.getMessage());
            return null;
        }
    }

    private String generateText1(StudentVO student) {
        if (student.getCourseList().size() == 1) {
            CourseVO course = student.getCourseList().get(0);
            String startTime = DateUtils.parseDateToStr("yyyy年MM月dd日", course.getPayTime()); //course.getPayTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            String text = "您于" + startTime + "进入" + course.getSchoolName() + "" + course.getDeptName() + "-" + course.getMajorName() + "-" + course.getCourseName() + "学习";
            String text2 = "。";
            if (StringUtils.isNotBlank(course.getClassPost())) {
                text2 = "，担任" + course.getClassPost() + "职务。";
            }
            return text + text2;
        } else {
            CourseVO first = student.getCourseList().get(0);
            String startTime = DateUtils.parseDateToStr("yyyy年MM月dd日", first.getPayTime());//first.getPayTime().format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
            String text = "您于" + startTime + "起，进入" + first.getSchoolName() + "学习多个课程，包括";
            int count = 0;
            for (int i = 0; i < student.getCourseList().size(); i++) {
                CourseVO course = student.getCourseList().get(i);
                String text1 = course.getDeptName() + "-" + course.getMajorName() + "-" + course.getCourseName();
                if (text1.contains("null")) {
                    continue;
                }
                String text2 = "，";
                /*if (count == 2 || count == student.getCourseList().size() - 1 || i == student.getCourseList().size() - 1) {
                    text2 = "。";
                }*/
                if (StringUtils.isNotBlank(course.getClassPost())) {
                    text2 = "，担任" + course.getClassPost() + "职务。";
                }
                if ((text + text1 + text2).length() > 107) {
                    text = text.substring(0, text.length() - 1) + "......";
                    break;
                }else{
                    if(i == student.getCourseList().size() - 1){
                        text2 = text2.substring(0, text2.length() - 1) + "。";
                    }
                    text = text + text1 + text2;
                }
                count++;
            }

            return text;
        }
    }

    private String generateText2(StudentVO student) {
        String text = "学习期间，保持老骥伏枥、老当益壮的健康心态和进取精神，自觉践行“弘德重教 博学康乐”的校训，弘扬“求知康乐 团结奉献”的校风，遵守学校规章制度，勤奋刻苦学习知识，积极参加各项活动，传播正能量、展现新风采，争做有作为、有进步、有快乐的新时代风范长者。";
        return text;
    }

    private InputStream getAvtarStream(StudentVO student) {
        InputStream avtarStream = null;
        if (StringUtils.isNotBlank(student.getUserFilePath())) {
            try {
                avtarStream = getInputStreamFromUrl(student.getUserFilePath());
            } catch (Exception e) {
                log.error("获取头像失败，msg:{}", e.getMessage());
            }
        }
        if (null == avtarStream) {
            String basePath = "templates/introduce1";
            if (student.getUserSex().equalsIgnoreCase("1")) {
                avtarStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/male.png");
            } else {
                avtarStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/female.png");
            }
        }
        return avtarStream;
    }

    private InputStream getLogoStream(String logUrl) {
        InputStream avtarStream = null;
        if (StringUtils.isNotBlank(logUrl)) {
            try {
                avtarStream = getInputStreamFromUrl(logUrl);
            } catch (Exception e) {
                log.error("获取学校logo失败，msg:{}", e.getMessage());
            }
        }
        if (null == avtarStream) {
            String basePath = "templates/letter";
            avtarStream = ImageService.class.getClassLoader().getResourceAsStream(basePath + "/logo.png");
        }
        return avtarStream;
    }


    private static InputStream getInputStreamFromUrl(String urlString) throws Exception {
        URL url = new URL(escapeChineseInUrl(urlString));
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(5000); // 设置连接超时时间（单位：毫秒）
        connection.setReadTimeout(5000); // 设置读取超时时间（单位：毫秒）

        int responseCode = connection.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) {
            return connection.getInputStream();
        } else {
            throw new ServiceException("网络请求失败，响应码：" + responseCode);
        }
    }

    public static String escapeChineseInUrl(String url) throws UnsupportedEncodingException {
        StringBuilder escapedUrl = new StringBuilder();
        for (char c : url.toCharArray()) {
            if (isChineseCharacter(c)) {
                escapedUrl.append(URLEncoder.encode(String.valueOf(c), "UTF-8"));
            } else {
                escapedUrl.append(c);
            }
        }
        return escapedUrl.toString();
    }

    private static boolean isChineseCharacter(char c) {
        // 中文字符的Unicode范围
        return c >= 0x4E00 && c <= 0x9FFF;
    }

    private Font loadCustomFont() {
        try {
            return Font.createFont(Font.TRUETYPE_FONT, ImageService.class.getClassLoader().getResourceAsStream("fonts/SourceHanSansCN-Bold.otf"));
        } catch (IOException | FontFormatException e) {
            log.error("loadCustomFont字体加载失败,{}", e.getMessage());
            return new Font("Source Han Sans CN Bold", Font.BOLD, (int) custom_font_size);
        }
    }

    private static Font loadXinWeiFont() {
        try {
            return Font.createFont(Font.TRUETYPE_FONT, ImageService.class.getClassLoader().getResourceAsStream("fonts/STXINWEI.TTF"));
        } catch (IOException | FontFormatException e) {
            log.error("loadXinWeiFont字体加载失败,{}", e.getMessage());
            return new Font("Source Han Sans CN Bold", Font.BOLD, (int) custom_font_size);
        }
    }

    private Font loadCustomRegularFont() {
        try {
            return Font.createFont(Font.TRUETYPE_FONT, ImageService.class.getClassLoader().getResourceAsStream("fonts/SourceHanSansCN-Regular.otf"));
        } catch (IOException | FontFormatException e) {
            // 如果字体加载失败，可以使用默认字体
            log.error("loadCustomRegularFont字体加载失败,{}", e.getMessage());
            return new Font("Source Han Sans CN Regular", Font.BOLD, (int) custom_font_size);
        }
    }


    private void drawTextRightAligned(Graphics2D g2d, String text, int x, int y, FontMetrics fontMetrics) {
        // 计算文本的宽度
        int textWidth = fontMetrics.stringWidth(text);
        // 计算文本的起始x坐标，以实现右对齐
        int startX = x - textWidth;
        // 绘制文本
        g2d.drawString(text, startX, y);
    }

    private void drawCenteredText(Graphics2D g2d, String text, int x, int y, int width, float charSpacing) {
        FontMetrics fontMetrics = g2d.getFontMetrics();
        int totalTextWidth = 0;
        for (int i = 0; i < text.length(); i++) {
            totalTextWidth += fontMetrics.charWidth(text.charAt(i)) + charSpacing;
        }
        totalTextWidth -= charSpacing; // 减去最后一个字符的额外间距

        int textX = x + (width - totalTextWidth) / 2;
        int currentX = textX;

        for (int i = 0; i < text.length(); i++) {
            g2d.drawString(String.valueOf(text.charAt(i)), currentX, y);
            currentX += fontMetrics.charWidth(text.charAt(i)) + charSpacing;
        }
    }

    /**
     * 编码文件名+时间
     */
    public String generatePNGName() {
        return UUID.randomUUID().toString().replace("-", "") + "renders" + new SimpleDateFormat("yyyyMMdd").format(new Date()) + ".png";
    }

    private byte[] bufferedImageToBytes(BufferedImage image, String formatName) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ImageIO.write(image, formatName, outputStream);
        return outputStream.toByteArray();
    }

    private int drawText(Graphics2D g2d, String text, int x, int y, FontMetrics fontMetrics) throws Exception {
        return drawText(g2d, text, x, y, fontMetrics, MAX_WIDTH, 30);
    }

    private int drawText(Graphics2D g2d, String text, int x, int y, FontMetrics fontMetrics, int maxWidth, int height) throws Exception {
        int lineHeight = fontMetrics.getHeight();
        if (StringUtils.isEmpty(text)) {
            return y + lineHeight + height;
        }
        int maxLineWidth = maxWidth;
        AttributedString attributedString = new AttributedString(text);
        attributedString.addAttribute(TextAttribute.FONT, g2d.getFont());
        LineBreakMeasurer lineBreakMeasurer = new LineBreakMeasurer(attributedString.getIterator(), g2d.getFontRenderContext());
        int currentY = y;
        while (lineBreakMeasurer.getPosition() < text.length()) {
            TextLayout layout = lineBreakMeasurer.nextLayout(maxLineWidth);
            currentY += layout.getAscent();
            layout.draw(g2d, x, currentY);
            currentY += layout.getDescent() + layout.getLeading() + height;
        }
        return currentY;
    }

    private int drawColoredText2(Graphics2D g2d, String fullText, int startX, int startY, FontMetrics fontMetrics,
                                 int maxWidth, int beginLength, int projectLength, int middleLength, int amountLength, Font customFont, Font customRegularFont) {
        int currentX = startX;
        int currentY = startY;
        int lineHeight = 10;//fontMetrics.getHeight();
        int maxLineWidth = maxWidth;

        AttributedString attributedString = new AttributedString(fullText);
        attributedString.addAttribute(TextAttribute.FONT, g2d.getFont());
        LineBreakMeasurer lineBreakMeasurer = new LineBreakMeasurer(attributedString.getIterator(), g2d.getFontRenderContext());

        while (lineBreakMeasurer.getPosition() < fullText.length()) {
            TextLayout layout = lineBreakMeasurer.nextLayout(maxLineWidth);
            currentY += layout.getAscent();

            // 计算当前布局的字符范围
            int start = lineBreakMeasurer.getPosition() - layout.getCharacterCount();
            int end = lineBreakMeasurer.getPosition();

            // 遍历当前布局中的每个字符
            for (int i = start; i < end; i++) {
                char ch = fullText.charAt(i);
                // 设置颜色
                if (i >= beginLength && i < beginLength + projectLength) {
                    g2d.setColor(Color.BLACK); // 默认颜色
                    g2d.setFont(customFont.deriveFont(Font.TRUETYPE_FONT, custom_bold_font_size));
                } else {
                    g2d.setColor(Color.BLACK); // 默认颜色
                    g2d.setFont(customRegularFont.deriveFont(Font.TRUETYPE_FONT, custom_font_size));
                }

                // 绘制单个字符
                String singleChar = Character.toString(ch);
                int charWidth = fontMetrics.stringWidth(singleChar);
                g2d.drawString(singleChar, currentX, currentY);
                if (i >= beginLength && i < beginLength + projectLength) {
                    currentX += charWidth + 9;
                } else {
                    currentX += charWidth + 5;
                }
            }

            currentY += layout.getDescent() + layout.getLeading() + lineHeight;
            currentX = startX; // 重置 x 坐标以开始新的一行
        }

        // 返回绘制完当前部分文本后的新 y 坐标
        return currentY;
    }

    private int drawColoredText(Graphics2D g2d, String fullText, int startX, int startY, FontMetrics fontMetrics,
                                int maxWidth, int beginLength, int projectLength, int middleLength, int amountLength, Font customFont, Font customRegularFont) {
        int currentX = startX;
        int currentY = startY;
        int lineHeight = 10;//fontMetrics.getHeight();
        int maxLineWidth = maxWidth;

        AttributedString attributedString = new AttributedString(fullText);
        attributedString.addAttribute(TextAttribute.FONT, g2d.getFont());
        LineBreakMeasurer lineBreakMeasurer = new LineBreakMeasurer(attributedString.getIterator(), g2d.getFontRenderContext());
        int a = 0;
        while (lineBreakMeasurer.getPosition() < fullText.length()) {
            if (a == 0) {
                maxLineWidth = maxWidth - 20;
            } else {
                maxLineWidth = maxWidth;
            }
            TextLayout layout = lineBreakMeasurer.nextLayout(maxLineWidth);
            currentY += layout.getAscent();

            // 计算当前布局的字符范围
            int start = lineBreakMeasurer.getPosition() - layout.getCharacterCount();
            int end = lineBreakMeasurer.getPosition();

            // 遍历当前布局中的每个字符
            for (int i = start; i < end; i++) {
                char ch = fullText.charAt(i);

                // 设置颜色
                if (i >= beginLength && i < beginLength + projectLength) {
                    g2d.setColor(new Color(0xC2, 0x88, 0x31)); // 特殊颜色
                    g2d.setFont(customFont.deriveFont(Font.BOLD, custom_font_size));
                } else if (i >= beginLength + projectLength + middleLength && i < beginLength + projectLength + middleLength + amountLength) {
                    g2d.setColor(Color.red); // 特殊颜色
                    g2d.setFont(customFont.deriveFont(Font.BOLD, custom_font_size));
                } else {
                    g2d.setColor(Color.BLACK); // 默认颜色
                    g2d.setFont(customRegularFont.deriveFont(Font.PLAIN, custom_font_size));
                }

                // 绘制单个字符
                String singleChar = Character.toString(ch);
                int charWidth = fontMetrics.stringWidth(singleChar);
                g2d.drawString(singleChar, currentX, currentY);
                if (i >= beginLength && i < beginLength + projectLength) {
                    currentX += charWidth + 6;
                } else {
                    currentX += charWidth + 4;
                }
            }

            currentY += layout.getDescent() + layout.getLeading() + lineHeight;
            currentX = 79; // 重置 x 坐标以开始新的一行
            a++;
        }

        // 返回绘制完当前部分文本后的新 y 坐标
        return currentY;
    }

    private BufferedImage decodeBase64ToImage(String base64Image) {
        try {
            // 将Base64编码的字符串解码为字节数组
            byte[] imageBytes = Base64.getDecoder().decode(base64Image);

            // 使用字节数组创建一个ByteArrayInputStream对象
            ByteArrayInputStream inputStream = new ByteArrayInputStream(imageBytes);

            // 使用ImageIO.read()方法从ByteArrayInputStream对象中读取图像
            return ImageIO.read(inputStream);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    public void saveBytesToFile(byte[] imageBytes, String filePath) throws IOException {
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(filePath);
            fos.write(imageBytes);
        } finally {
            if (fos != null) {
                fos.close();
            }
        }
    }

    /**
     * 在字符串前添加指定数量的空格。
     *
     * @param str       要处理的字符串
     * @param numSpaces 需要在字符串前添加的空格数，如果为0则直接返回原字符串
     * @return 添加了空格的字符串
     */
    public String prependSpaces(String str, int numSpaces) {
        if (numSpaces <= 0) {
            return str; // 如果不需要添加空格，直接返回原字符串
        }

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < numSpaces; i++) {
            sb.append(" "); // 添加空格
        }
        sb.append(str); // 添加原始字符串
        return sb.toString();
    }

    public String convertImageToBase64(BufferedImage image, String formatName) throws Exception {
        // 使用 ByteArrayOutputStream 来存储图像的字节
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        // 将 BufferedImage 写入 ByteArrayOutputStream，指定图像格式（如 "png"）
        ImageIO.write(image, formatName, baos);

        // 将字节数组转换为 Base64 编码的字符串
        String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());

        // 关闭 ByteArrayOutputStream
        baos.close();

        return base64Image;
    }

    public static void main(String[] args) throws Exception {
        ImageService imageService = new ImageService();
        InputStream inputStreamFromUrl = imageService.getInputStreamFromUrl("https://image.baidu.com/search/detail?ct=503316480&z=0&ipn=d&word=%E5%9B%BE%E7%89%87&hs=0&pn=0&spn=0&di=7416423379248349185&pi=0&rn=1&tn=baiduimagedetail&is=0%2C0&ie=utf-8&oe=utf-8&cl=2&lm=-1&cs=3292075640%2C1695839085&os=1775280423%2C3291766899&simid=4239237363%2C559175049&adpicid=0&lpn=0&ln=30&fr=ala&fm=&sme=&cg=&bdtype=0&oriquery=%E5%9B%BE%E7%89%87&objurl=https%3A%2F%2Fmmbiz.qpic.cn%2Fsz_mmbiz_jpg%2F27Sj82GvO8X9WajjuhPtwKL2ibNZv9xpfOS8CZwr1whmmKCiagXvz3Rt04sftyPjJicXXh2xtermcwgsEgq39iaicsA%2F640%3Fwx_fmt%3Djpeg%26from%3Dappmsg&fromurl=ippr_z2C%24qAzdH3FAzdH3Fpwtwg_z%26e3Btqts7_z%26e3Bv54AzdH3Fpwtwg4tgfijg2AzdH3Fdad9AzdH3FacdnAzdH3Fcmc0mnb_z%26e3Bfip4s&gsm=&islist=&querylist=&dyTabStr=MCwzLDEsMiwxMyw3LDYsNSwxMiw5");
        System.out.println(inputStreamFromUrl);
    }
}