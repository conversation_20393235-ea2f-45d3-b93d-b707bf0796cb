package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="师资资源申请统计VO")
public class TeacherApplyCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "已申请数量")
    private int applyCount;

    @Schema(description = "已处理数量")
    private Long processedCount;

    @Schema(description = "待处理数量")
    private Long pendingCount;

    @Schema(description = "处理率")
    private String rate;


}