package com.ows.ufa.system.vo.excel;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ows.ufa.common.core.annotation.Excel;
import lombok.Data;

/**
 * 问卷调查信息填报表
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
public class QuestionnaireSubmitExcelVO implements Serializable {

    private static final long serialVersionUID=1L;


    @Excel(name = "问卷调查ID", type = Excel.Type.ALL)
    private Long questionnaireId;

    @Excel(name = "用户ID", type = Excel.Type.ALL)
    private String userId;

    @Excel(name = "问卷填写内容", type = Excel.Type.ALL)
    private String contentJson;

    @Excel(name = "删除标志：0-删除；1-有效", type = Excel.Type.ALL)
    private Integer delFlag;

    @Excel(name = "数据权限部门id", type = Excel.Type.ALL)
    private String deptId;

    @Excel(name = "创建人", type = Excel.Type.ALL)
    private String createAt;

    @Excel(name = "更新人", type = Excel.Type.ALL)
    private String updateAt;

    @Excel(name = "创建时间", type = Excel.Type.ALL)
    private LocalDateTime createTime;

    @Excel(name = "更新时间", type = Excel.Type.ALL)
    private LocalDateTime updateTime;
}