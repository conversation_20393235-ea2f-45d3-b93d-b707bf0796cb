package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.request.NoticeLogRequest;
import com.ows.ufa.system.service.NoticeLogService;
import com.ows.ufa.system.vo.NoticeLogVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 通知书推送信息日志记录表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "noticeLog")
@Tag(name = "通知书推送信息接口", description = "通知书推送信息接口")
public class NoticeLogController extends BaseController {

    private final NoticeLogService NoticeLogServiceImpl;

    @GetMapping("list")
    @Operation(summary = "通知书推送信息日志记录表分页查询")
    public TableDataInfo listNoticeLogByPage(NoticeLogRequest request) {
        startPage();
        return getDataTable(NoticeLogServiceImpl.queryNoticeLogs(request));
    }

    @GetMapping("{id}")
    @Operation(summary = "通知书推送信息日志记录表查询详情")
    public AjaxResult findNoticeLog(@PathVariable Long id) {
        return success(NoticeLogServiceImpl.findNoticeLog(id));
    }

    @PostMapping
    @Operation(summary = "通知书推送信息日志记录表新增数据")
    public AjaxResult saveNoticeLog(@RequestBody NoticeLogVO vo) {
        return success(NoticeLogServiceImpl.saveNoticeLog(vo));
    }

    @PostMapping("update")
    @Operation(summary = "通知书推送信息日志记录表修改数据")
    public AjaxResult updateNoticeLog(@RequestBody NoticeLogVO vo) {
        return success(NoticeLogServiceImpl.updateNoticeLog(vo));
    }

    @PostMapping("delete")
    @Operation(summary = "通知书推送信息日志记录表删除数据")
    public AjaxResult removeNoticeLog(@RequestBody IdRequest id) {
        return success(NoticeLogServiceImpl.removeNoticeLog(id.getId()));
    }
}
