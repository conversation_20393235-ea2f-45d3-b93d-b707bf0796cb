package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 师资资源申请处理流程表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(description="师资资源申请处理流程表VO")
public class TeacherResourcesApplyHandleVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "师资资源申请ID")
    @NotNull(message = "师资资源申请ID不能为空")
    private Long teacherResourcesApplyId;

    @Schema(description = "处理人名称")
    @NotNull(message = "处理人名称不能为空")
    private String handleUserName;

    @Schema(description = "状态:0-待处理;1-已处理")
    @NotNull(message = "状态:0-待处理;1-已处理不能为空")
    private Integer status;

    @Schema(description = "事件处理描述")
    @NotNull(message = "事件处理描述不能为空")
    private String descp;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime updateTime;

}