package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.common.core.constant.UserConstants;
import com.ows.ufa.common.core.enums.SensitiveType;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.SensitiveUtil;
import com.ows.ufa.common.core.utils.StringUtils;
import com.ows.ufa.system.domain.SysPost;
import com.ows.ufa.system.entity.BackupTeacher;
import com.ows.ufa.system.mapper.BackupTeacherMapper;
import com.ows.ufa.system.mapper.SysPostMapper;
import com.ows.ufa.system.mapper.SysUserPostMapper;
import com.ows.ufa.system.service.*;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 通用手机号加解密 服务层处理
 *
 */
@Service
public class PhoneSecurityServiceImpl implements PhoneSecurityService
{

    @Autowired
    private BackupTeacherService backupTeacherService;

    @Autowired
    private ClubInfoService clubInfoService;

    @Autowired
    private ClubMemberService clubMemberService;

    @Autowired
    private BaseStudentService baseStudentService;

    @Autowired
    private BaseTeacherServiceImpl baseTeacherService;

    @Override
    public String updatePhone() {
//        //备用师资库
//        backupTeacherService.updatePhone();
//        //社团信息管理
//        clubInfoService.updatePhone();
//        //社团成员管理
//        clubMemberService.updatePhone();
        //共享师资加密入库
//        baseStudentService.updatePhone();
        //跟人画像加密入库
        baseTeacherService.updatePhone();
        return "1";
    }

    @Override
    public Object getPhone(String phoneEncrypt) {
        return SensitiveUtil.decrypt(phoneEncrypt);
    }
}
