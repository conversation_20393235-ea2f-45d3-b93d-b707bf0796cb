package com.ows.ufa.system.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;

@Configuration
public class MultipartConfig {

    @Bean
    @Profile("!test") // 非test环境生效
    public CommonsMultipartResolver defaultMultipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        resolver.setMaxUploadSize(50 * 1024 * 1024); // 50MB
        resolver.setMaxInMemorySize(50 * 1024 * 1024); // 50MB
        return resolver;
    }

    @Bean
    @Profile("test") // 仅test环境生效
    public CommonsMultipartResolver testMultipartResolver() {
        CommonsMultipartResolver resolver = new CommonsMultipartResolver();
        resolver.setMaxUploadSize(200 * 1024 * 1024); // 200MB
        resolver.setMaxInMemorySize(200 * 1024 * 1024); // 200MB
        return resolver;
    }
}