package com.ows.ufa.system.task;

import com.alibaba.fastjson.JSONArray;
import com.ows.ufa.system.entity.Questionnaire;
import com.ows.ufa.system.enums.DelFlag;
import com.ows.ufa.system.enums.QuestionnaireStatus;
import com.ows.ufa.system.service.QuestionnaireService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class QuestionnaireTask {

    private final QuestionnaireService questionnaireServiceImpl;

    @Scheduled(cron = "0 * * * * ?")// 每分钟的第0秒执行一次
    public void processEventFeedback() {
        log.info("执行问卷结束状态更新开始");
        List<Questionnaire> questionnaires = questionnaireServiceImpl.lambdaQuery()
                .select(Questionnaire::getId)
                .eq(Questionnaire::getDelFlag, DelFlag.VALID.getCode())
                .le(Questionnaire::getEndTime, LocalDateTime.now())
                .eq(Questionnaire::getStatus, QuestionnaireStatus.COLLECTING.getCode()).list();
        if (questionnaires != null && !questionnaires.isEmpty()) {
            List<Long> ids = questionnaires.stream()
                    .map(Questionnaire::getId).collect(Collectors.toList());
            log.info("执行问卷结束状态更新:questionnaireids:{}", JSONArray.toJSONString(ids));
            questionnaireServiceImpl.lambdaUpdate().set(Questionnaire::getStatus, QuestionnaireStatus.ENDED.getCode())
                    .in(Questionnaire::getId, ids).update();
        }
        log.info("执行问卷结束状态更新结束");
    }
}