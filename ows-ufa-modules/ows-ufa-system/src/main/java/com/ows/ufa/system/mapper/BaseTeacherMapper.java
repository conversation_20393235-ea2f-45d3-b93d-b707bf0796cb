package com.ows.ufa.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.entity.BaseTeacher;
import com.ows.ufa.system.request.BaseTeacherRequest;
import com.ows.ufa.system.vo.BaseTeacherVO;
import com.ows.ufa.system.vo.count.BaseTeacherCountVO;
import com.ows.ufa.system.vo.count.BaseTeacherEduCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Mapper
public interface BaseTeacherMapper extends BaseMapper<BaseTeacher> {

    List<BaseTeacherVO> querySchoolTeachers(@Param("req")BaseTeacherRequest request);


    Long countSchool();

    BaseTeacherCountVO countTeacher();

    Long countDept();

    List<BaseTeacherEduCountVO> countTeacherEdu();
}