package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.AbilityLabelUserService;
import com.ows.ufa.system.form.AbilityLabelUserForm;
import com.ows.ufa.system.request.AbilityLabelUserRequest;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 * 标签人员表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "abilityLabelUser")
@Tag(name = "abilityLabelUser", description = "标签人员表接口")
public class AbilityLabelUserController extends BaseController {

    private final AbilityLabelUserService AbilityLabelUserServiceImpl;

    @GetMapping("list")
    @Operation(summary = "标签人员表分页查询")
    public AjaxResult listAbilityLabelUserByPage(AbilityLabelUserRequest request) {
        startPage();
        return success(getDataTable(AbilityLabelUserServiceImpl.queryAbilityLabelUsers(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "标签人员表查询详情")
    public AjaxResult findAbilityLabelUser(@PathVariable Long id) {
        return success(AbilityLabelUserServiceImpl.findAbilityLabelUser(id));
    }

    @PostMapping
    @Operation(summary = "标签人员表新增数据")
    public AjaxResult saveAbilityLabelUser(@RequestBody AbilityLabelUserForm form) {
        return success(AbilityLabelUserServiceImpl.saveAbilityLabelUser(form));
    }

    @PostMapping("update")
    @Operation(summary = "标签人员表修改数据")
    public AjaxResult updateAbilityLabelUser(@RequestBody AbilityLabelUserForm form) {
        return success(AbilityLabelUserServiceImpl.updateAbilityLabelUser(form));
    }

    @PostMapping("delete")
    @Operation(summary = "标签人员表删除数据")
    public AjaxResult removeAbilityLabelUser(@RequestBody IdRequest id) {
        return success(AbilityLabelUserServiceImpl.removeAbilityLabelUser(id.getId()));
    }
}
