package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.TeacherResourcesApply;
import com.ows.ufa.system.form.TeacherResourcesApplyForm;
import com.ows.ufa.system.vo.TeacherResourcesApplyVO;
import com.ows.ufa.system.request.TeacherResourcesApplyRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.count.TeacherApplyCountVO;

import java.util.List;
/**
 * <p>
 * 师资资源申请表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
public interface TeacherResourcesApplyService extends IService<TeacherResourcesApply> {

    List<TeacherResourcesApply> queryTeacherResourcesApplys(TeacherResourcesApplyRequest request);

    TeacherResourcesApplyVO findTeacherResourcesApply(Long id);

    Long saveTeacherResourcesApply(TeacherResourcesApplyForm vo);

    boolean updateTeacherResourcesApply(TeacherResourcesApplyVO vo);

    boolean removeTeacherResourcesApply(Long id);

    TeacherApplyCountVO countTeacherApply();

    TeacherApplyCountVO applyStatics();
}
