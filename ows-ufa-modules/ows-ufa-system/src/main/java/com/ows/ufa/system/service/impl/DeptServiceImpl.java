package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.entity.Dept;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.vo.DeptVO;
import com.ows.ufa.system.form.DeptForm;
import com.ows.ufa.system.request.DeptRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.DeptMapper;
import com.ows.ufa.system.service.DeptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.time.LocalDateTime;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 部门表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements DeptService {

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<Dept> queryDepts() {
        LambdaQueryWrapper<Dept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(Dept::getDeptId, Dept::getDeptName,Dept::getParentId);
        queryWrapper.in(Dept::getDeptId, SecurityUtils.getAncestors());
        return this.list(queryWrapper);
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public Dept findDept(String deptId) {
        return this.getById(deptId);
    }
}