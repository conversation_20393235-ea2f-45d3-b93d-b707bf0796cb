package com.ows.ufa.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ows.ufa.system.entity.AreaCode;
import com.ows.ufa.system.mapper.AreaCodeMapper;
import com.ows.ufa.system.service.IAreaCodeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 *
 * <AUTHOR>
 * @since 2024-12-24
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class SysAreaCodeServiceImpl extends ServiceImpl<AreaCodeMapper, AreaCode> implements IAreaCodeService {

}