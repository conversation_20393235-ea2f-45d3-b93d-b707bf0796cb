package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ows.ufa.common.core.annotation.Sensitive;
import com.ows.ufa.common.core.enums.SensitiveType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@TableName("t_club_info")
@Schema(description ="社团信息表实体")
public class ClubInfo implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "社团照片URL或文件路径")
    private String clubPhoto;

    @Schema(description = "社团名称")
    private String clubName;

    @Schema(description = "社团简介")
    private String clubDescp;

    @Schema(description = "负责人姓名")
    private String leader;

    @Schema(description = "联系方式")
    @Sensitive(type = SensitiveType.PHONE,cipherField = "phoneEncrypt")
    private String phoneNumber;

    @Schema(description = "加密手机号")
    private String phoneEncrypt;

    @Schema(description = "是否需要考试:0-否;1-是")
    private Integer isExamRequired;

    @Schema(description = "招募条件")
    private String recruitmentConditions;

    @Schema(description = "删除标志:0-删除;1-有效")
    private Integer delFlag;

    @Schema(description = "申请日期")
    private LocalDateTime applicationDate;

    @Schema(description = "审核日期")
    private LocalDateTime reviewDate;

    @Schema(description = "审核人")
    private String reviewAt;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "数据权限部门id")
    private String deptId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recruitmentStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recruitmentEndDate;
}