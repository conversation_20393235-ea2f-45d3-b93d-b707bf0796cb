package com.ows.ufa.system.aspect;

import com.baomidou.mybatisplus.extension.plugins.handler.DataPermissionHandler;
import com.ows.ufa.common.security.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.operators.conditional.AndExpression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Method;
import java.util.Objects;


@Slf4j
public class MyDataScopeHandler implements DataPermissionHandler {
    /**
     * 获取数据权限 SQL 片段表达式
     *
     * @param where             待执行 SQL Where 条件表达式
     * @param mappedStatementId Mybatis MappedStatement Id 根据该参数可以判断具体执行方法
     * @return 数据权限 SQL 片段表达式
     */
    @Override
    public Expression getSqlSegment(Expression where, String mappedStatementId) {
        try {
            String className = mappedStatementId.substring(0, mappedStatementId.lastIndexOf("."));
            String methodName = mappedStatementId.substring(mappedStatementId.lastIndexOf(".") + 1);
            Method[] methods = Class.forName(className).getMethods();
            for (Method m : methods) {
                if (StringUtils.isBlank(m.getName()) || !m.getName().equals(methodName)) {
                    continue;
                }
                MyDataScope annotation = m.getAnnotation(MyDataScope.class);
                if (Objects.isNull(annotation)) {
                    return where;
                }
                String sqlSegment = getSqlSegment(annotation);
                return StringUtils.isBlank(sqlSegment) ? where : getExpression(where, sqlSegment);
            }
        } catch (ClassNotFoundException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 拼接需要在业务 SQL 中额外追加的数据权限 SQL
     *
     * @param annotation
     * @return 数据权限 SQL
     */
    private String getSqlSegment(MyDataScope annotation) {
        String sqlSegment = "";
        String ancestors= SecurityUtils.getDeptAncestors();
        if (StringUtils.isBlank(ancestors)) {
            return sqlSegment;
        } else {
            if(StringUtils.isNotBlank(annotation.alias())){
                sqlSegment = com.ows.ufa.common.core.utils.StringUtils.format(" {}.{} IN ({}) ",
                        annotation.alias(), annotation.dataId(), ancestors);
            }else{
                sqlSegment = com.ows.ufa.common.core.utils.StringUtils.format(" dept_id IN ({}) ", ancestors);
            }
        }
        return sqlSegment;
    }

    /**
     * 将数据权限 SQL 语句追加到数据权限 SQL 片段表达式里
     *
     * @param where      待执行 SQL Where 条件表达式
     * @param sqlSegment 数据权限 SQL 片段
     * @return
     */
    private Expression getExpression(Expression where, String sqlSegment) {
        try {
            Expression sqlSegmentExpression = CCJSqlParserUtil.parseCondExpression(sqlSegment);
            return (null != where) ? new AndExpression(where, sqlSegmentExpression) : sqlSegmentExpression;
        } catch (JSQLParserException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
}