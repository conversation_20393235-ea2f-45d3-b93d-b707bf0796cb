package com.ows.ufa.system.vo.excel;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ows.ufa.common.core.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 社团成员活动信息表
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
public class ClubMemberActivityExcelVO implements Serializable {

    private static final long serialVersionUID=1L;

    @Excel(name = "社团名称", type = Excel.Type.ALL)
    private Long clubInfoName;

    @Excel(name = "活动名称", type = Excel.Type.ALL)
    private Long clubActivityName;

    @Excel(name = "姓名", type = Excel.Type.ALL)
    private String memberName;

    @Excel(name = "性别", readConverterExp = "0=男,1=女", type = Excel.Type.ALL)
    private Integer sex;

    @Excel(name = "联系电话", type = Excel.Type.ALL)
    private String phoneNumber;

    @Excel(name = "申请日期", dateFormat = "yyyy-MM-dd", type = Excel.Type.ALL)
    private LocalDateTime applicationDate;

    @Excel(name = "审核状态",readConverterExp = "0=待审核,1=审核通过,2=审核不通过", type = Excel.Type.ALL)
    private Integer status;
}