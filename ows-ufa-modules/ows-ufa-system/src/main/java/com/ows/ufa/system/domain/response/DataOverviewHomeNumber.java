package com.ows.ufa.system.domain.response;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 数据统计首页数量统计
 * 
 * <AUTHOR>
 */
@Data
public class DataOverviewHomeNumber
{
    private static final long serialVersionUID = 1L;

    //查询在籍学员总数
    private Long zsPlanStudentTotal;

    //院系数量
    private Long deptNumber;

    //教师数量
    private Long teacherNumber;

    //教室数量
    private Long classroomNumber;

    //班级数量
    private Long classNumber;
}
