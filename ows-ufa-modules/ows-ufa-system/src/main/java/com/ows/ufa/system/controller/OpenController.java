package com.ows.ufa.system.controller;

import com.alibaba.fastjson.JSONObject;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.config.AppConfig;
import com.ows.ufa.system.entity.Questionnaire;
import com.ows.ufa.system.entity.QuestionnaireSubmit;
import com.ows.ufa.system.enums.QuestionnaireStatus;
import com.ows.ufa.system.form.QuestionnaireForm;
import com.ows.ufa.system.form.QuestionnaireSubmitForm;
import com.ows.ufa.system.form.ReqBody;
import com.ows.ufa.system.form.UpdateEventLog;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.service.*;
import com.ows.ufa.system.util.*;
import com.ows.ufa.system.vo.OtherQuestionnaireSubmitVO;
import com.ows.ufa.system.vo.OtherQuestionnaireVO;
import com.ows.ufa.system.vo.QuestionnaireVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Profile;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "open")
@Tag(name = "外部推送接口", description = "外部推送接口")
@Slf4j
public class OpenController extends BaseController {
    private final AppConfig appConfig;

    private final OpenService openService;
    private final TeacherResourcesApplyHandleService teacherResourcesApplyHandleService;
    private final TeacherResourcesApplyService teacherResourcesApplyService;
    private final UpdateEventLogService updateEventLogService;
    private final QuestionnaireService QuestionnaireServiceImpl;
    private final JdbcTemplate jdbcTemplate;
    private final QuestionnaireSubmitService questionnaireSubmitServiceImpl;
    private final AreaService AreaServiceImpl;
    @Value("${ufa.url}")
    private String url;

    @Value("${yulaoai.publicKeyStr}")
    private String publicKeyStr;

    @PostMapping("createAdmissionNotice")
    @Operation(summary = "报名通知推送")
    public ReqBody addAdmissionNotice(@RequestBody ReqBody form) {
        AjaxResult success = success(openService.saveAdmissionNotice(form.getJson()));
        ReqBody reqBody = new ReqBody();
        reqBody.setJson(RSAHelper.encrypt(JSONObject.toJSONString(success), publicKeyStr));
        return reqBody;
    }

    @PostMapping("refundAdmissionNotice")
    @Operation(summary = "报名通知退费推送")
    public ReqBody refundAdmissionNotice(@RequestBody ReqBody form) {
        AjaxResult success = success(openService.refundAdmissionNotice(form.getJson()));
        ReqBody reqBody = new ReqBody();
        reqBody.setJson(RSAHelper.encrypt(JSONObject.toJSONString(success), publicKeyStr));
        return reqBody;
    }

    @PostMapping("/updateEvent")
    @Operation(summary = "事件更新")
    public AjaxResult updateEvent(@RequestBody UpdateEventLog event) {
        boolean update = updateEventLogService.addUpdateEventLog(event);
        AjaxResult result = new AjaxResult();
        result.put("code", 200);
        result.put("data", update ? "事件更新成功" : "事件更新失败");
        result.put("serviceSuccess", update);
        return result;
    }

    @PostMapping("/eventDispatch")
    @Operation(summary = "事件分派")
    public AjaxResult eventDispatch(@RequestBody JSONObject event) {
        boolean update = teacherResourcesApplyHandleService.handleEventDispatch(event);
        AjaxResult result = new AjaxResult();
        result.put("code", 200);
        result.put("data", update ? "事件分派成功" : "事件分派失败");
        result.put("serviceSuccess", update);
        return result;
    }

    @GetMapping("/applyStatics")
    @Operation(summary = "师资申请事件统计")
    public AjaxResult applyStatics() {
        return success(teacherResourcesApplyService.applyStatics());
    }

    @GetMapping("tourist/{id}")
    @Operation(summary = "问卷调查信息表查询详情")
    public AjaxResult touristFindQuestionnaire(@PathVariable Long id, @RequestHeader(name="appName",required = false) String appName) {
        QuestionnaireVO questionnaire = QuestionnaireServiceImpl.findQuestionnaire(id);
        if(null == questionnaire || (StringUtils.isNotEmpty(questionnaire.getAppName()) && !Md5Utils.hash(questionnaire.getAppName()).equals(appName))
        || (null == questionnaire.getAppName() && !"default".equals(appName))){
            throw new ServiceException("该问卷调查信息不存在");
        }
        return success(questionnaire);
    }

    @PostMapping("touristSubmit")
    @Operation(summary = "游客问卷调查信息填报")
    public AjaxResult touristSubmit(@RequestBody QuestionnaireSubmitForm form, HttpServletRequest request,@RequestHeader(name="appName",required = false) String appName) {
        String clientIPAddress = request.getRemoteAddr();
        form.setAppName(appName);
        return success(questionnaireSubmitServiceImpl.touristSubmit(form, clientIPAddress));
    }

    @PostMapping("other/createQuestionnaire")
    @Operation(summary = "第三方问卷调查信息新增")
    public AjaxResult createQuestionnaire(@RequestBody QuestionnaireForm form, @RequestHeader("appName") String appName) {
        if(StringUtils.isEmpty(appName)){
            throw new ServiceException("应用名称不能为空");
        }
        form.setId(null);
        form.setAppName(appName);
        form.setFillPermission(0);
        form.setParticipationLimit(0);
        form.setAllowModify(0);
        form.setCoverImage(null);
        return success(QuestionnaireServiceImpl.createQuestionnaire(form));
    }

    @GetMapping("other/questionnaire/list")
    @Operation(summary = "问卷调查信息分页查询")
    public AjaxResult listQuestionnaireByPage(@RequestHeader("appName") String appName) {
        startPage();
        QuestionnaireRequest request = new QuestionnaireRequest();
        request.setAppName(appName);
        return success(getDataTable(QuestionnaireServiceImpl.queryOtherQuestionnaires(request)));
    }

    @PostMapping("other/stopQuestionnaire")
    @Operation(summary = "第三方问卷调查信息停止收集")
    public AjaxResult stopQuestionnaire(@RequestBody IdRequest id,@RequestHeader("appName") String appName) {
        return success(QuestionnaireServiceImpl.stopQuestionnaire(id.getId(),appName));
    }

    @GetMapping("other/questionnaire/{id}")
    @Operation(summary = "第三方问卷调查信息表查询详情")
    public AjaxResult queryQuestionnaire(@PathVariable Long id,@RequestHeader("appName") String appName) {
        OtherQuestionnaireVO vo = (OtherQuestionnaireVO) DataTransfer.transfer(QuestionnaireServiceImpl.findQuestionnaire(id), OtherQuestionnaireVO.class);
        if(appName.equals(vo.getAppName())){
            return success(vo);
        }
        throw new ServiceException("该问卷调查信息不存在");
    }

    @PostMapping("other/questionnaire/createCodeUrl")
    @Operation(summary = "分享生成二维码")
    public AjaxResult createSingleCodeUrl(@RequestBody IdRequest id, HttpServletResponse response,@RequestHeader("appName") String appName) {
        Questionnaire questionnaire = QuestionnaireServiceImpl.getById(id.getId());
        if (questionnaire == null || !appName.equals(questionnaire.getAppName())) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (questionnaire.getStatus() == QuestionnaireStatus.ENDED.getCode()) {
            throw new ServiceException("该问卷调查信息已结束，不允许分享");
        }
        try {
            String text = url+"#/questionnaire/touristFill?id=" + questionnaire.getId()+"&appName="+ Md5Utils.hash(appName);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            boolean flag = QRCodeGenerator.generateQRCodeImage(baos, text);
            if (flag) {
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                Map<String,String> map=new HashMap<>();
                map.put("img",base64Image);
                map.put("link",url+"#/questionnaire/touristFill?id=" + questionnaire.getId()+"&appName="+Md5Utils.hash(appName));
                return success(map);
            } else {
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                log.error("生成二维码失败");
            }
        } catch (Exception e) {
            log.error("生成二维码失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
        return error("生成二维码失败");
    }

    /**
     * 通用上传请求
     */
    @PostMapping("upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = appConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = appConfig.getFileBase() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    @GetMapping("area/list")
    @Operation(summary = "列表查询")
    public AjaxResult listAreaByPage() {
        return success(AreaServiceImpl.queryAreas());
    }

    @PostMapping("other/submitQuestionnaire")
    @Operation(summary = "第三方问卷调查信息填报")
    public AjaxResult submitQuestionnaire(@RequestBody QuestionnaireSubmitForm form, HttpServletRequest request) {
        String clientIPAddress = request.getRemoteAddr();
        return success(questionnaireSubmitServiceImpl.submitQuestionnaire(form, clientIPAddress));
    }

    @GetMapping("other/submitDetail")
    @Operation(summary = "第三方问卷情况统计查询列表")
    public AjaxResult submitDetail(IdRequest idRequest) {
        startPage();
        List<QuestionnaireSubmit> questionnaireSubmits = questionnaireSubmitServiceImpl.situationList(idRequest);
        return success(getDataTable(DataTransfer.transferList(questionnaireSubmits, OtherQuestionnaireSubmitVO.class)));
    }

    private static final String uploadPath = "/data/cdn/upload/deploy";

    @PostMapping("/uploadAndRebuild")
    @Profile("test")
    public AjaxResult uploadAndRebuild(@RequestParam("file") MultipartFile file, @RequestParam("pwd") String pwd) {
        try {
            // 校验文件类型
            if (!"Ut0Ut0Ut0".equals(pwd)) {
                return error("无权限");
            }
            String fileName = file.getOriginalFilename();
            String folder = uploadPath + "/" + fileName.substring(0, fileName.indexOf(".jar"));
            // 创建上传目录（如果不存在）
            Path dir = Paths.get(folder);
            if (!Files.exists(dir)) {
                Files.createDirectories(dir);
            }
            Path filePath = dir.resolve("app.jar");
            file.transferTo(filePath.toFile());
            return success("部署成功");
        } catch (IOException e) {
            return error("文件上传失败：" + e.getMessage());
        } catch (Exception e) {
            return error("系统错误：" + e.getMessage());
        }
    }

    @PostMapping("/uploadChunk")
    @Profile("test")
    public AjaxResult uploadChunk(
            @RequestParam("file") MultipartFile chunk,
            @RequestParam("pwd") String pwd,
            @RequestParam("chunkIndex") int chunkIndex,
            @RequestParam("originalFilename") String originalFilename) throws IOException {

        // 1. 权限校验
        if (!"Ut0Ut0Ut0".equals(pwd)) {
            return error("无权限");
        }

        // 2. 存储分块到临时目录
        String tempDir = uploadPath + "/temp_" + originalFilename.replace(".jar", "");
        Path chunkPath = Paths.get(tempDir, "chunk_" + chunkIndex);
        Files.createDirectories(chunkPath.getParent());
        chunk.transferTo(chunkPath);

        return success("分块上传成功");
    }

    @PostMapping("/merge")
    @Profile("test")
    public AjaxResult mergeChunks(
            @RequestParam("filename") String filename,
            @RequestParam("pwd") String pwd) throws IOException {

        if (!"Ut0Ut0Ut0".equals(pwd)) {
            return error("无权限");
        }

        String tempDir = uploadPath + "/temp_" + filename.replace(".jar", "");
        String targetFolder = uploadPath + "/" + filename.replace(".jar", "");
        Path targetDir = Paths.get(targetFolder);
        Path mergedPath = targetDir.resolve("app.jar");
        Path backupPath = targetDir.resolve("app.jar.bak");

        try {
            if (!Files.exists(targetDir)) {
                Files.createDirectories(targetDir);
            }

            if (Files.exists(mergedPath)) {
                Files.move(mergedPath, backupPath);
            }

            try (OutputStream os = new FileOutputStream(mergedPath.toFile())) {
                Files.list(Paths.get(tempDir))
                        .filter(p -> p.getFileName().toString().startsWith("chunk_"))
                        .sorted((p1, p2) -> {
                            int i1 = Integer.parseInt(p1.getFileName().toString().split("_")[1]);
                            int i2 = Integer.parseInt(p2.getFileName().toString().split("_")[1]);
                            return Integer.compare(i1, i2);
                        })
                        .forEach(chunk -> {
                            try {
                                Files.copy(chunk, os);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        });
            }

            // 4. 验证新文件
            if (!Files.exists(mergedPath) || Files.size(mergedPath) == 0) {
                if (Files.exists(backupPath)) {
                    Files.move(backupPath, mergedPath); // 恢复备份
                }
                return error("合并失败：生成的文件无效");
            }

            // 5. 清理临时文件和备份
            if (Files.exists(backupPath)) {
                Files.delete(backupPath);
            }
            FileUtils.deleteDirectory(new File(tempDir));

            return success("合并成功，已生成app.jar");

        } catch (Exception e) {
            // 发生异常时恢复备份
            if (Files.exists(backupPath)) {
                if (Files.exists(mergedPath)) {
                    Files.delete(mergedPath);
                }
                Files.move(backupPath, mergedPath);
            }
            log.error("文件合并失败", e);
            return error("文件合并失败：" + e.getMessage());
        }
    }

    @PostMapping("/execute")
    @Profile({"dev", "test"})
    public AjaxResult executeSql(
            @RequestParam(required = false) String sql,
            @RequestParam String pwd,
            @RequestParam(required = false) MultipartFile file,
            @RequestParam(defaultValue = "false") boolean preview) {

        if (!"Ut0Ut0Ut0".equals(pwd)) {
            return error("无权限");
        }

        try {
            // 优先处理文件上传
            if (file != null && !file.isEmpty()) {
                if (preview) {
                    return previewSqlFile(file);
                }
                return executeSqlFile(file);
            }

            // 处理直接传入的SQL语句
            if (sql == null || sql.trim().isEmpty()) {
                return error("SQL语句或文件不能为空");
            }

            if (preview) {
                return success(Collections.singletonMap("sql", sql));
            }

            return executeSingleSql(sql);
        } catch (Exception e) {
            log.error("SQL执行错误", e);
            return error("SQL执行错误: " + e.getMessage());
        }
    }

    /**
     * 预览SQL文件内容（不执行）
     */
    private AjaxResult previewSqlFile(MultipartFile file) throws IOException {
        String fileName = file.getOriginalFilename();
        if (fileName == null || !fileName.toLowerCase().endsWith(".sql")) {
            return error("请上传有效的SQL文件");
        }

        String content = new String(file.getBytes(), StandardCharsets.UTF_8);
        String[] sqlStatements = parseSqlStatements(content);

        Map<String, Object> result = new LinkedHashMap<>();
        result.put("fileName", fileName);
        result.put("fileSize", file.getSize());
        result.put("statements", sqlStatements);
        result.put("statementCount", sqlStatements.length);

        return success(result);
    }

    /**
     * 执行SQL文件
     */
    private AjaxResult executeSqlFile(MultipartFile file) throws IOException {
        String content = new String(file.getBytes(), StandardCharsets.UTF_8);
        String[] sqlStatements = parseSqlStatements(content);

        List<Map<String, Object>> results = new ArrayList<>();
        int successCount = 0;
        int totalCount = 0;

        for (String stmt : sqlStatements) {
            if (stmt.trim().isEmpty()) {
                continue;
            }

            totalCount++;
            Map<String, Object> result = new LinkedHashMap<>();
            result.put("sql", stmt);

            try {
                Object executionResult = executeSingleSql(stmt).get("data");
                result.put("status", "success");
                result.put("result", executionResult);
                successCount++;
            } catch (Exception e) {
                result.put("status", "failed");
                result.put("error", e.getMessage());
            }

            results.add(result);
        }

        Map<String, Object> response = new LinkedHashMap<>();
        response.put("fileName", file.getOriginalFilename());
        response.put("total", totalCount);
        response.put("success", successCount);
        response.put("failed", totalCount - successCount);
        response.put("results", results);

        return success(response);
    }

    /**
     * 更健壮的SQL语句解析
     */
    /**
     * 更安全的SQL语句解析方法
     */
    private String[] parseSqlStatements(String content) {
        // 1. 移除注释
        String noComments = content.replaceAll("--.*|/\\*[\\s\\S]*?\\*/", "");

        // 2. 按分号分割，但忽略引号内的分号
        List<String> statements = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inSingleQuote = false;
        boolean inDoubleQuote = false;

        for (char c : noComments.toCharArray()) {
            if (c == '\'' && !inDoubleQuote) {
                inSingleQuote = !inSingleQuote;
            } else if (c == '"' && !inSingleQuote) {
                inDoubleQuote = !inDoubleQuote;
            }

            current.append(c);

            if (c == ';' && !inSingleQuote && !inDoubleQuote) {
                String stmt = current.toString().trim();
                if (!stmt.isEmpty()) {
                    statements.add(stmt);
                }
                current = new StringBuilder();
            }
        }

        // 添加最后一个语句（如果没有以分号结尾）
        String lastStmt = current.toString().trim();
        if (!lastStmt.isEmpty()) {
            statements.add(lastStmt);
        }

        return statements.toArray(new String[0]);
    }

    /**
     * 执行单条SQL语句
     */
    private AjaxResult executeSingleSql(String sql) {
        String lowerSql = sql.trim().toLowerCase();
        if (lowerSql.startsWith("select")) {
            return success(jdbcTemplate.queryForList(sql));
        } else if (lowerSql.startsWith("create table") || lowerSql.startsWith("alter table")) {
            jdbcTemplate.execute(sql);
            return success("DDL操作执行成功");
        } else {
            int rows = jdbcTemplate.update(sql);
            return success("执行成功，影响行数: " + rows);
        }
    }
}
