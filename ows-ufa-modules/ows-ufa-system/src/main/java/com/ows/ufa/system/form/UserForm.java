package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 用户信息表
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
@Data
@Schema(description ="用户信息表VO")
public class UserForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "微信公众号认证")
    @NotNull(message = "微信公众号认证不能为空")
    private String openid;

    @Schema(description = "对应部门id")
    @NotNull(message = "对应部门id不能为空")
    private String topOrgId;

    @Schema(description = "对应学生表或教师表ID或注册用户ID(学生用户该ID存的是学生表ID,教师i用户该ID存的是学生表ID)")
    @NotNull(message = "对应学生表或教师表ID或注册用户ID(学生用户该ID存的是学生表ID,教师i用户该ID存的是学生表ID)不能为空")
    private String detailId;

    @Schema(description = "用户账号")
    @NotNull(message = "用户账号不能为空")
    private String userName;

    @Schema(description = "用户昵称")
    @NotNull(message = "用户昵称不能为空")
    private String nickName;

    @Schema(description = "身份证号")
    @NotNull(message = "身份证号不能为空")
    private String userCard;

    @Schema(description = "用户类型[00平台管理员|01老干局用户|02学校用户|03注册学员|09其他用户]")
    @NotNull(message = "用户类型[00平台管理员|01老干局用户|02学校用户|03注册学员|09其他用户]不能为空")
    private String userType;

    @Schema(description = "用户邮箱")
    @NotNull(message = "用户邮箱不能为空")
    private String email;

    @Schema(description = "手机号码")
    @NotNull(message = "手机号码不能为空")
    private String phonenumber;

    @Schema(description = "用户性别[1男2女3未知]")
    @NotNull(message = "用户性别[1男2女3未知]不能为空")
    private String sex;

    @Schema(description = "头像地址")
    @NotNull(message = "头像地址不能为空")
    private String avatar;

    @Schema(description = "密码")
    @NotNull(message = "密码不能为空")
    private String password;

    @Schema(description = "最后登录IP")
    @NotNull(message = "最后登录IP不能为空")
    private String loginIp;

    @Schema(description = "最后登录时间")
    @NotNull(message = "最后登录时间不能为空")
    private LocalDateTime loginDate;

    @Schema(description = "备注")
    @NotNull(message = "备注不能为空")
    private String remark;

    @Schema(description = "部门名称(逗号分隔)")
    @NotNull(message = "部门名称(逗号分隔)不能为空")
    private String deptNames;

    @Schema(description = "岗位名称(逗号分隔)")
    @NotNull(message = "岗位名称(逗号分隔)不能为空")
    private String postNames;

    @Schema(description = "角色名称(逗号分隔)")
    @NotNull(message = "角色名称(逗号分隔)不能为空")
    private String roleNames;

    @Schema(description = "创建者")
    @NotNull(message = "创建者不能为空")
    private String createBy;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    @NotNull(message = "更新者不能为空")
    private String updateBy;

    @Schema(description = "更新时间")
    @NotNull(message = "更新时间不能为空")
    private LocalDateTime updateTime;

    @Schema(description = "帐号状态[0正常 1停用]")
    @NotNull(message = "帐号状态[0正常 1停用]不能为空")
    private String status;

    @Schema(description = "删除标志[0代表存在 2代表删除]")
    @NotNull(message = "删除标志[0代表存在 2代表删除]不能为空")
    private String delFlag;

    @Schema(description = "渝快政用户唯一标识")
    @NotNull(message = "渝快政用户唯一标识不能为空")
    private Long accountId;

}