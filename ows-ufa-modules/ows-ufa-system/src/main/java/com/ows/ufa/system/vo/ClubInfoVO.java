package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ows.ufa.system.entity.ClubMember;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description = "VO")
public class ClubInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团照片URL或文件路径")
    private String clubPhoto;

    @Schema(description = "社团名称")
    private String clubName;

    @Schema(description = "社团简介")
    private String clubDescp;

    @Schema(description = "负责人姓名")
    private String leader;

    @Schema(description = "联系方式")
    private String phoneNumber;

    @Schema(description = "加密联系方式")
    private String phoneEncrypt;

    @Schema(description = "是否需要考试:0-否;1-是")
    private Integer isExamRequired;

    @Schema(description = "招募条件")
    private String recruitmentConditions;

    @Schema(description = "申请日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime applicationDate;

    @Schema(description = "审核日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime reviewDate;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(description = "成员数量")
    private Integer memberCount;

    @Schema(description = "活动数量")
    private Integer activityCount;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recruitmentStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime recruitmentEndDate;

    @Schema(description = "状态:0-未招募;1-招募中;2-招募结束;3-未发起招募")
    private Integer recruitmentStatus;

    private ClubMember clubMember;

    public Integer getRecruitmentStatus() {
        if (null == this.recruitmentStartDate || null == this.recruitmentEndDate) {
            this.recruitmentStatus = 3;
        } else if (null != this.recruitmentStartDate && this.recruitmentStartDate.isAfter(LocalDateTime.now())) {
            this.recruitmentStatus = 0;
        } else if (null != this.recruitmentEndDate && this.recruitmentEndDate.isBefore(LocalDateTime.now())) {
            this.recruitmentStatus = 2;
        } else {
            this.recruitmentStatus = 1;
        }
        return this.recruitmentStatus;
    }

    public LocalDateTime getReviewDate() {
        if (this.status == 1) {
            return reviewDate;
        }
        return null;
    }
}