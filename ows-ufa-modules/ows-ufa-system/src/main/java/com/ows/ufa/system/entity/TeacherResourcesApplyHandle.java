package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 师资资源申请处理流程表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@TableName("t_teacher_resources_apply_handle")
@Schema(description="师资资源申请处理流程表实体")
public class TeacherResourcesApplyHandle implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "主键ID")
    @TableId(type = IdType.AUTO)
    private Long id;

    @Schema(description = "师资资源申请ID")
    private Long teacherResourcesApplyId;

    @Schema(description = "处理人名称")
    private String handleUserName;

    @Schema(description = "上报机构")
    private String orgName;

    @Schema(description = "处理部门")
    private String handleDept;

    @Schema(description = "状态:0-待处理;1-已处理")
    private Integer status;

    @Schema(description = "事件处理描述")
    private String descp;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "事件单ID")
    private String eventNum;

}