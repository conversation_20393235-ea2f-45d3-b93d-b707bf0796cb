package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@TableName("base_teacher")
@Schema(description ="实体")
public class BaseTeacher implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "教师表")
    @TableId(type = IdType.AUTO)
    private String id;

    @Schema(description = "教师名称")
    private String tchName;

    @Schema(description = "身份证号")
    private String tchCard;

    @Schema(description = "生日")
    private String tchBirthday;

    @Schema(description = "性别")
    private String tchSex;

    @Schema(description = "手机号")
    private String tchPhone;

    @Schema(description = "家庭住址")
    private String tchAddr;

    @Schema(description = "毕业学校")
    private String tchGraduatedSchool;

    @Schema(description = "学历")
    private String tchEdu;

    @Schema(description = "创建人")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    private String updateBy;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "来源")
    private String tchSource="1";
}