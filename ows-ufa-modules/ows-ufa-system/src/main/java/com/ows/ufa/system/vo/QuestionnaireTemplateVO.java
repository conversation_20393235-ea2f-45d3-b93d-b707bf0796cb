package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 问卷调查信息模板表
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Data
@Schema(description ="问卷调查信息模板表VO")
public class QuestionnaireTemplateVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "封面图片")
    private String coverImage;

    @Schema(description = "发起单位")
    private String initiator;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "填写权限:0-开放填写;1-仅指定范围填写")
    private Integer fillPermission;

    @Schema(description = "参与限制:0-不限制(可重复参加);1-只能参加一次")
    private Integer participationLimit;

    @Schema(description = "是否允许提交问卷后修改:0-不允许;1-可修改")
    private Integer allowModify;

    @Schema(description = "限制收集总数")
    private Integer collectionLimit;

    @Schema(description = "状态:0-未发布;1-收集中;2-已结束")
    private Integer status;

    @Schema(description = "问卷调查内容")
    private String contentJson;

    @Schema(description = "删除标志：0-删除；1-有效")
    private Integer delFlag;

    @Schema(description = "数据权限部门id")
    private String deptId;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "是否限制收集总数:0-不限制;1-限制")
    private Integer collection;

    @Schema(description = "标题描述")
    private String titleDesc;

    @Schema(description = "模板图片")
    private String templateUrl;
}