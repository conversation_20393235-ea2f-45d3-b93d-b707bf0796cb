package com.ows.ufa.system.vo.count;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="社团统计VO")
public class ClubInfoCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "社团数量")
    private long clubCount;

    @Schema(description = "社团成员数量")
    private long memberCount;

    @Schema(description = "社团活动数量")
    private long activityCount;

}