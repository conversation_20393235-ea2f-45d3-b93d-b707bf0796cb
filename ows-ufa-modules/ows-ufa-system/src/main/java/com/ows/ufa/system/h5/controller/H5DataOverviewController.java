package com.ows.ufa.system.h5.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.form.ReviewForm;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.service.ClubMemberService;
import com.ows.ufa.system.service.DataOverviewService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "h5/dataOverview")
@Tag(name = "dataOverview", description = "h5数据概览")
public class H5DataOverviewController extends BaseController {

    private final DataOverviewService dataOverviewService;

    @GetMapping("homePage-number")
    @Operation(summary = "首页数据-数量统计")
    public AjaxResult homePageNumber() {
        return success(dataOverviewService.homePageNumber());
    }

    @GetMapping("homePage-pay")
    @Operation(summary = "首页数据-缴费统计")
    public AjaxResult homePagePay(@RequestParam(value = "type") Integer type) {
        return success(dataOverviewService.homePagePay(type));
    }

    @GetMapping("homePage-signUp")
    @Operation(summary = "首页数据-招生统计")
    public AjaxResult homePageSignUp() {
        return success(dataOverviewService.homePageSignUp());
    }

    @GetMapping("dept-situation")
    @Operation(summary = "院系情况")
    public AjaxResult deptSituation(@RequestParam(value = "deptId",required = false) String deptId) {
        return success(dataOverviewService.deptSituation(deptId));
    }

    @GetMapping("dept")
    @Operation(summary = "院系列表")
    public AjaxResult dept() {
        return success(dataOverviewService.dept());
    }

    @GetMapping("dept-signUp")
    @Operation(summary = "院系招生统计")
    public AjaxResult deptSignUp() {
        return success(dataOverviewService.deptSignUp());
    }

    @GetMapping("student")
    @Operation(summary = "在籍学员统计")
    public AjaxResult student() {
        return success(dataOverviewService.student());
    }

}
