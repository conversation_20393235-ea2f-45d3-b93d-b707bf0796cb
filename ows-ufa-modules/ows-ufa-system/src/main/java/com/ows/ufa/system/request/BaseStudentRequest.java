package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 生源学生表
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@Data
@Schema(description ="生源学生表Request")
public class BaseStudentRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "姓名")
    private String stuName;


}