package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.BaseSchool;
import com.ows.ufa.system.entity.BaseSchoolDept;
import com.ows.ufa.system.vo.BaseSchoolDeptVO;
import com.ows.ufa.system.form.BaseSchoolDeptForm;
import com.ows.ufa.system.request.BaseSchoolDeptRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
public interface BaseSchoolDeptService extends IService<BaseSchoolDept> {

    List<BaseSchoolDept> queryBaseSchoolDepts();

    String queryAncestors(String deptId);

    List<BaseSchool> querySchools();
}
