package com.ows.ufa.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import java.time.LocalDateTime;

@Data
@TableName("t_area_code")
public class AreaCode {
    @TableId(type = IdType.AUTO)
    private Integer id;
    private String areaCode;
    private String areaName;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
} 