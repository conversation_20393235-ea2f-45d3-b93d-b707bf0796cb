package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.utils.StringUtils;
import com.ows.ufa.common.core.utils.file.FileUtils;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.config.AppConfig;
import com.ows.ufa.system.config.ServerConfig;
import com.ows.ufa.system.util.FileUploadUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 * @since 2025-01-17
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "common")
@Tag(name = "common", description = "通用请求处理")
public class CommonController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    private final ServerConfig serverConfig;
    private final AppConfig appConfig;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.isValidFilename(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = "";
            if (fileName.indexOf("_") >= 0) {
                realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            } else {
                realFileName = fileName;
            }

            String filePath = appConfig.getDownloadPath() + fileName;

            response.setCharacterEncoding("utf-8");
            response.setContentType("multipart/form-data");
            response.setHeader("Content-Disposition",
                    "attachment;fileName=" + FileUtils.setFileDownloadHeader(request, realFileName));
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = appConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = appConfig.getFileBase() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

}
