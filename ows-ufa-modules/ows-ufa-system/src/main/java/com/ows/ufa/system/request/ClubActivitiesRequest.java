package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="Request")
public class ClubActivitiesRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "社团名称")
    private String clubInfoName;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点")
    private String activityLocation;

    @Schema(description = "活动状态:1-未开始;2-报名中;3-进行中;4-已结束")
    private int progressStatus;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(hidden = true)
    private List<String> ancestors;

    @Schema(description = "h5查询条件")
    private String searchText;
}