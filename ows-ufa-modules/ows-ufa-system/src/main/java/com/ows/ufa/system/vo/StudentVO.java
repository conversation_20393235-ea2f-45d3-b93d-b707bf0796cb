package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 学员信息VO
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Schema(description  = "学员信息VO")
public class StudentVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "学员姓名")
    private String userName;

    @Schema(description = "学员性别")
    private String userSex;

    @Schema(description = "学员头像图片链接")
    private String userFilePath;

    @Schema(description = "报名课程信息列表")
    private List<CourseVO> courseList; // 假设以JSON字符串形式存储
}