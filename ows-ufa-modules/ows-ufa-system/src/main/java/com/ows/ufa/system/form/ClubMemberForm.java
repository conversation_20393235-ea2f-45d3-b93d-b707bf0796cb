package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description ="VO")
public class ClubMemberForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团ID")
    @NotNull(message = "社团ID不能为空")
    private Long clubInfoId;

    @Schema(description = "姓名")
    @NotNull(message = "姓名不能为空")
    private String name;

    @Schema(description = "性别:0-男;1-女;")
    @NotNull(message = "性别:0-男;1-女;不能为空")
    private Integer sex;

    @Schema(description = "联系电话")
    @NotNull(message = "联系电话不能为空")
    private String phoneNumber;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    @NotNull(message = "状态:0-待审核;1-审核通过;2-审核不通过不能为空")
    private Integer status;

}