package com.ows.ufa.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ReviewStatus implements IEnum<Integer> {
    //状态:0-待审核;1-审核通过;2-审核不通过
    PENDING(0, "待审核"),
    PASS(1, "审核通过"),
    REJECT(2, "审核不通过");
    ReviewStatus(Integer code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final Integer code;
    @JsonValue
    private final String descp;

    @Override
    public Integer getValue() {
        return this.code;
    }

}
