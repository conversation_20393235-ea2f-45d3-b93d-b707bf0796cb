package com.ows.ufa.system.form;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 缴费订单信息表
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Schema(description="缴费订单信息Form")
public class AdmissionNoticeForm implements Serializable {

    private static final long serialVersionUID=1L;

    @Schema(description = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "缴费订单编号")
    @NotNull(message = "缴费订单编号不能为空")
    private String orderNo;

    @Schema(description = "缴费时间")
    @NotNull(message = "缴费时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime payTime;

    @Schema(description = "学员姓名")
    @NotNull(message = "学员姓名不能为空")
    private String userName;

    @Schema(description = "学校图片")
    @NotNull(message = "学校图片不能为空")
    private String logUrl;

    @Schema(description = "学校名称")
    @NotNull(message = "学校名称不能为空")
    private String schoolName;

    @Schema(description = "系名称")
    @NotNull(message = "系名称不能为空")
    private String deptName;

    @Schema(description = "专业名称")
    @NotNull(message = "专业名称不能为空")
    private String majorName;

    @Schema(description = "课程名称")
    @NotNull(message = "课程名称不能为空")
    private String courseName;

    @Schema(description = "退费时间")
    @NotNull(message = "退费时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime refundTime;

    @Schema(description = "开课时间")
    @NotNull(message = "开课时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime courseStartTime;

    @Schema(description = "学校地址")
    @NotNull(message = "学校地址不能为空")
    private String schoolAddr;

}