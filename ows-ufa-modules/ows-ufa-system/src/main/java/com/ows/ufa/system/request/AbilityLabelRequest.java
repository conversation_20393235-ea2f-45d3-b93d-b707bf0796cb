package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 能力标签表
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Data
@Schema(description ="能力标签表Request")
public class AbilityLabelRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "标签主题")
    private String title;

    @Schema(hidden = true)
    private List<String> ancestors;
}