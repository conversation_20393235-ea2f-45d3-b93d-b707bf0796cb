package com.ows.ufa.system.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class QRCodeGenerator {
    public static boolean generateQRCodeImage(ByteArrayOutputStream baos, String content) {
        try {
            // 设置二维码参数
            Map<EncodeHintType, ErrorCorrectionLevel> hints = new HashMap<>();
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H); // 设置容错率

            // 创建二维码写入器
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, 200, 200, hints);

            // 将BitMatrix转换为图像并写入到ByteArrayOutputStream中
            MatrixToImageWriter.writeToStream(bitMatrix, "PNG", baos);
            return true;
        } catch (WriterException | IOException e) {
            log.error("生成二维码失败," + e.getMessage(), e);
            return false;
        }
    }

    public static boolean generateQRCodeImage(String file, String text) {
        try {
            generateQRCodeImage(text, 350, 350, file);
        } catch (WriterException e) {
            log.error("生成二维码失败," + file + e.getMessage(), e);
            return false;
        } catch (IOException e) {
            log.error("生成二维码失败," + file + e.getMessage(), e);
            return false;
        }
        return true;
    }

    private static void generateQRCodeImage(String text, int width, int height, String filePath)
            throws WriterException, IOException {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        BitMatrix bitMatrix = qrCodeWriter.encode(text, BarcodeFormat.QR_CODE, width, height, hints);
        Path path = FileSystems.getDefault().getPath(filePath);
        MatrixToImageWriter.writeToPath(bitMatrix, "PNG", path);
    }

}
