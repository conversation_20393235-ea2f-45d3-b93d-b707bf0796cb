package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.core.utils.StringUtils;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.entity.BaseSchool;
import com.ows.ufa.system.entity.BaseSchoolDept;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.vo.BaseSchoolDeptVO;
import com.ows.ufa.system.form.BaseSchoolDeptForm;
import com.ows.ufa.system.request.BaseSchoolDeptRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.BaseSchoolDeptMapper;
import com.ows.ufa.system.service.BaseSchoolDeptService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.time.LocalDateTime;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class BaseSchoolDeptServiceImpl extends ServiceImpl<BaseSchoolDeptMapper, BaseSchoolDept> implements BaseSchoolDeptService {

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public List<BaseSchoolDept> queryBaseSchoolDepts() {
        LambdaQueryWrapper<BaseSchoolDept> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(BaseSchoolDept::getId,BaseSchoolDept::getDeptName);
        queryWrapper.eq(BaseSchoolDept::getStatus,1).eq(BaseSchoolDept::getIsEnable,1);
        queryWrapper.orderByAsc(BaseSchoolDept::getDeptSort);
        return this.list(queryWrapper);
    }

    @Override
    @DataSource(value = DataSourceType.SLAVE)
    public String queryAncestors(String deptId) {
        if(StringUtils.isBlank(deptId)){
            return "";
        }
        List<String> ancestors = this.baseMapper.queryAncestors(deptId);
        return StringUtils.join(ancestors,",");
    }

    @Override
    public List<BaseSchool> querySchools() {
        return this.baseMapper.querySchools();
    }
}