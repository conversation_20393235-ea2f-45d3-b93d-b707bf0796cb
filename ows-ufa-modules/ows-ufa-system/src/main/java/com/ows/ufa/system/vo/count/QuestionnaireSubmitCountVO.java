package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="问卷调查信息填报列表统计VO")
public class QuestionnaireSubmitCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "已填数量")
    private long filledQuantity;

    @Schema(description = "未填数量")
    private long unfilledQuantity;

    @Schema(description = "已结束数量")
    private long completedQuantity;

}