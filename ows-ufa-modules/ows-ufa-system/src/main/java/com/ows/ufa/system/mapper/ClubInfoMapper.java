package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.ClubInfo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.ows.ufa.system.vo.ClubInfoVO;
import com.ows.ufa.system.vo.count.ClubInfoCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Mapper
public interface ClubInfoMapper extends BaseMapper<ClubInfo> {

    List<ClubInfoVO> queryClubInfos(@Param("req") ClubInfoRequest req);

    ClubInfoVO findClubInfo(Long id);

    List<ClubInfoVO> h5queryClubInfos(@Param("req") ClubInfoRequest req);
}