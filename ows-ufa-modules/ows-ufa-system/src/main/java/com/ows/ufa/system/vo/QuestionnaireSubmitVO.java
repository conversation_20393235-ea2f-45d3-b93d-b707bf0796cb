package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 问卷调查信息填报表
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@Schema(description ="问卷调查信息填报表VO")
public class QuestionnaireSubmitVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "提交用户名")
    private String userName;

    @Schema(description = "提交部门名")
    private String deptName;

    @Schema(description = "问卷填写内容")
    private List<QuestionnaireSubmitRadioVO> contentJson;
    @Data
    @Schema(description = "组件属性详细信息")
    public static class QuestionnaireSubmitRadioVO implements Serializable {
        @Schema(description = "主键ID")
        private Long id;
        @Schema(description = "组件名称")
        private String name;
        @Schema(description = "用户选择的值")
        private Object value;
    }
}