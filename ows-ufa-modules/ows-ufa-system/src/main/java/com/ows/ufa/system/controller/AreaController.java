package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.service.AreaService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "area")
@Tag(name = "area", description = "地区接口")
public class AreaController extends BaseController {

    private final AreaService AreaServiceImpl;

    @GetMapping("list")
    @Operation(summary = "列表查询")
    public AjaxResult listAreaByPage() {
        return success(AreaServiceImpl.queryAreas());
    }
}
