package com.ows.ufa.system.service.impl;

import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.entity.QuestionnaireTemplate;
import com.ows.ufa.system.vo.QuestionnaireTemplateVO;
import com.ows.ufa.system.form.QuestionnaireTemplateForm;
import com.ows.ufa.system.request.QuestionnaireTemplateRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.QuestionnaireTemplateMapper;
import com.ows.ufa.system.service.QuestionnaireTemplateService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;
import java.time.LocalDateTime;
import com.ows.ufa.system.enums.DelFlag;

/**
 * <p>
 * 问卷调查信息模板表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class QuestionnaireTemplateServiceImpl extends ServiceImpl<QuestionnaireTemplateMapper, QuestionnaireTemplate> implements QuestionnaireTemplateService {

    @Override
    public List<QuestionnaireTemplate> queryQuestionnaireTemplates(QuestionnaireTemplateRequest req) {
        QuestionnaireTemplate entity = (QuestionnaireTemplate) DataTransfer.transfer(req, QuestionnaireTemplate.class);
        LambdaQueryWrapper<QuestionnaireTemplate> queryWrapper = new LambdaQueryWrapper<>(entity);
        queryWrapper.eq(QuestionnaireTemplate::getDelFlag, DelFlag.VALID.getCode());
        return this.list(queryWrapper);
    }

    @Override
    public QuestionnaireTemplateVO findQuestionnaireTemplate(Long id) {
        QuestionnaireTemplate entity = this.getById(id);
        QuestionnaireTemplateVO vo = (QuestionnaireTemplateVO) DataTransfer.transfer(entity, QuestionnaireTemplateVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveQuestionnaireTemplate(QuestionnaireTemplateForm form) {
        QuestionnaireTemplate entity = (QuestionnaireTemplate) DataTransfer.transfer(form, QuestionnaireTemplate.class);
        entity.setCreateTime(LocalDateTime.now());
        entity.setCreateAt(SecurityUtils.getThirdUserid());
        entity.setDeptId(SecurityUtils.getThirdDeptId());
        entity.setInitiator(SecurityUtils.getThirdDeptName());
        this.save(entity);
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateQuestionnaireTemplate(QuestionnaireTemplateForm form) {
        QuestionnaireTemplate entity = (QuestionnaireTemplate) DataTransfer.transfer(form, QuestionnaireTemplate.class);
        entity.setUpdateTime(LocalDateTime.now());
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeQuestionnaireTemplate(Long id) {
        return this.lambdaUpdate().eq(QuestionnaireTemplate::getId,id).set(QuestionnaireTemplate::getDelFlag,DelFlag.DELETE.getCode()).update();
    }
}