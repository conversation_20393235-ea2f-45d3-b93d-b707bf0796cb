package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.Questionnaire;
import com.ows.ufa.system.vo.QuestionnaireVO;
import com.ows.ufa.system.form.QuestionnaireForm;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ows.ufa.system.vo.count.QuestionnaireCountVO;

import java.util.List;
/**
 * <p>
 * 问卷调查信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
public interface QuestionnaireService extends IService<Questionnaire> {

    List<QuestionnaireVO> queryQuestionnaires(QuestionnaireRequest request);

    QuestionnaireVO findQuestionnaire(Long id);

    Long saveQuestionnaire(QuestionnaireForm form);

    boolean updateQuestionnaire(QuestionnaireForm form);

    boolean removeQuestionnaire(Long id);

    boolean releaseQuestionnaire(QuestionnaireForm form);

    boolean stopQuestionnaire(Long id);

    QuestionnaireCountVO listCount();

    boolean releaseAllQuestionnaire(QuestionnaireForm form);

    Long createQuestionnaire(QuestionnaireForm form);

    List<QuestionnaireVO> queryOtherQuestionnaires(QuestionnaireRequest req);

    boolean stopQuestionnaire(Long id,String appName);
}
