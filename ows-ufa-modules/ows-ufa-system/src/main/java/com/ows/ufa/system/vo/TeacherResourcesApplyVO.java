package com.ows.ufa.system.vo;

import com.ows.ufa.system.entity.TeacherResourcesApplyHandle;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 师资资源申请表
 *
 * <AUTHOR>
 * @since 2024-12-19
 */
@Data
@Schema(description="师资资源申请表VO")
public class TeacherResourcesApplyVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "申请用户ID")
    private Long userId;

    @Schema(description = "事件标题")
    private String title;

    @Schema(description = "事件描述")
    private String descp;

    @Schema(description = "状态:0-待处理;1-已处理")
    private Integer status;

    @Schema(description = "用户姓名")
    private String userName;

    @Schema(description = "用户学校名称")
    private String schoolName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    private List<TeacherResourcesApplyHandle> handles;

}