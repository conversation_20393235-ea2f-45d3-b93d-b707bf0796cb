package com.ows.ufa.system.vo.excel;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.ows.ufa.common.core.annotation.Excel;
import lombok.Data;

/**
 * 问卷调查信息表
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Data
public class QuestionnaireExcelVO implements Serializable {

    private static final long serialVersionUID=1L;


    @Excel(name = "标题", type = Excel.Type.ALL)
    private String title;

    @Excel(name = "封面图片", type = Excel.Type.ALL)
    private String coverImage;

    @Excel(name = "发起单位", type = Excel.Type.ALL)
    private String initiator;

    @Excel(name = "开始时间", type = Excel.Type.ALL)
    private LocalDateTime startTime;

    @Excel(name = "结束时间", type = Excel.Type.ALL)
    private LocalDateTime endTime;

    @Excel(name = "填写权限:0-开放填写;1-仅指定范围填写", type = Excel.Type.ALL)
    private Integer fillPermission;

    @Excel(name = "参与限制:0-不限制(可重复参加);1-只能参加一次", type = Excel.Type.ALL)
    private Integer participationLimit;

    @Excel(name = "是否允许提交问卷后修改:0-不允许;1-可修改", type = Excel.Type.ALL)
    private Integer allowModify;

    @Excel(name = "限制收集总数", type = Excel.Type.ALL)
    private Integer collectionLimit;

    @Excel(name = "状态:0-未发布;1-收集中;2-已结束", type = Excel.Type.ALL)
    private Integer status;

    @Excel(name = "问卷调查内容", type = Excel.Type.ALL)
    private String contentJson;

    @Excel(name = "删除标志：0-删除；1-有效", type = Excel.Type.ALL)
    private Integer delFlag;

    @Excel(name = "数据权限部门id", type = Excel.Type.ALL)
    private String deptId;

    @Excel(name = "创建人", type = Excel.Type.ALL)
    private String createAt;

    @Excel(name = "更新人", type = Excel.Type.ALL)
    private String updateAt;

    @Excel(name = "创建时间", type = Excel.Type.ALL)
    private LocalDateTime createTime;

    @Excel(name = "更新时间", type = Excel.Type.ALL)
    private LocalDateTime updateTime;
}