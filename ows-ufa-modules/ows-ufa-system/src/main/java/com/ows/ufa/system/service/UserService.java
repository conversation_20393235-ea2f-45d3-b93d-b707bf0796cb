package com.ows.ufa.system.service;

import com.ows.ufa.system.entity.User;
import com.ows.ufa.system.vo.UserVO;
import com.ows.ufa.system.form.UserForm;
import com.ows.ufa.system.request.UserRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
/**
 * <p>
 * 用户信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-17
 */
public interface UserService extends IService<User> {
    UserVO findUser(Long id);
}
