package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="社团统计VO")
public class ClubReviewCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "社团待审核数量")
    private long pendingAuditCount;

    @Schema(description = "社团已审核数量")
    private long approvedAuditCount;


}