package com.ows.ufa.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.common.core.constant.CacheConstants;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.utils.DateUtils;
import com.ows.ufa.common.core.utils.Sm4Utils;
import com.ows.ufa.common.core.utils.file.ImageUtils;
import com.ows.ufa.system.config.DataSource;
import com.ows.ufa.system.domain.response.*;
import com.ows.ufa.system.domain.vo.StudentStatisticsVo;
import com.ows.ufa.system.entity.*;
import com.ows.ufa.system.enums.DataSourceType;
import com.ows.ufa.system.mapper.*;
import com.ows.ufa.system.service.DataOverviewService;
import com.ows.ufa.system.service.DatavInitService;
import com.ows.ufa.system.service.OpenService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 数据大屏初始 服务层处理
 *
 */
@Service
public class DatavInitServiceImpl implements DatavInitService
{

    private static final Logger log = LoggerFactory.getLogger(DatavInitServiceImpl.class);

    @Autowired
    private DistrictYkzUidMapper districtYkzUidMapper;

    @Value("${ykz.appSecret}")
    private String sm4Secret;

    @Autowired
    private OpenService openService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public DistrictYkzUid getDistrictYkzUidByToken(String token) {
        //获取api-token
        String apiToken = openService.getYkzApiToken();
        //获取access-token
        String accessToken = getYkzAccessToken(apiToken);
        //获取愉快政UID
        Map<String,Object> uidMap = openService.getYkzUid(token,accessToken,apiToken);
        String uid = uidMap.get("accountId").toString();
        //先解密uid
        Base64.Decoder decoder = Base64.getDecoder();
        try {
            String id = new String(Sm4Utils.decrypt_Ecb_Padding(sm4Secret, decoder.decode(uid)));
            //防止出现多条，报异常，目前默认拿第一条，如果后续多条都传回，则修改返回List
            LambdaQueryWrapper<DistrictYkzUid> districtYkzUidLambdaQueryWrapper = new LambdaQueryWrapper<>();
            districtYkzUidLambdaQueryWrapper.eq(DistrictYkzUid::getUid,id);
            List<DistrictYkzUid> districtYkzUids = districtYkzUidMapper.selectList(districtYkzUidLambdaQueryWrapper);
            if(districtYkzUids != null && districtYkzUids.size() > 0){
                return districtYkzUids.get(0);
            }else {
                return new DistrictYkzUid();
            }
        }catch (Exception e){
            return new DistrictYkzUid();
        }
    }

    @Override
    public Boolean verifyToken(String token) {
        Map response = openService.verifyToken(token);
        if(response != null && ObjectUtil.isNotEmpty(response.get("data"))){
            return response.get("data").toString().equals("true");
        }else{
            return false;
        }
    }

    /**
     *  获取愉快政accessToken，查缓存
     * @return
     */
    private String getYkzAccessToken(String apiToken){
        //第一先从redis获取token
        Object accessToken = redisTemplate.opsForValue().get(CacheConstants.YKZ_ACCESS_TOKEN);
        if(ObjectUtil.isNotEmpty(accessToken)){
            return accessToken.toString();
        }
        //执行查询
        Map<String,Object> dataMap = openService.getYkzAccessToken(apiToken);
        redisTemplate.opsForValue().set(CacheConstants.YKZ_ACCESS_TOKEN,dataMap.get("accessToken").toString(),Long.parseLong(dataMap.get("expiresIn").toString()), TimeUnit.SECONDS);
        return dataMap.get("accessToken").toString();
    }
}
