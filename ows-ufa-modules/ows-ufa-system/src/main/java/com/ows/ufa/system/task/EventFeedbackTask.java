package com.ows.ufa.system.task;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ows.ufa.system.entity.NoticeLog;
import com.ows.ufa.system.entity.TeacherResourcesApply;
import com.ows.ufa.system.enums.NoticeType;
import com.ows.ufa.system.service.NoticeLogService;
import com.ows.ufa.system.service.OpenService;
import com.ows.ufa.system.service.TeacherResourcesApplyService;
import com.ows.ufa.system.util.DateUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Component
@RequiredArgsConstructor
public class EventFeedbackTask {

    private final NoticeLogService noticeLogService;
    private final OpenService openService;
    private final TeacherResourcesApplyService teacherResourcesApplyService;

    @Scheduled(fixedRate = 60000) // 每5分钟执行一次
    public void processEventFeedback() {
        try {
            // 查询待处理的事件通知
            LambdaQueryWrapper<NoticeLog> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(NoticeLog::getNoticeType, NoticeType.EVENT.getCode())
                    .eq(NoticeLog::getStatus, 1);

            List<NoticeLog> noticeLogs = noticeLogService.list(queryWrapper);

            for (NoticeLog noticeLog : noticeLogs) {
                try {
                    // 解析通知内容
                    JSONObject eventContent = JSON.parseObject(noticeLog.getContent());

                    // 构建反馈请求
                    Map<String, Object> feedback = buildFeedbackRequest(eventContent);
                    System.out.println(JSON.toJSONString(feedback));
                    // 发送反馈
                    openService.sendFeedback(feedback);
                    // 更新通知状态为已处理
                    noticeLog.setStatus(2);
                    noticeLogService.updateById(noticeLog);
                    log.info("事件反馈成功, noticeLogId: {}", noticeLog.getLogId());
                } catch (Exception e) {
                    log.error("事件反馈失败, noticeLogId: {}, error: {}", noticeLog.getLogId(), e.getMessage());
                    noticeLog.setStatus(3); // 状态3表示处理失败
                    noticeLogService.updateById(noticeLog);
                }
            }
        } catch (Exception e) {
            log.error("执行事件反馈任务失败", e);
        }
    }

    private Map<String, Object> buildFeedbackRequest(JSONObject eventContent) {
        Map<String, Object> feedback = new HashMap<>();

        // 设置基本信息
        JSONObject eventInfo = eventContent.getJSONObject("eventInfo");
        feedback.put("sourceSystemCode", eventInfo.getString("sourceSystemCode"));
        feedback.put("eventNum", eventInfo.getString("eventNum"));

        // 构建taskOrderOutPuts
        List<Map<String, Object>> taskOrderOutPuts = new ArrayList<>();
        Map<String, Object> taskOrder = new HashMap<>();

        taskOrder.put("taskOrderId", eventContent.getString("taskOrderId"));
        taskOrder.put("taskOrderDealStatus", "1");
        taskOrder.put("taskOrderEndStatus", "1");

        // 构建taskOrderOutputArray
        List<Map<String, Object>> outputArray = new ArrayList<>();
        Map<String, Object> output = new HashMap<>();
        output.put("fieldCode", "executeContent");
        output.put("fieldName", "办理情况");
        List<String> fieldValue = new ArrayList<>();
        fieldValue.add("已入库");
        output.put("fieldValue", fieldValue);
        output.put("addToExtends", false);
        outputArray.add(output);
        taskOrder.put("taskOrderOutputArray", outputArray);

        // 从eventContent中获取logList并筛选
        List<Map<String, Object>> filteredLogList = new ArrayList<>();
        JSONArray logList = eventContent.getJSONArray("logList");
        String taskCode = null;
        String taskName = null;
        if (logList != null) {
            for (int i = 0; i < logList.size(); i++) {
                JSONObject log = logList.getJSONObject(i);
                if ("师资资源整合接收入库".equals(log.getString("bizTaskName"))) {
                    Map<String, Object> logMap = new HashMap<>();
                    logMap.put("eventProcessId", log.getString("eventProcessId"));
                    logMap.put("executorId", "103075");
                    logMap.put("executor", "范文峰");
                    logMap.put("executorPhone", "15023727876");
                    logMap.put("executeDeptId", log.getString("executeDeptId"));
                    logMap.put("executeDept", log.getString("executeDept"));
                    logMap.put("executeTime", DateUtils.getTime());
                    logMap.put("executeNode", log.getString("bizTaskName"));
                    logMap.put("executeContent", "已处置");
                    logMap.put("isLastTaskOrderLog", true);
                    taskCode = log.getString("bizTaskCode");
                    taskName = log.getString("bizTaskName");
                    filteredLogList.add(logMap);
                }
            }
        }
        if (null != taskCode && null != taskName) {
            taskOrder.put("taskCode", taskCode);
            taskOrder.put("taskName", taskName);
        }else{
            JSONObject bizTask = eventContent.getJSONObject("bizTask");
            taskOrder.put("taskCode", bizTask.getString("rowGuid"));
            taskOrder.put("taskName", bizTask.getString("taskName"));
            Map<String, Object> logMap = new HashMap<>();
            LambdaQueryWrapper<TeacherResourcesApply> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(TeacherResourcesApply::getEventNum, eventInfo.getString("eventNum"));
            TeacherResourcesApply apply = teacherResourcesApplyService.getOne(queryWrapper);
            if(null == apply){
                throw new RuntimeException("未找到对应的申请信息");
            }
            logMap.put("eventProcessId", apply.getId());
            logMap.put("executorId", "103075");
            logMap.put("executor", "范文峰");
            logMap.put("executorPhone", "15023727876");
            logMap.put("executeDeptId", bizTask.getString("departmentCode"));
            logMap.put("executeDept", bizTask.getString("departmentName"));
            logMap.put("executeTime", DateUtils.getTime());
            logMap.put("executeNode", bizTask.getString("taskName"));
            logMap.put("executeContent", "已处置");
            logMap.put("isLastTaskOrderLog", true);
            filteredLogList.add(logMap);
        }

        taskOrder.put("logList", filteredLogList);
        taskOrderOutPuts.add(taskOrder);
        feedback.put("taskOrderOutPuts", taskOrderOutPuts);
        return feedback;
    }

} 