package com.ows.ufa.system.util;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

public class JarUploader {

    private static final String SERVER_URL = "https://cqlncs.12399.gov.cn:8093/api/system/open";
    private static final String PWD = "Ut0Ut0Ut0";
    private static final int CHUNK_SIZE = 40 * 1024 * 1024; // 40MB

    public static void main(String[] args) throws IOException {
        String jarPath = "D:\\gsyw\\ows-ufa-cloud\\ows-ufa-modules\\ows-ufa-system\\target\\ows-ufa-modules-system.jar"; // 替换为你的jar路径
        File jarFile = new File(jarPath);

        // 1. 分块
        splitAndUpload(jarFile);

        // 2. 通知服务器合并
        triggerMerge(jarFile.getName());
        System.out.println("发布测试成功！");
    }

    private static void splitAndUpload(File file) throws IOException {
        try (InputStream is = new FileInputStream(file)) {
            byte[] buffer = new byte[CHUNK_SIZE];
            int chunkIndex = 0;
            int bytesRead;

            while ((bytesRead = is.read(buffer)) > 0) {
                // 保存临时分块
                String chunkName = "chunk_" + chunkIndex + ".tmp";
                Path chunkPath = Paths.get(chunkName);
                try (FileOutputStream fos = new FileOutputStream(chunkPath.toFile())) {
                    fos.write(buffer, 0, bytesRead);
                }

                // 上传分块
                uploadChunk(chunkName, chunkIndex, file.getName());

                // 删除临时分块文件
                Files.deleteIfExists(chunkPath);

                chunkIndex++;
            }
        }
    }

    private static void uploadChunk(String chunkPath, int chunkIndex, String originalFilename) throws IOException {
        HttpURLConnection connection = (HttpURLConnection) new URL(SERVER_URL+"/uploadChunk").openConnection();
        connection.setRequestMethod("POST");
        connection.setDoOutput(true);
        String boundary = "----WebKitFormBoundary" + System.currentTimeMillis();

        // 设置请求头
        connection.setRequestProperty("Content-Type", "multipart/form-data; boundary=" + boundary);

        try (OutputStream os = connection.getOutputStream();
             PrintWriter writer = new PrintWriter(new OutputStreamWriter(os))) {

            // 添加分块文件
            writer.append("--").append(boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"file\"; filename=\"").append(chunkPath).append("\"\r\n");
            writer.append("Content-Type: application/octet-stream\r\n\r\n");
            writer.flush();

            // 写入文件内容
            try (FileInputStream fis = new FileInputStream(chunkPath)) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fis.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            // 添加其他字段
            writer.append("\r\n--").append(boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"pwd\"\r\n\r\n").append(PWD).append("\r\n");

            writer.append("--").append(boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"chunkIndex\"\r\n\r\n").append(String.valueOf(chunkIndex)).append("\r\n");

            writer.append("--").append(boundary).append("\r\n");
            writer.append("Content-Disposition: form-data; name=\"originalFilename\"\r\n\r\n").append(originalFilename).append("\r\n");

            writer.append("--").append(boundary).append("--\r\n");
            writer.flush();
        }

        // 检查响应
        if (connection.getResponseCode() != 200) {
            throw new IOException("Upload failed: " + connection.getResponseMessage());
        }
    }

    private static void triggerMerge(String filename) throws IOException {
        String mergeUrl = SERVER_URL + "/merge?filename=" + filename + "&pwd=" + PWD;
        HttpURLConnection connection = (HttpURLConnection) new URL(mergeUrl).openConnection();
        connection.setRequestMethod("POST");

        if (connection.getResponseCode() != 200) {
            throw new IOException("Merge failed: " + connection.getResponseMessage());
        }
    }
}