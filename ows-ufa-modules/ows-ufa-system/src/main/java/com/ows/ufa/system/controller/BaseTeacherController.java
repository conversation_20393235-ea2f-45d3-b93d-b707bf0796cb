package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.request.BaseTeacherRequest;
import com.ows.ufa.system.service.BaseTeacherService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "baseTeacher")
@Tag(name = "baseTeacher", description = "共享师资库接口")
public class BaseTeacherController extends BaseController {

    private final BaseTeacherService BaseTeacherServiceImpl;

    @GetMapping("count")
    @Operation(summary = "统计")
    public AjaxResult countBaseTeacher() {
        return success(BaseTeacherServiceImpl.countBaseTeacher());
    }

    @GetMapping("list")
    @Operation(summary = "共享师资库分页查询")
    public AjaxResult listBaseTeacherByPage(BaseTeacherRequest request) {
        startPage();
        return success(getDataTable(BaseTeacherServiceImpl.querySchoolTeachers(request)));
    }
}
