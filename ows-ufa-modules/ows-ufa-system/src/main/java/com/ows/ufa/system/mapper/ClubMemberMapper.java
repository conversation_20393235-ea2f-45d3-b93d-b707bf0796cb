package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.ClubMember;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.request.ClubMemberRequest;
import com.ows.ufa.system.vo.ClubMemberVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Mapper
public interface ClubMemberMapper extends BaseMapper<ClubMember> {

    List<ClubMemberVO> queryClubMembers(@Param("req") ClubMemberRequest request);

    ClubMemberVO findClubMember(@Param("id") Long id);
}