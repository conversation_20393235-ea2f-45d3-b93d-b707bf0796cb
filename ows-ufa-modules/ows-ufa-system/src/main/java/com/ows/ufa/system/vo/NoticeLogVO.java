package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 通知书推送信息日志记录表
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Data
@Schema(description ="通知书推送信息日志记录表VO")
public class NoticeLogVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long logId;

    @Schema(description = "推送类型:0-缴费;1-退费")
    @NotNull(message = "推送类型:0-缴费;1-退费不能为空")
    private Integer noticeType;

    @Schema(description = "JSON内容")
    @NotNull(message = "JSON内容不能为空")
    private String content;

    @Schema(description = "时间")
    @NotNull(message = "时间不能为空")
    private LocalDateTime createTime;

}