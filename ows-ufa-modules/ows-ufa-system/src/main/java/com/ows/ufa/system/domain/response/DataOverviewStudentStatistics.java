package com.ows.ufa.system.domain.response;

import com.ows.ufa.system.domain.vo.StudentStatisticsVo;
import com.ows.ufa.system.entity.ZsPlanClass;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 在籍学院统计数据
 * 
 * <AUTHOR>
 */
@Data
public class DataOverviewStudentStatistics
{
    private static final long serialVersionUID = 1L;

    //政治面貌统计 政治面貌[1党员 2团员 3民主党派 4无党派 5群众]
    List<StudentStatisticsVo> studentPolitical;

    //退休状态统计 离退状态[1离休 2机关单位退休 3事业单位退休 4企业退休 5个体]
    List<StudentStatisticsVo> studentRetireState;

    //学历统计 文化程度[1初中以下 2初中至高中 3大专及以上]
    List<StudentStatisticsVo> studentEduLevel;

    //居住情况统计 居住情况[1与配偶居住 2与子女居住 3独居 9其他]
    List<StudentStatisticsVo> studentLiveState;

    //性别统计 性别[1男 2女]
    List<StudentStatisticsVo> studentSex;

    //年龄统计 年龄[1 50岁以下 2 50-59岁 3 60-69岁 4 70岁以上]
    List<StudentStatisticsVo> studentAge;

}
