package com.ows.ufa.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum TeacherStatus implements IEnum<Integer> {
    //状态:0-未推送;1-已推送
    NOT_PUSHED(0, "未推送"),
    PUSHED(1, "已推送");
    TeacherStatus(Integer code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final Integer code;
    @JsonValue
    private final String descp;

    @Override
    public Integer getValue() {
        return this.code;
    }

}
