package com.ows.ufa.system.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 问卷调查信息表
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Data
@Schema(description = "问卷调查信息表VO")
public class OtherQuestionnaireVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "标题描述")
    private String titleDesc;

    @Schema(description = "封面图片")
    private String coverImage;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "是否限制收集总数:0-不限制;1-限制")
    private Integer collection;

    @Schema(description = "限制收集总数")
    private Integer collectionLimit;

    @Schema(description = "状态:0-未发布;1-收集中;2-已结束")
    private Integer status;

    @Schema(description = "问卷调查内容")
    private String contentJson;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    private String appName;

}