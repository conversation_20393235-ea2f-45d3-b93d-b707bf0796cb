package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;

import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ows.ufa.system.entity.ClubMemberActivity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2025-01-22
 */
@Data
@Schema(description = "VO")
public class ClubActivitiesVO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团ID")
    private Long clubInfoId;

    @Schema(description = "社团名称")
    private String clubInfoName;

    @Schema(description = "活动名称")
    private String activityName;

    @Schema(description = "活动地点")
    private String activityLocation;

    @Schema(description = "报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registrationTime;

    @Schema(description = "截止报名时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime registrationDeadline;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    @Schema(description = "学员报名是否需要审核:0-否;1-是")
    private Integer needApproval;

    @Schema(description = "活动介绍")
    private String activityDescp;

    @Schema(description = "附件")
    private String attachmentUrl;

    @Schema(description = "申请日期")
    private LocalDateTime applicationDate;

    @Schema(description = "审核日期")
    private LocalDateTime reviewDate;

    @Schema(description = "审核人")
    private String reviewAt;

    @Schema(description = "审核状态:0-待审核;1-审核通过;2-审核不通过")
    private Integer status;

    @Schema(description = "删除标志:0-删除;1-有效")
    private Integer delFlag;

    @Schema(description = "创建人")
    private String createAt;

    @Schema(description = "更新人")
    private String updateAt;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "数据权限部门id")
    private String deptId;

    @Schema(description = "活动状态:1-未开始;2-报名中;3-进行中;4-已结束")
    @TableField(exist = false)
    private Integer progressStatus;

    private ClubMemberActivity clubMemberActivity;

    public Integer getProgressStatus() {
        if (this.registrationTime.isAfter(LocalDateTime.now()) || (this.registrationDeadline.isBefore(LocalDateTime.now()) && this.startTime.isAfter(LocalDateTime.now()))) {
            progressStatus = 1;
        } else if (this.registrationTime.isBefore(LocalDateTime.now()) && this.registrationDeadline.isAfter(LocalDateTime.now())) {
            progressStatus = 2;
        } else if (this.startTime.isBefore(LocalDateTime.now()) && this.endTime.isAfter(LocalDateTime.now())) {
            progressStatus = 3;
        } else if (this.endTime.isBefore(LocalDateTime.now())) {
            progressStatus = 4;
        }
        return progressStatus;
    }
}