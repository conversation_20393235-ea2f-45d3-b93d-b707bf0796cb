package com.ows.ufa.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum QuestionnaireStatus implements IEnum<Integer> {
    //状态:0-未发布;1-收集中;2-已结束
    UNPUBLISHED(0, "未发布"),
    COLLECTING(1, "收集中"),
    ENDED(2, "已结束");
    QuestionnaireStatus(Integer code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final Integer code;
    @JsonValue
    private final String descp;

    @Override
    public Integer getValue() {
        return this.code;
    }

}
