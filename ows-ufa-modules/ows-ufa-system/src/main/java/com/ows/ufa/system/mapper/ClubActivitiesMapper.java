package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.ClubActivities;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.request.ClubActivitiesRequest;
import com.ows.ufa.system.vo.ClubActivitiesVO;
import com.ows.ufa.system.vo.count.ClubActivityCountVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-22
 */
@Mapper
public interface ClubActivitiesMapper extends BaseMapper<ClubActivities> {

    List<ClubActivitiesVO> queryClubActivitiess(@Param("req") ClubActivitiesRequest request);

    ClubActivityCountVO countClubActivity(@Param("ancestors") List<String> ancestors);

    ClubActivitiesVO findClubActivities(@Param("id") Long id);
}