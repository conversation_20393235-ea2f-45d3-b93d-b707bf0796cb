package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.AbilityLabel;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.request.AbilityLabelRequest;
import com.ows.ufa.system.vo.AbilityLabelVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 能力标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-26
 */
@Mapper
public interface AbilityLabelMapper extends BaseMapper<AbilityLabel> {

    List<AbilityLabelVO> queryAbilityLabels(@Param("req") AbilityLabelRequest request);
}