package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@Schema(description ="VO")
public class BaseSchoolDeptForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "院系基础表-主键(UUID)")
    private String id;

    @Schema(description = "院系编号")
    @NotNull(message = "院系编号不能为空")
    private String deptCode;

    @Schema(description = "院系名称")
    @NotNull(message = "院系名称不能为空")
    private String deptName;

    @Schema(description = "院系主任ID")
    @NotNull(message = "院系主任ID不能为空")
    private String deptHeadId;

    @Schema(description = "院系排序")
    @NotNull(message = "院系排序不能为空")
    private Integer deptSort;

    @Schema(description = "院系说明")
    @NotNull(message = "院系说明不能为空")
    private String deptDesc;

    @Schema(description = "课程费用参考(单位元)")
    @NotNull(message = "课程费用参考(单位元)不能为空")
    private Integer courseFees;

    @Schema(description = "所属学校")
    @NotNull(message = "所属学校不能为空")
    private String schoolId;

    @Schema(description = "创建人")
    @NotNull(message = "创建人不能为空")
    private String createBy;

    @Schema(description = "创建时间")
    @NotNull(message = "创建时间不能为空")
    private LocalDateTime createTime;

    @Schema(description = "修改人")
    @NotNull(message = "修改人不能为空")
    private String updateBy;

    @Schema(description = "修改时间")
    @NotNull(message = "修改时间不能为空")
    private LocalDateTime updateTime;

    @Schema(description = "状态[1正常]")
    @NotNull(message = "状态[1正常]不能为空")
    private String status;

    @Schema(description = "校区ID")
    @NotNull(message = "校区ID不能为空")
    private String campusId;

    @Schema(description = "是否启用[1是0否]")
    @NotNull(message = "是否启用[1是0否]不能为空")
    private String isEnable;

}