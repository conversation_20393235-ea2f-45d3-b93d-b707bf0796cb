package com.ows.ufa.system.mapper;

import com.ows.ufa.system.entity.Questionnaire;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.vo.QuestionnaireVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 问卷调查信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@Mapper
public interface QuestionnaireMapper extends BaseMapper<Questionnaire> {

    List<QuestionnaireVO> queryQuestionnaires(@Param("req") QuestionnaireRequest request);

    List<QuestionnaireVO> querySubmitQuestionnaires(@Param("req") QuestionnaireRequest request);

    List<QuestionnaireVO> countSubmitQuestionnaires(@Param("req") QuestionnaireRequest request);
}