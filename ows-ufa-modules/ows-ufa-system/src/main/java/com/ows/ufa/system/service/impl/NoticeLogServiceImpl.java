package com.ows.ufa.system.service.impl;

import com.ows.ufa.system.entity.NoticeLog;
import com.ows.ufa.system.vo.NoticeLogVO;
import com.ows.ufa.system.request.NoticeLogRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.mapper.NoticeLogMapper;
import com.ows.ufa.system.service.NoticeLogService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Service;
import lombok.RequiredArgsConstructor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * <p>
 * 通知书推送信息日志记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-18
 */
@Service
@RequiredArgsConstructor
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true)
public class NoticeLogServiceImpl extends ServiceImpl<NoticeLogMapper, NoticeLog> implements NoticeLogService {

    @Override
    public List<NoticeLog> queryNoticeLogs(NoticeLogRequest request) {
        NoticeLog entity = (NoticeLog) DataTransfer.transfer(request, NoticeLog.class);
        LambdaQueryWrapper<NoticeLog> queryWrapper = new LambdaQueryWrapper<>(entity);
        return this.list(queryWrapper);
    }

    @Override
    public NoticeLogVO findNoticeLog(Long id) {
        NoticeLog entity = this.getById(id);
        NoticeLogVO vo = (NoticeLogVO) DataTransfer.transfer(entity, NoticeLogVO.class);
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveNoticeLog(NoticeLogVO vo) {
        NoticeLog entity = (NoticeLog) DataTransfer.transfer(vo, NoticeLog.class);
        this.save(entity);
        return entity.getLogId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNoticeLog(NoticeLogVO vo) {
        NoticeLog entity = (NoticeLog) DataTransfer.transfer(vo, NoticeLog.class);
        return this.saveOrUpdate(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeNoticeLog(Long id) {
        return this.removeById(id);
    }
}