package com.ows.ufa.system.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 问卷调查信息填报表
 *
 * <AUTHOR>
 * @since 2025-02-07
 */
@Data
@Schema(description ="问卷调查信息填报统计")
public class QuestionnaireOptionsVO implements Serializable {
    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "题目名称")
    private String name;
    @Schema(description = "题目类型")
    private String type;
    @Schema(description = "选项统计")
    List<QuestionnaireSubmitOptions> options;
}