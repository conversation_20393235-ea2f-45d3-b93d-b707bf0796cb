package com.ows.ufa.system.form;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;

import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="VO")
public class ClubInfoForm implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "社团照片URL或文件路径")
    @NotNull(message = "社团照片URL或文件路径不能为空")
    private String clubPhoto;

    @Schema(description = "社团名称")
    @NotNull(message = "社团名称不能为空")
    private String clubName;

    @Schema(description = "社团简介")
    @NotNull(message = "社团简介不能为空")
    private String clubDescp;

    @Schema(description = "负责人姓名")
    @NotNull(message = "负责人姓名不能为空")
    private String leader;

    @Schema(description = "联系方式")
    @NotNull(message = "联系方式不能为空")
    private String phoneNumber;

    @Schema(description = "是否需要考试:0-否;1-是")
    @NotNull(message = "是否需要考试:0-否;1-是不能为空")
    private Integer isExamRequired;

    @Schema(description = "招募条件")
    @NotNull(message = "招募条件不能为空")
    private String recruitmentConditions;

    @Schema(description = "状态:0-待审核;1-审核通过;2-审核不通过")
    @NotNull(message = "状态:0-待审核;1-审核通过;2-审核不通过不能为空")
    private Integer status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "招募开始时间")
    private LocalDateTime recruitmentStartDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "招募结束时间")
    private LocalDateTime recruitmentEndDate;
}