package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.utils.poi.ExcelUtil;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.common.core.web.page.TableDataInfo;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.ClubInfoService;
import com.ows.ufa.system.form.ClubInfoForm;
import com.ows.ufa.system.request.ClubInfoRequest;
import com.ows.ufa.system.util.DataTransfer;
import com.ows.ufa.system.vo.ClubInfoVO;
import com.ows.ufa.system.vo.excel.ClubInfoExcelVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "clubInfo")
@Tag(name = "clubInfo", description = "社团信息接口")
public class ClubInfoController extends BaseController {

    private final ClubInfoService ClubInfoServiceImpl;

    @GetMapping("countClub")
    @Operation(summary = "社团统计")
    public AjaxResult countClub() {
        return success(ClubInfoServiceImpl.countClub());
    }

    @GetMapping("queryAll")
    @Operation(summary = "查询发布招募的社团")
    public AjaxResult queryAll() {
        return success(ClubInfoServiceImpl.queryAll());
    }

    @GetMapping("queryReviewAll")
    @Operation(summary = "查询审核通过的社团")
    public AjaxResult queryReviewAll() {
        return success(ClubInfoServiceImpl.queryReviewAll());
    }

    @GetMapping("list")
    @Operation(summary = "分页查询")
    public AjaxResult listClubInfoByPage(ClubInfoRequest request) {
        startPage();
        return success(getDataTable(ClubInfoServiceImpl.queryClubInfos(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "查询详情")
    public AjaxResult findClubInfo(@PathVariable Long id, @RequestParam(value = "type") Integer type) {
        return success(ClubInfoServiceImpl.findClubInfo(id,type));
    }


    @PostMapping
    @Operation(summary = "新增数据")
    public AjaxResult saveClubInfo(@RequestBody ClubInfoForm form) {
        return success(ClubInfoServiceImpl.saveClubInfo(form));
    }

    @PostMapping("update")
    @Operation(summary = "修改数据")
    public AjaxResult updateClubInfo(@RequestBody ClubInfoForm form) {
        return success(ClubInfoServiceImpl.updateClubInfo(form));
    }

    @PostMapping("delete")
    @Operation(summary = "删除数据")
    public AjaxResult removeClubInfo(@RequestBody IdRequest id) {
        return success(ClubInfoServiceImpl.removeClubInfo(id));
    }

    @PostMapping("recruitment")
    @Operation(summary = "发起招募")
    public AjaxResult recruitment(@RequestBody ClubInfoForm form) {
        return success(ClubInfoServiceImpl.recruitment(form));
    }

    @PostMapping("/export")
    @Operation(summary = "社团信息导出")
    public void export(HttpServletResponse response,@RequestBody ClubInfoRequest request) {
        List<ClubInfoVO> list = ClubInfoServiceImpl.queryClubInfos(request);
        List<ClubInfoExcelVO> excelVOList = DataTransfer.transferList(list, ClubInfoExcelVO.class);
        ExcelUtil<ClubInfoExcelVO> util = new ExcelUtil<>(ClubInfoExcelVO.class);
        util.exportExcel(response, excelVOList, "社团信息数据");
    }
}
