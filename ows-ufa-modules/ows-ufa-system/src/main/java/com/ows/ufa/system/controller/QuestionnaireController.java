package com.ows.ufa.system.controller;

import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.web.controller.BaseController;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.entity.Questionnaire;
import com.ows.ufa.system.enums.QuestionnaireStatus;
import com.ows.ufa.system.request.IdRequest;
import com.ows.ufa.system.service.QuestionnaireService;
import com.ows.ufa.system.form.QuestionnaireForm;
import com.ows.ufa.system.request.QuestionnaireRequest;
import com.ows.ufa.system.util.QRCodeGenerator;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 问卷调查信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-06
 */
@RestController
@RequiredArgsConstructor
@RequestMapping(value = "questionnaire")
@Tag(name = "questionnaire", description = "问卷调查信息表接口")
@Slf4j
public class QuestionnaireController extends BaseController {

    private final QuestionnaireService QuestionnaireServiceImpl;

    @Value("${ufa.url}")
    private String url;

    @GetMapping("listCount")
    @Operation(summary = "列表统计")
    public AjaxResult listCount() {
        return success(QuestionnaireServiceImpl.listCount());
    }

    @GetMapping("list")
    @Operation(summary = "问卷调查信息分页查询")
    public AjaxResult listQuestionnaireByPage(QuestionnaireRequest request) {
        startPage();
        return success(getDataTable(QuestionnaireServiceImpl.queryQuestionnaires(request)));
    }

    @GetMapping("{id}")
    @Operation(summary = "问卷调查信息表查询详情")
    public AjaxResult findQuestionnaire(@PathVariable Long id) {
        return success(QuestionnaireServiceImpl.findQuestionnaire(id));
    }

    @PostMapping
    @Operation(summary = "问卷调查信息表新增数据")
    public AjaxResult saveQuestionnaire(@RequestBody QuestionnaireForm form) {
        return success(QuestionnaireServiceImpl.saveQuestionnaire(form));
    }

    @PostMapping("update")
    @Operation(summary = "问卷调查信息表修改数据")
    public AjaxResult updateQuestionnaire(@RequestBody QuestionnaireForm form) {
        return success(QuestionnaireServiceImpl.updateQuestionnaire(form));
    }

    @PostMapping("delete")
    @Operation(summary = "问卷调查信息表删除数据")
    public AjaxResult removeQuestionnaire(@RequestBody IdRequest id) {
        return success(QuestionnaireServiceImpl.removeQuestionnaire(id.getId()));
    }

    @PostMapping("releaseAll")
    @Operation(summary = "问卷调查信息发布")
    public AjaxResult releaseAllQuestionnaire(@RequestBody QuestionnaireForm form) {
        return success(QuestionnaireServiceImpl.releaseAllQuestionnaire(form));
    }

    @PostMapping("release")
    @Operation(summary = "问卷调查信息发布")
    public AjaxResult releaseQuestionnaire(@RequestBody QuestionnaireForm form) {
        return success(QuestionnaireServiceImpl.releaseQuestionnaire(form));
    }

    @PostMapping("stop")
    @Operation(summary = "问卷调查信息停止收集")
    public AjaxResult stopQuestionnaire(@RequestBody IdRequest id) {
        return success(QuestionnaireServiceImpl.stopQuestionnaire(id.getId()));
    }

    @PostMapping("createCodeUrl")
    @Operation(summary = "分享生成二维码")
    public AjaxResult createSingleCodeUrl(@RequestBody IdRequest id, HttpServletResponse response) {
        Questionnaire questionnaire = QuestionnaireServiceImpl.getById(id.getId());
        if (questionnaire == null) {
            throw new ServiceException("该问卷调查信息不存在");
        }
        if (questionnaire.getStatus() == QuestionnaireStatus.ENDED.getCode()) {
            throw new ServiceException("该问卷调查信息已结束，不允许分享");
        }
        /*if (questionnaire.getFillPermission() != 0) {
            throw new ServiceException("问卷填写权限为指定范围，不允许分享");
        }*/
        try {
            //https://cqlncs.12399.gov.cn:8093/#/questionnaire/touristFill?id=45
            String text = url+"#/questionnaire/touristFill?id=" + questionnaire.getId();
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            boolean flag = QRCodeGenerator.generateQRCodeImage(baos, text);
            if (flag) {
//                response.setHeader("Content-Disposition", "attachment; filename=\"" + questionnaire.getTitle() + ".png\"");
//                response.setContentType("image/png");
//                OutputStream out = response.getOutputStream();
//                baos.writeTo(out);
//                out.flush();
                String base64Image = Base64.getEncoder().encodeToString(baos.toByteArray());
                Map<String,String> map=new HashMap<>();
                map.put("img",base64Image);
                map.put("link",url+"#/questionnaire/touristFill?id=" + questionnaire.getId());
                return success(map);
            } else {
                // 如果生成二维码失败，可以记录错误日志或者返回错误信息给客户端
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                log.error("生成二维码失败");
            }
        } catch (Exception e) {
            log.error("生成二维码失败", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
        return error("生成二维码失败");
    }
}
