package com.ows.ufa.system.aspect;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface MyDataScope {
    /**
     * 表别名设置
     */
    String alias() default "";

    /**
     * 数据权限表字段名
     */
    String dataId() default "";
}