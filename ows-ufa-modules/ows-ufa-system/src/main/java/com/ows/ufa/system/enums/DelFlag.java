package com.ows.ufa.system.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum DelFlag implements IEnum<Integer> {
    DELETE(0, "删除"),
    VALID(1, "有效");
    DelFlag(Integer code, String descp) {
        this.code = code;
        this.descp = descp;
    }

    @EnumValue
    private final Integer code;
    @JsonValue
    private final String descp;

    @Override
    public Integer getValue() {
        return this.code;
    }

}
