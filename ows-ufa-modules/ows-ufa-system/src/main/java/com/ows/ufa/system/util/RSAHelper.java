package com.ows.ufa.system.util;

import com.alibaba.fastjson.JSONArray;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.system.entity.AdmissionNotice;
import org.bouncycastle.util.encoders.Base64;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RSAHelper {
    public static void main(String[] args) throws Exception {
        String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGQoBqSrFZM+5CAFmI08KNA2yjIg2Oa/tp4wSSRqlRpNWl3UfVEYZvhe8iAEedobOGyXqZ9KjZxnJgLxyX/wx7wQlVFhsFA2d8Um07KyZmCgpm+BagSKXOC3KWMqZBmwQM/kKAODkMxBsBUBLj0nKsraU1zbecXoR+tk4bbqbOpwIDAQAB";
        List<AdmissionNotice> list = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            AdmissionNotice admissionNotice = new AdmissionNotice();
            admissionNotice.setUserId("20240000");
            admissionNotice.setUserName("测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测");
            admissionNotice.setSchoolAddr("测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测");
            admissionNotice.setCourseName("测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测测试测试测试测试测试测试测试测试测试测试测试测试测试测");
            list.add(admissionNotice);
        }
        String encryptDemo = encrypt(JSONArray.toJSONString(list), publicKey);
        System.out.println("加密后的数据：" + encryptDemo);

        String privateKeyStr = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAIZCgGpKsVkz7kIAWYjTwo0DbKMiDY5r+2njBJJGqVGk1aXdR9URhm+F7yIAR52hs4bJepn0qNnGcmAvHJf/DHvBCVUWGwUDZ3xSbTsrJmYKCmb4FqBIpc4LcpYypkGbBAz+QoA4OQzEGwFQEuPScqytpTXNt5xehH62Thtups6nAgMBAAECgYAAwMV6P1uP9RvaCNxNzg6Jmn9b132pX61vFSyhj+fXVcImDvWG1/VGKzMlp433IMtY/Ad8q1S9HCr2XY9QuSTR7Tny2zCGFKi4KjFMn/zY+WkgHpY76WB72Vlep4bb6C+SldcQutp/DlelEQaU4bgW6WdWCnq4FgQCO96yu0WhWQJBALaZKhSoFjjnHK0ex7VIgBNHmxNwc0cDFJh1/Oj9kmxS+ytt8KsZ2n9why18yFzvP6QFfo4peIgzyiR2gmBP5z8CQQC8OundxcAB1QCW6IpOmwUuipIy2YgGtdisCgNA5KbBcaEldwWJnw24rt1tOEUTROZ3lknzO2L2hU+FkyddHeaZAkEAqfld0Ki/Nuz8JrM2WCxK/Kd1Vd0hkgLuExYR/Zi9ypRcNos6syt315WaX99+a/RSG475xcAY8UC0dMjUiG8Y5QJADCVtE+9/zV52VEabLilYs0Ej3/4jhfNUEP3wac6XaCIx+N3uUIwf8YvvI+4enviyOoNLMFPjte6yS/dL8flM+QI/PoKkCEpNp/CqOu8jqPFvKgoV9f/6fsA5GPnexIhkPsYU7SRf79V4e+9ID22KdPaHXtmDiHdUWIaeiVljcf7a";
        String decrypt = decrypt(encryptDemo, privateKeyStr);
        System.out.println("解密后的数据：" + decrypt);
    }

    public static String encrypt(String json, String publicKeyStr) {
        try {
            byte[] publicKeyBytes = org.bouncycastle.util.encoders.Base64.decode(publicKeyStr);
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey restoredPublicKey = keyFactory.generatePublic(x509KeySpec);
            // 2. 加密
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding"); // 指定填充模式
            cipher.init(Cipher.ENCRYPT_MODE, restoredPublicKey);

            byte[] jsonBytes = json.getBytes("UTF-8");
            int maxEncryptLen = 117;// 根据RSA加密块大小和填充方式确定，1024位密钥下
            int inputLen = jsonBytes.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段加密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > maxEncryptLen) {
                    cache = cipher.doFinal(jsonBytes, offSet, maxEncryptLen);
                } else {
                    cache = cipher.doFinal(jsonBytes, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * maxEncryptLen;
            }
            byte[] encryptedData = out.toByteArray();
            out.close();

            return bytesToHex(encryptedData);
        } catch (Exception e) {
            throw new ServiceException("加密失败" + e.getMessage());
        }
        // 从字符串形式恢复公钥
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    public static String decrypt(String json, String privateKeyStr) {
        // 解码私钥
        try {
            byte[] privateKeyBytes = Base64.decode(privateKeyStr);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey restoredPrivateKey = keyFactory.generatePrivate(pkcs8KeySpec);
            // 初始化Cipher对象进行解密
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding"); // 指定填充模式
            cipher.init(Cipher.DECRYPT_MODE, restoredPrivateKey);

            // 将十六进制字符串转换为字节数组
            byte[] encryptedData = hexStringToByteArray(json);
            int maxDecryptLen = 128; // 根据RSA密钥长度和填充方式确定，1024位密钥下
            int inputLen = encryptedData.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段解密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > maxDecryptLen) {
                    cache = cipher.doFinal(encryptedData, offSet, maxDecryptLen);
                } else {
                    cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * maxDecryptLen;
            }
            byte[] decryptedData = out.toByteArray();
            out.close();

            return new String(decryptedData, "UTF-8");
        } catch (Exception e) {
            throw new ServiceException("加密失败" + e.getMessage());
        }
    }
}
