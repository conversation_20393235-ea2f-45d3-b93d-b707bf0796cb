package com.ows.ufa.system.request;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-08
 */
@Data
@Schema(description ="Request")
public class BaseTeacherRequest implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "学校名称")
    private String schoolName;

    @Schema(description = "学校ID")
    private String schoolId;

    @Schema(description = "所属院系")
    private String deptName;

    @Schema(description = "家庭住址")
    private String tchAddr;
}