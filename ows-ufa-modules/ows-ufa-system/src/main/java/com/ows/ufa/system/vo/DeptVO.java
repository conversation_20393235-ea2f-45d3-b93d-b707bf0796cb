package com.ows.ufa.system.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;

/**
 * 部门表
 *
 * <AUTHOR>
 * @since 2025-01-09
 */
@Data
@Schema(description ="部门表VO")
public class DeptVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "父部门ID")
    private String parentId;

    @Schema(description = "祖级列表")
    private String ancestors;

    @Schema(description = "部门名称")
    private String deptName;

    @Schema(description = "显示顺序")
    private Integer orderNum;

    @Schema(description = "负责人")
    private String leader;

    @Schema(description = "联系电话")
    private String phone;

    @Schema(description = "创建者")
    private String createBy;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新者")
    private String updateBy;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "部门状态[1正常]")
    private String status;

    @Schema(description = "机构地址")
    private String deptAddress;

    @Schema(description = "机构传真")
    private String deptFax;

    @Schema(description = "机构邮编")
    private String deptPostcode;

    @Schema(description = "机构邮箱")
    private String deptEmail;

    @Schema(description = "是否学校[1学校2其他机构3行政区划]")
    private String isSchool;

    @Schema(description = "id字段唯一键")
    private String deptId;

}