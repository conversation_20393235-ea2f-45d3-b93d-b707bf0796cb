package com.ows.ufa.system.vo.count;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 
 *
 * <AUTHOR>
 * @since 2025-01-21
 */
@Data
@Schema(description ="共享师资库统计VO")
public class BaseTeacherCountVO implements Serializable {

    private static final long serialVersionUID=1L;
    @Schema(description = "学校数量")
    private Long schoolCount;

    @Schema(description = "教学类型")
    private Long deptCount;

    @Schema(description = "共享师资总数")
    private Long teacherCount;

    @Schema(description = "性别比例:男")
    private Integer maleRatio;

    @Schema(description = "性别比例:女")
    private Integer femaleRatio;

    @Schema(description = "学历比例")
    List<BaseTeacherEduCountVO> eduCounts;

}