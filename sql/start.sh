#!/bin/bash

# 定义变量
APP_NAME="eps-ins"
JAR_FILE="/data/eps-ins/eps-ins-0.0.1-SNAPSHOT.jar"
LOG_FILE="/data/eps-ins/app.log"
PID_FILE="/data/eps-ins/eps-ins.pid"
JAVA_OPTS="-Xms512m -Xmx1024m"
PORT="--server.port=7006"
MAX_WAIT=30  # 等待停止的最大秒数

# 检查JAR文件是否存在
if [ ! -f "$JAR_FILE" ]; then
    echo "[错误] JAR文件不存在: $JAR_FILE"
    exit 1
fi

# 停止当前运行的进程
if [ -f "$PID_FILE" ]; then
    OLD_PID=$(cat "$PID_FILE")
    if ps -p $OLD_PID > /dev/null; then
        echo "正在停止运行中的 $APP_NAME (PID: $OLD_PID)..."
        kill $OLD_PID

        # 等待进程退出
        COUNT=0
        while [ $COUNT -lt $MAX_WAIT ] && ps -p $OLD_PID > /dev/null; do
            sleep 1
            ((COUNT++))
            echo "等待进程停止... ($COUNT/$MAX_WAIT)"
        done

        if ps -p $OLD_PID > /dev/null; then
            echo "[警告] 强制终止进程 (PID: $OLD_PID)"
            kill -9 $OLD_PID
        fi
    fi
    rm -f "$PID_FILE"
fi

# 启动新实例
echo "正在启动 $APP_NAME..."
nohup java $JAVA_OPTS -jar "$JAR_FILE" $PORT >> "$LOG_FILE" 2>&1 &
NEW_PID=$!
echo $NEW_PID > "$PID_FILE"

# 验证启动
sleep 3
if ps -p $NEW_PID > /dev/null; then
    echo "$APP_NAME 重启成功! (PID: $NEW_PID, 日志: $LOG_FILE)"
else
    echo "[错误] 启动失败，请检查日志: $LOG_FILE"
    exit 1
fi