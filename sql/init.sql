CREATE TABLE sys_config (
    config_id integer NOT NULL,
    config_name character varying(100) DEFAULT ''::character varying,
    config_key character varying(100) DEFAULT ''::character varying,
    config_value character varying(500) DEFAULT ''::character varying,
    config_type character(1) DEFAULT 'N'::bpchar,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(500)
);
COMMENT ON TABLE sys_config IS '参数配置表';
COMMENT ON COLUMN sys_config.config_id IS '参数主键';
COMMENT ON COLUMN sys_config.config_name IS '参数名称';
CREATE SEQUENCE sys_config_config_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_config_config_id_seq OWNED BY sys_config.config_id;
CREATE TABLE sys_dept (
    dept_id bigint NOT NULL,
    parent_id bigint DEFAULT 0,
    ancestors character varying(50) DEFAULT ''::character varying,
    dept_name character varying(30) DEFAULT ''::character varying,
    order_num integer DEFAULT 0,
    leader character varying(20) DEFAULT NULL::character varying,
    phone character varying(11) DEFAULT NULL::character varying,
    email character varying(50) DEFAULT NULL::character varying,
    status character(1) DEFAULT '0'::bpchar,
    del_flag character(1) DEFAULT '0'::bpchar,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone
);
COMMENT ON TABLE sys_dept IS '部门表';
COMMENT ON COLUMN sys_dept.dept_id IS '部门id';
COMMENT ON COLUMN sys_dept.parent_id IS '父部门id';
COMMENT ON COLUMN sys_dept.ancestors IS '祖级列表';
COMMENT ON COLUMN sys_dept.dept_name IS '部门名称';
COMMENT ON COLUMN sys_dept.order_num IS '显示顺序';
COMMENT ON COLUMN sys_dept.leader IS '负责人';
COMMENT ON COLUMN sys_dept.phone IS '联系电话';
COMMENT ON COLUMN sys_dept.email IS '邮箱';
COMMENT ON COLUMN sys_dept.status IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN sys_dept.del_flag IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN sys_dept.create_by IS '创建者';
COMMENT ON COLUMN sys_dept.create_time IS '创建时间';
COMMENT ON COLUMN sys_dept.update_by IS '更新者';
COMMENT ON COLUMN sys_dept.update_time IS '更新时间';
CREATE SEQUENCE sys_dept_dept_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_dept_dept_id_seq OWNED BY sys_dept.dept_id;
CREATE TABLE sys_dict_data (
    dict_code bigint NOT NULL,
    dict_sort integer DEFAULT 0,
    dict_label character varying(100) DEFAULT ''::character varying,
    dict_value character varying(100) DEFAULT ''::character varying,
    dict_type character varying(100) DEFAULT ''::character varying,
    css_class character varying(100),
    list_class character varying(100),
    is_default character(1) DEFAULT 'N'::bpchar,
    status character(1) DEFAULT '0'::bpchar,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(500)
);
COMMENT ON TABLE sys_dict_data IS '字典数据表';
COMMENT ON COLUMN sys_dict_data.dict_code IS '字典编码';
COMMENT ON COLUMN sys_dict_data.dict_sort IS '字典排序';
CREATE SEQUENCE sys_dict_data_dict_code_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_dict_data_dict_code_seq OWNED BY sys_dict_data.dict_code;
CREATE TABLE sys_dict_type (
    dict_id bigint NOT NULL,
    dict_name character varying(100) DEFAULT ''::character varying,
    dict_type character varying(100) DEFAULT ''::character varying,
    status character(1) DEFAULT '0'::bpchar,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(500)
);
COMMENT ON TABLE sys_dict_type IS '字典类型表';
COMMENT ON COLUMN sys_dict_type.dict_id IS '字典主键';
COMMENT ON COLUMN sys_dict_type.dict_name IS '字典名称';
CREATE SEQUENCE sys_dict_type_dict_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_dict_type_dict_id_seq OWNED BY sys_dict_type.dict_id;
CREATE TABLE sys_logininfor (
    info_id bigint NOT NULL,
    user_name character varying(50) DEFAULT ''::character varying,
    ipaddr character varying(128) DEFAULT ''::character varying,
    login_location character varying(255) DEFAULT ''::character varying,
    browser character varying(50) DEFAULT ''::character varying,
    os character varying(50) DEFAULT ''::character varying,
    status character(1) DEFAULT '0'::bpchar,
    msg character varying(255) DEFAULT ''::character varying,
    login_time timestamp without time zone
);
COMMENT ON TABLE sys_logininfor IS '系统访问记录';
COMMENT ON COLUMN sys_logininfor.info_id IS '访问ID';
COMMENT ON COLUMN sys_logininfor.user_name IS '用户账号';
CREATE SEQUENCE sys_logininfor_info_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_logininfor_info_id_seq OWNED BY sys_logininfor.info_id;
CREATE TABLE sys_menu (
    menu_id bigint NOT NULL,
    menu_name character varying(50) NOT NULL,
    parent_id bigint DEFAULT 0,
    order_num integer DEFAULT 0,
    path character varying(200) DEFAULT ''::character varying,
    component character varying(255) DEFAULT NULL::character varying,
    query character varying(255) DEFAULT NULL::character varying,
    is_frame character(1) DEFAULT '1'::bpchar,
    is_cache character(1) DEFAULT '0'::bpchar,
    menu_type character(1) DEFAULT ''::bpchar,
    visible character(1) DEFAULT '0'::bpchar,
    status character(1) DEFAULT '0'::bpchar,
    perms character varying(100) DEFAULT NULL::character varying,
    icon character varying(100) DEFAULT '#'::character varying,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(500) DEFAULT ''::character varying
);
COMMENT ON TABLE sys_menu IS '菜单权限表';
COMMENT ON COLUMN sys_menu.menu_id IS '菜单ID';
COMMENT ON COLUMN sys_menu.menu_name IS '菜单名称';
COMMENT ON COLUMN sys_menu.parent_id IS '父菜单ID';
COMMENT ON COLUMN sys_menu.order_num IS '显示顺序';
COMMENT ON COLUMN sys_menu.path IS '路由地址';
COMMENT ON COLUMN sys_menu.component IS '组件路径';
COMMENT ON COLUMN sys_menu.query IS '路由参数';
COMMENT ON COLUMN sys_menu.is_frame IS '是否为外链（0是 1否）';
COMMENT ON COLUMN sys_menu.is_cache IS '是否缓存（0缓存 1不缓存）';
COMMENT ON COLUMN sys_menu.menu_type IS '菜单类型（M目录 C菜单 F按钮）';
COMMENT ON COLUMN sys_menu.visible IS '菜单状态（0显示 1隐藏）';
COMMENT ON COLUMN sys_menu.status IS '菜单状态（0正常 1停用）';
COMMENT ON COLUMN sys_menu.perms IS '权限标识';
COMMENT ON COLUMN sys_menu.icon IS '菜单图标';
COMMENT ON COLUMN sys_menu.create_by IS '创建者';
COMMENT ON COLUMN sys_menu.create_time IS '创建时间';
COMMENT ON COLUMN sys_menu.update_by IS '更新者';
COMMENT ON COLUMN sys_menu.update_time IS '更新时间';
COMMENT ON COLUMN sys_menu.remark IS '备注';
CREATE SEQUENCE sys_menu_menu_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_menu_menu_id_seq OWNED BY sys_menu.menu_id;
CREATE TABLE sys_notice (
    notice_id integer NOT NULL,
    notice_title character varying(50) NOT NULL,
    notice_type character(1) NOT NULL,
    notice_content text,
    status character(1) DEFAULT '0'::bpchar,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(255) DEFAULT NULL::character varying
);
COMMENT ON TABLE sys_notice IS '通知公告表';
COMMENT ON COLUMN sys_notice.notice_id IS '公告ID';
COMMENT ON COLUMN sys_notice.notice_title IS '公告标题';
CREATE SEQUENCE sys_notice_notice_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_notice_notice_id_seq OWNED BY sys_notice.notice_id;
CREATE TABLE sys_oper_log (
    oper_id bigint NOT NULL,
    title character varying(50) DEFAULT ''::character varying,
    business_type integer DEFAULT 0,
    method character varying(200) DEFAULT ''::character varying,
    request_method character varying(10) DEFAULT ''::character varying,
    operator_type integer DEFAULT 0,
    oper_name character varying(50) DEFAULT ''::character varying,
    dept_name character varying(50) DEFAULT ''::character varying,
    oper_url character varying(255) DEFAULT ''::character varying,
    oper_ip character varying(128) DEFAULT ''::character varying,
    oper_location character varying(255) DEFAULT ''::character varying,
    oper_param character varying(2000) DEFAULT ''::character varying,
    json_result character varying(2000) DEFAULT ''::character varying,
    status integer DEFAULT 0,
    error_msg character varying(2000) DEFAULT ''::character varying,
    oper_time timestamp without time zone,
    cost_time bigint DEFAULT 0
);
COMMENT ON TABLE sys_oper_log IS '操作日志记录';
COMMENT ON COLUMN sys_oper_log.oper_id IS '日志主键';
COMMENT ON COLUMN sys_oper_log.title IS '模块标题';
CREATE SEQUENCE sys_oper_log_oper_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_oper_log_oper_id_seq OWNED BY sys_oper_log.oper_id;
CREATE TABLE sys_post (
    post_id bigint NOT NULL,
    post_code character varying(64) NOT NULL,
    post_name character varying(50) NOT NULL,
    post_sort integer NOT NULL,
    status character(1) NOT NULL,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(500) DEFAULT NULL::character varying
);
COMMENT ON TABLE sys_post IS '岗位信息表';
COMMENT ON COLUMN sys_post.post_id IS '岗位ID';
COMMENT ON COLUMN sys_post.post_code IS '岗位编码';
COMMENT ON COLUMN sys_post.post_name IS '岗位名称';
COMMENT ON COLUMN sys_post.post_sort IS '显示顺序';
COMMENT ON COLUMN sys_post.status IS '状态（0正常 1停用）';
COMMENT ON COLUMN sys_post.create_by IS '创建者';
COMMENT ON COLUMN sys_post.create_time IS '创建时间';
COMMENT ON COLUMN sys_post.update_by IS '更新者';
COMMENT ON COLUMN sys_post.update_time IS '更新时间';
COMMENT ON COLUMN sys_post.remark IS '备注';
CREATE SEQUENCE sys_post_post_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_post_post_id_seq OWNED BY sys_post.post_id;
CREATE TABLE sys_role (
    role_id bigint NOT NULL,
    role_name character varying(30) NOT NULL,
    role_key character varying(100) NOT NULL,
    role_sort integer NOT NULL,
    data_scope character(1) DEFAULT '1'::bpchar,
    menu_check_strictly character(1) DEFAULT '1'::bpchar,
    dept_check_strictly character(1) DEFAULT '1'::bpchar,
    status character(1) NOT NULL,
    del_flag character(1) DEFAULT '0'::bpchar,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(500) DEFAULT NULL::character varying
);
COMMENT ON TABLE sys_role IS '角色信息表';
COMMENT ON COLUMN sys_role.role_id IS '角色ID';
COMMENT ON COLUMN sys_role.role_name IS '角色名称';
COMMENT ON COLUMN sys_role.role_key IS '角色权限字符串';
COMMENT ON COLUMN sys_role.role_sort IS '显示顺序';
COMMENT ON COLUMN sys_role.data_scope IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';
COMMENT ON COLUMN sys_role.menu_check_strictly IS '菜单树选择项是否关联显示';
COMMENT ON COLUMN sys_role.dept_check_strictly IS '部门树选择项是否关联显示';
COMMENT ON COLUMN sys_role.status IS '角色状态（0正常 1停用）';
COMMENT ON COLUMN sys_role.del_flag IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN sys_role.create_by IS '创建者';
COMMENT ON COLUMN sys_role.create_time IS '创建时间';
COMMENT ON COLUMN sys_role.update_by IS '更新者';
COMMENT ON COLUMN sys_role.update_time IS '更新时间';
COMMENT ON COLUMN sys_role.remark IS '备注';
CREATE TABLE sys_role_dept (
    role_id bigint NOT NULL,
    dept_id bigint NOT NULL
);
COMMENT ON TABLE sys_role_dept IS '角色和部门关联表';
COMMENT ON COLUMN sys_role_dept.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_dept.dept_id IS '部门ID';
CREATE TABLE sys_role_menu (
    role_id bigint NOT NULL,
    menu_id bigint NOT NULL
);
COMMENT ON TABLE sys_role_menu IS '角色和菜单关联表';
COMMENT ON COLUMN sys_role_menu.role_id IS '角色ID';
COMMENT ON COLUMN sys_role_menu.menu_id IS '菜单ID';
CREATE SEQUENCE sys_role_role_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_role_role_id_seq OWNED BY sys_role.role_id;
CREATE TABLE sys_user (
    user_id bigint NOT NULL,
    dept_id bigint,
    user_name character varying(30) NOT NULL,
    nick_name character varying(30),
    user_type character varying(2) DEFAULT '00'::character varying,
    email character varying(50) DEFAULT ''::character varying,
    phonenumber character varying(11) DEFAULT ''::character varying,
    sex character(1) DEFAULT '0'::bpchar,
    avatar character varying(100) DEFAULT ''::character varying,
    password character varying(100) DEFAULT ''::character varying,
    status character(1) DEFAULT '0'::bpchar,
    del_flag character(1) DEFAULT '0'::bpchar,
    login_ip character varying(128) DEFAULT ''::character varying,
    login_date timestamp without time zone,
    create_by character varying(64) DEFAULT ''::character varying,
    create_time timestamp without time zone,
    update_by character varying(64) DEFAULT ''::character varying,
    update_time timestamp without time zone,
    remark character varying(500) DEFAULT NULL::character varying
);
COMMENT ON TABLE sys_user IS '用户信息表';
COMMENT ON COLUMN sys_user.user_id IS '用户ID';
COMMENT ON COLUMN sys_user.dept_id IS '部门ID';
COMMENT ON COLUMN sys_user.user_name IS '用户账号';
COMMENT ON COLUMN sys_user.nick_name IS '用户昵称';
COMMENT ON COLUMN sys_user.user_type IS '用户类型（00系统用户）';
COMMENT ON COLUMN sys_user.email IS '用户邮箱';
COMMENT ON COLUMN sys_user.phonenumber IS '手机号码';
COMMENT ON COLUMN sys_user.sex IS '用户性别（0男 1女 2未知）';
COMMENT ON COLUMN sys_user.avatar IS '头像地址';
COMMENT ON COLUMN sys_user.password IS '密码';
COMMENT ON COLUMN sys_user.status IS '帐号状态（0正常 1停用）';
COMMENT ON COLUMN sys_user.del_flag IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN sys_user.login_ip IS '最后登录IP';
COMMENT ON COLUMN sys_user.login_date IS '最后登录时间';
COMMENT ON COLUMN sys_user.create_by IS '创建者';
COMMENT ON COLUMN sys_user.create_time IS '创建时间';
COMMENT ON COLUMN sys_user.update_by IS '更新者';
COMMENT ON COLUMN sys_user.update_time IS '更新时间';
COMMENT ON COLUMN sys_user.remark IS '备注';
CREATE TABLE sys_user_post (
    user_id bigint NOT NULL,
    post_id bigint NOT NULL
);
COMMENT ON TABLE sys_user_post IS '用户与岗位关联表';
COMMENT ON COLUMN sys_user_post.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_post.post_id IS '岗位ID';
CREATE TABLE sys_user_role (
    user_id bigint NOT NULL,
    role_id bigint NOT NULL
);
COMMENT ON TABLE sys_user_role IS '用户和角色关联表';
COMMENT ON COLUMN sys_user_role.user_id IS '用户ID';
COMMENT ON COLUMN sys_user_role.role_id IS '角色ID';
CREATE SEQUENCE sys_user_user_id_seq
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;
ALTER SEQUENCE sys_user_user_id_seq OWNED BY sys_user.user_id;

ALTER TABLE ONLY sys_config ALTER COLUMN config_id SET DEFAULT nextval('sys_config_config_id_seq'::regclass);
ALTER TABLE ONLY sys_dept ALTER COLUMN dept_id SET DEFAULT nextval('sys_dept_dept_id_seq'::regclass);
ALTER TABLE ONLY sys_dict_data ALTER COLUMN dict_code SET DEFAULT nextval('sys_dict_data_dict_code_seq'::regclass);
ALTER TABLE ONLY sys_dict_type ALTER COLUMN dict_id SET DEFAULT nextval('sys_dict_type_dict_id_seq'::regclass);
ALTER TABLE ONLY sys_logininfor ALTER COLUMN info_id SET DEFAULT nextval('sys_logininfor_info_id_seq'::regclass);
ALTER TABLE ONLY sys_menu ALTER COLUMN menu_id SET DEFAULT nextval('sys_menu_menu_id_seq'::regclass);
ALTER TABLE ONLY sys_notice ALTER COLUMN notice_id SET DEFAULT nextval('sys_notice_notice_id_seq'::regclass);
ALTER TABLE ONLY sys_oper_log ALTER COLUMN oper_id SET DEFAULT nextval('sys_oper_log_oper_id_seq'::regclass);
ALTER TABLE ONLY sys_post ALTER COLUMN post_id SET DEFAULT nextval('sys_post_post_id_seq'::regclass);
ALTER TABLE ONLY sys_role ALTER COLUMN role_id SET DEFAULT nextval('sys_role_role_id_seq'::regclass);
ALTER TABLE ONLY sys_user ALTER COLUMN user_id SET DEFAULT nextval('sys_user_user_id_seq'::regclass);
INSERT INTO sys_config VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '初始化密码 123456');
INSERT INTO sys_config VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO sys_config VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-11-01 13:25:33.036153', 'admin', '2024-11-11 14:57:04.212666', '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO sys_dept VALUES (100, 0, '0', '渝老爱学', 0, '管理员', '***********', '<EMAIL>', '0', '0', 'admin', '2024-11-01 13:25:32.886543', 'admin', '2024-11-11 14:44:30.137242');
INSERT INTO sys_dept VALUES (101, 100, '0,100', '重庆', 1, '管理员', '***********', '<EMAIL>', '0', '2', 'admin', '2024-11-01 13:25:32.886543', 'admin', '2024-11-11 14:49:50.896515');
INSERT INTO sys_dict_data VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '显示菜单');
INSERT INTO sys_dict_data VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '隐藏菜单');
INSERT INTO sys_dict_data VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '停用状态');
INSERT INTO sys_dict_data VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '正常状态');
INSERT INTO sys_dict_data VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '停用状态');
INSERT INTO sys_dict_data VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '默认分组');
INSERT INTO sys_dict_data VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '系统分组');
INSERT INTO sys_dict_data VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '系统默认是');
INSERT INTO sys_dict_data VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '系统默认否');
INSERT INTO sys_dict_data VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '通知');
INSERT INTO sys_dict_data VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '公告');
INSERT INTO sys_dict_data VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '正常状态');
INSERT INTO sys_dict_data VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '关闭状态');
INSERT INTO sys_dict_data VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '其他操作');
INSERT INTO sys_dict_data VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '新增操作');
INSERT INTO sys_dict_data VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '修改操作');
INSERT INTO sys_dict_data VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '删除操作');
INSERT INTO sys_dict_data VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '授权操作');
INSERT INTO sys_dict_data VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '导出操作');
INSERT INTO sys_dict_data VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '导入操作');
INSERT INTO sys_dict_data VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '强退操作');
INSERT INTO sys_dict_data VALUES (26, 8, '生成', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '生成操作');
INSERT INTO sys_dict_data VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '清空操作');
INSERT INTO sys_dict_data VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '正常状态');
INSERT INTO sys_dict_data VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '停用状态');
INSERT INTO sys_dict_data VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '性别男');
INSERT INTO sys_dict_data VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '性别女');
INSERT INTO sys_dict_data VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '性别未知');
INSERT INTO sys_dict_data VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-11-01 13:25:33.036153', 'admin', '2024-11-02 13:58:31.472214', '正常状态');
INSERT INTO sys_dict_type VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '菜单状态列表');
INSERT INTO sys_dict_type VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '系统开关列表');
INSERT INTO sys_dict_type VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '任务状态列表');
INSERT INTO sys_dict_type VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '任务分组列表');
INSERT INTO sys_dict_type VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '系统是否列表');
INSERT INTO sys_dict_type VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '通知类型列表');
INSERT INTO sys_dict_type VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '通知状态列表');
INSERT INTO sys_dict_type VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '操作类型列表');
INSERT INTO sys_dict_type VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2024-11-01 13:25:33.036153', '', NULL, '登录状态列表');
INSERT INTO sys_dict_type VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2024-11-01 13:25:33.036153', 'admin', '2024-11-02 13:58:14.789967', '用户性别列表');
INSERT INTO sys_menu VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '1', '0', 'M', '0', '0', '', 'system', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '系统管理目录');
INSERT INTO sys_menu VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '1', '0', 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '用户管理菜单');
INSERT INTO sys_menu VALUES (1000, '用户查询', 100, 1, '', '', '', '1', '0', 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_menu VALUES (1001, '用户新增', 100, 2, '', '', '', '1', '0', 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_menu VALUES (1002, '用户修改', 100, 3, '', '', '', '1', '0', 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_menu VALUES (1003, '用户删除', 100, 4, '', '', '', '1', '0', 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_menu VALUES (1004, '用户导出', 100, 5, '', '', '', '1', '0', 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_menu VALUES (1005, '用户导入', 100, 6, '', '', '', '1', '0', 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_menu VALUES (1006, '重置密码', 100, 7, '', '', '', '1', '0', 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_notice VALUES (1, '温馨提醒：2024-11-11 新版本发布啦', '2', '<p>&lt;</p>', '0', 'admin', '2024-11-01 13:25:33.036153', 'admin', '2024-11-11 14:52:56.922403', '管理员');
INSERT INTO sys_post VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_post VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_post VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '');
INSERT INTO sys_post VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2024-11-01 13:25:32.886543', 'admin', '2024-11-02 13:57:55.332829', '');
INSERT INTO sys_role VALUES (1, '超级管理员', 'admin', 1, '1', '1', '1', '0', '0', 'admin', '2024-11-01 13:25:32.886543', '', NULL, '超级管理员');
INSERT INTO sys_role VALUES (2, '管理员', 'common', 2, '2', '1', '1', '0', '0', 'admin', '2024-11-01 13:25:32.886543', 'admin', '2024-11-11 15:01:34.471232', '普通角色');
INSERT INTO sys_role_dept VALUES (2, 100);
INSERT INTO sys_role_dept VALUES (2, 101);
INSERT INTO sys_role_dept VALUES (2, 105);
INSERT INTO sys_role_menu VALUES (1, 1);
INSERT INTO sys_role_menu VALUES (1, 100);
INSERT INTO sys_role_menu VALUES (1, 1000);
INSERT INTO sys_role_menu VALUES (1, 1001);
INSERT INTO sys_role_menu VALUES (1, 1002);
INSERT INTO sys_role_menu VALUES (1, 1003);
INSERT INTO sys_role_menu VALUES (1, 1004);
INSERT INTO sys_role_menu VALUES (1, 1005);
INSERT INTO sys_role_menu VALUES (1, 1006);
INSERT INTO sys_role_menu VALUES (2, 1);
INSERT INTO sys_role_menu VALUES (2, 100);
INSERT INTO sys_role_menu VALUES (2, 1000);
INSERT INTO sys_role_menu VALUES (2, 1001);
INSERT INTO sys_role_menu VALUES (2, 1002);
INSERT INTO sys_role_menu VALUES (2, 1003);
INSERT INTO sys_role_menu VALUES (2, 1004);
INSERT INTO sys_role_menu VALUES (2, 1005);
INSERT INTO sys_role_menu VALUES (2, 1006);
INSERT INTO sys_role_menu VALUES (2, 101);
INSERT INTO sys_role_menu VALUES (2, 1007);
INSERT INTO sys_role_menu VALUES (2, 1008);
INSERT INTO sys_role_menu VALUES (2, 1009);
INSERT INTO sys_role_menu VALUES (2, 1010);
INSERT INTO sys_role_menu VALUES (2, 1011);
INSERT INTO sys_role_menu VALUES (2, 102);
INSERT INTO sys_role_menu VALUES (2, 1012);
INSERT INTO sys_role_menu VALUES (2, 1013);
INSERT INTO sys_role_menu VALUES (2, 1014);
INSERT INTO sys_role_menu VALUES (2, 1015);
INSERT INTO sys_role_menu VALUES (2, 103);
INSERT INTO sys_role_menu VALUES (2, 1016);
INSERT INTO sys_role_menu VALUES (2, 1017);
INSERT INTO sys_role_menu VALUES (2, 1018);
INSERT INTO sys_role_menu VALUES (2, 1019);
INSERT INTO sys_role_menu VALUES (2, 104);
INSERT INTO sys_role_menu VALUES (2, 1020);
INSERT INTO sys_role_menu VALUES (2, 1021);
INSERT INTO sys_role_menu VALUES (2, 1022);
INSERT INTO sys_role_menu VALUES (2, 1023);
INSERT INTO sys_role_menu VALUES (2, 1024);
INSERT INTO sys_role_menu VALUES (2, 107);
INSERT INTO sys_role_menu VALUES (2, 1035);
INSERT INTO sys_role_menu VALUES (2, 1036);
INSERT INTO sys_role_menu VALUES (2, 1037);
INSERT INTO sys_role_menu VALUES (2, 1038);
INSERT INTO sys_role_menu VALUES (2, 108);
INSERT INTO sys_role_menu VALUES (2, 500);
INSERT INTO sys_role_menu VALUES (2, 1039);
INSERT INTO sys_role_menu VALUES (2, 1040);
INSERT INTO sys_role_menu VALUES (2, 1041);
INSERT INTO sys_role_menu VALUES (2, 501);
INSERT INTO sys_role_menu VALUES (2, 1042);
INSERT INTO sys_role_menu VALUES (2, 1043);
INSERT INTO sys_role_menu VALUES (2, 1044);
INSERT INTO sys_role_menu VALUES (2, 1045);

INSERT INTO sys_user VALUES (1, 100, 'admin', '管理员', '00', '<EMAIL>', '***********', '1', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '127.0.0.1', '2024-12-06 16:08:22.356', 'admin', '2024-11-01 13:25:32.886543', '', '2024-12-06 16:08:21.551856', '管理员');
INSERT INTO sys_user VALUES (2, 100, 'ufa', '222', '00', '<EMAIL>', '13800138000', '0', '', '$2a$10$Kb5kHgZuKbRotzxzyUwaHu/vJD91JOCvxh4Y5Ag0YpI/9evgzuB7q', '0', '2', '', NULL, 'admin', '2024-11-11 15:11:26.680697', '', NULL, NULL);
INSERT INTO sys_user_post VALUES (1, 1);
INSERT INTO sys_user_role VALUES (1, 1);
INSERT INTO sys_user_role VALUES (14, 1);
INSERT INTO sys_user_role VALUES (15, 1);
SELECT pg_catalog.setval('sys_config_config_id_seq', 100, false);
SELECT pg_catalog.setval('sys_dept_dept_id_seq', 2, true);
SELECT pg_catalog.setval('sys_dict_data_dict_code_seq', 100, false);
SELECT pg_catalog.setval('sys_dict_type_dict_id_seq', 100, false);
SELECT pg_catalog.setval('sys_logininfor_info_id_seq', 369, true);
SELECT pg_catalog.setval('sys_menu_menu_id_seq', 2000, false);
SELECT pg_catalog.setval('sys_notice_notice_id_seq', 10, false);
SELECT pg_catalog.setval('sys_oper_log_oper_id_seq', 257, true);
SELECT pg_catalog.setval('sys_post_post_id_seq', 1, false);
SELECT pg_catalog.setval('sys_role_role_id_seq', 100, false);
SELECT pg_catalog.setval('sys_user_user_id_seq', 16, true);
SELECT pg_catalog.setval('t_payment_id_seq', 92, true);
SELECT pg_catalog.setval('t_project_id_seq', 34, true);
ALTER TABLE ONLY sys_config
    ADD CONSTRAINT sys_config_pkey PRIMARY KEY (config_id);
ALTER TABLE ONLY sys_dept
    ADD CONSTRAINT sys_dept_pkey PRIMARY KEY (dept_id);
ALTER TABLE ONLY sys_dict_data
    ADD CONSTRAINT sys_dict_data_pkey PRIMARY KEY (dict_code);
ALTER TABLE ONLY sys_dict_type
    ADD CONSTRAINT sys_dict_type_dict_type_key UNIQUE (dict_type);
ALTER TABLE ONLY sys_dict_type
    ADD CONSTRAINT sys_dict_type_pkey PRIMARY KEY (dict_id);
ALTER TABLE ONLY sys_logininfor
    ADD CONSTRAINT sys_logininfor_pkey PRIMARY KEY (info_id);
ALTER TABLE ONLY sys_menu
    ADD CONSTRAINT sys_menu_pkey PRIMARY KEY (menu_id);
ALTER TABLE ONLY sys_notice
    ADD CONSTRAINT sys_notice_pkey PRIMARY KEY (notice_id);
ALTER TABLE ONLY sys_oper_log
    ADD CONSTRAINT sys_oper_log_pkey PRIMARY KEY (oper_id);
ALTER TABLE ONLY sys_post
    ADD CONSTRAINT sys_post_pkey PRIMARY KEY (post_id);
ALTER TABLE ONLY sys_role_dept
    ADD CONSTRAINT sys_role_dept_pkey PRIMARY KEY (role_id, dept_id);
ALTER TABLE ONLY sys_role_menu
    ADD CONSTRAINT sys_role_menu_pkey PRIMARY KEY (role_id, menu_id);
ALTER TABLE ONLY sys_role
    ADD CONSTRAINT sys_role_pkey PRIMARY KEY (role_id);
ALTER TABLE ONLY sys_user
    ADD CONSTRAINT sys_user_pkey PRIMARY KEY (user_id);
ALTER TABLE ONLY sys_user_post
    ADD CONSTRAINT sys_user_post_pkey PRIMARY KEY (user_id, post_id);
ALTER TABLE ONLY sys_user_role
    ADD CONSTRAINT sys_user_role_pkey PRIMARY KEY (user_id, role_id);
ALTER TABLE ONLY t_payment
    ADD CONSTRAINT t_payment_pkey PRIMARY KEY (payment_id);
ALTER TABLE ONLY t_project
    ADD CONSTRAINT t_project_pkey PRIMARY KEY (project_id);
CREATE INDEX idx_sys_logininfor_lt ON sys_logininfor USING btree (login_time);
CREATE INDEX idx_sys_logininfor_s ON sys_logininfor USING btree (status);
CREATE INDEX idx_sys_oper_log_bt ON sys_oper_log USING btree (business_type);
CREATE INDEX idx_sys_oper_log_ot ON sys_oper_log USING btree (oper_time);
CREATE INDEX idx_sys_oper_log_s ON sys_oper_log USING btree (status);
