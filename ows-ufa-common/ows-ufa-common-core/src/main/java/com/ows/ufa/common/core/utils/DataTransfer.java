package com.ows.ufa.common.core.utils;

import com.github.pagehelper.Page;
import com.ows.ufa.common.core.annotation.Sensitive;
import com.ows.ufa.common.core.enums.SensitiveType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

public class DataTransfer {
    private static final Logger log = LoggerFactory.getLogger(DataTransfer.class);
    /**
     * 可转换支持的特殊类型
     */
    private static final Set<Class<?>> wrapperTypeSet = new HashSet<>();
    /**
     * 字符串分割符
     */
    public static final String MULTIPLE_DELIM = "&&&";

    public static final String MULTIPLE_DELIM_COMMON = ",";

    public static final String MULTIPLE_DELIM_REGION = "-";

    static {
        wrapperTypeSet.add(LocalDateTime.class);
        wrapperTypeSet.add(Date.class);
        wrapperTypeSet.add(String.class);
        wrapperTypeSet.add(List.class);
    }
    public static Object transfer(Object source, Class classType) {
        return transfer(source,classType,true);
    }
    /**
     * 參考org.springframework.beans.BeanUtils.copyProperties方法进行属性赋值，并扩展支持不同类型属性之间的转换
     *
     * @param source    待转换数据
     * @param classType 转换目标的类型
     * @return 转换目标的类型的对象
     */
    public static Object transfer(Object source, Class classType,boolean isEncrypt) {
        if (null == source) {
            return null;
        }
        Object target = null;
        try {
            target = classType.newInstance();
        } catch (InstantiationException e) {
            log.error(e.getMessage());
        } catch (IllegalAccessException e) {
            log.error(e.getMessage());
        }
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(target.getClass());
        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
            if (null == writeMethod || null == sourcePd || null == sourcePd.getReadMethod()) {
                continue;
            }
            Method readMethod = sourcePd.getReadMethod();
            Class sourcePropertyClass = readMethod.getReturnType();
            Class targetPropertyClass = writeMethod.getParameterTypes()[0];
            try {
                /*if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                    readMethod.setAccessible(true);
                }*/
                Object value = readMethod.invoke(source);
                /*if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                    writeMethod.setAccessible(true);
                }*/
                String fieldName = writeMethod.getName().substring(3, 4).toLowerCase() +
                        writeMethod.getName().substring(4);
                Field field = target.getClass().getDeclaredField(fieldName);
                if(sourcePropertyClass.equals(String.class) && targetPropertyClass.equals(String.class) && isEncrypt && field.isAnnotationPresent(Sensitive.class)&& null != value && !value.toString().contains("***")){
                    Sensitive sensitive = field.getAnnotation(Sensitive.class);
                    String originalValue = value.toString();
                    String maskedValue = maskValue(originalValue, sensitive.type());
                    // 脱敏
                    writeMethod.invoke(target, maskedValue);
                    // Determine cipher field name
                    String cipherFieldName = sensitive.cipherField().isEmpty() ?
                            fieldName + "Ciphertext" : sensitive.cipherField();

                    // Encrypt and set to cipher field
                    String encryptedValue = encryptValue(originalValue);
                    setFieldValue(target, cipherFieldName, encryptedValue);

                    String plainFieldName = sensitive.plainField().isEmpty() ?
                            fieldName + "Original" : sensitive.plainField();
                    setFieldValue(target, plainFieldName, originalValue);

                } else if (sourcePropertyClass.equals(String.class) && targetPropertyClass.equals(String.class) && isEncrypt
                        && (fieldName.endsWith("Original") || fieldName.endsWith("Ciphertext"))) {
                    String maskFieldName = fieldName;
                    if (fieldName.endsWith("Original")) {
                        maskFieldName = fieldName.substring(0, fieldName.length() - "Original".length());
                    } else if (fieldName.endsWith("Ciphertext")) {
                        maskFieldName = fieldName.substring(0, fieldName.length() - "Ciphertext".length());
                    }
                    Field maskField = target.getClass().getDeclaredField(maskFieldName);
                    if (!maskField.isAnnotationPresent(Sensitive.class)) {
                        writeMethod.invoke(target, value);
                    }
                } else if (ClassUtils.isAssignable(sourcePropertyClass, targetPropertyClass) || null == value) {
                    writeMethod.invoke(target, value);
                } else if (wrapperTypeSet.contains(sourcePropertyClass)) {
                    extracted(target, writeMethod, sourcePropertyClass, targetPropertyClass, value);
                }
            } catch (Throwable ex) {
                throw new FatalBeanException(
                        "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
            }
        }
        return target;
    }

    /**
     * 脱敏处理
     */
    public static String maskValue(String value, SensitiveType type) {
        if (value == null || value.isEmpty()) {
            return value;
        }

        switch (type) {
            case PHONE:
                if (value.length() == 11) {
                    return value.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
                }
                return value;
            case ID_CARD:
                String cleanId = value.replaceAll("[^0-9Xx]", "");
                if (cleanId.length() >= 6) {
                    return cleanId.replaceAll("(?<=^\\d{3}).*(?=\\d{2}[0-9Xx]$)", "************");
                }
                return value;
            default:
                return value;
        }
    }

    /**
     * 加密处理
     */
    public static String encryptValue(String value) {
        return SensitiveUtil.encrypt(value);
    }

    /**
     * 设置字段值
     */
    public static void setFieldValue(Object target, String fieldName, Object value) {
        try {
            PropertyDescriptor pd = BeanUtils.getPropertyDescriptor(target.getClass(), fieldName);
            if (pd != null && pd.getWriteMethod() != null) {
                Method writeMethod = pd.getWriteMethod();
                writeMethod.invoke(target, value);
            }
        } catch (Exception e) {
            log.error("Failed to set field value for {}", fieldName, e);
        }
    }


    /**
     * 根据类型转换
     *
     * @param target
     * @param writeMethod
     * @param sourcePropertyClass
     * @param targetPropertyClass
     * @param value
     * @throws IllegalAccessException
     * @throws InvocationTargetException
     */
    private static void extracted(Object target, Method writeMethod, Class sourcePropertyClass, Class targetPropertyClass, Object value) throws IllegalAccessException, InvocationTargetException {
        if (sourcePropertyClass.equals(String.class)) {
            String sourceString = (String) value;

            if (targetPropertyClass.equals(List.class)) {
                Type[] genericParameterTypes = writeMethod.getGenericParameterTypes();
                if (genericParameterTypes.length > 0 && genericParameterTypes[0] instanceof ParameterizedType) {
                    ParameterizedType listType = (ParameterizedType) genericParameterTypes[0];
                    Type[] actualTypeArguments = listType.getActualTypeArguments();

                    if (actualTypeArguments.length == 1) {
                        Class<?> listElementType = (Class<?>) actualTypeArguments[0];

                        if (listElementType.equals(Long.class)) {
                            String splitStr = sourceString.contains(MULTIPLE_DELIM) ? MULTIPLE_DELIM : ",";
                            if (null == StringUtils.delimitedListToStringArray(sourceString, splitStr)) {
                                List<Long> longList = Arrays.asList(Long.parseLong(sourceString));
                                writeMethod.invoke(target, longList);
                                return;
                            }
                            List<Long> longList = Arrays.stream(StringUtils.delimitedListToStringArray(sourceString, splitStr))
                                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                                    .map(s -> {
                                        try {
                                            return Long.parseLong(s);
                                        } catch (NumberFormatException e) {
                                            throw new IllegalArgumentException("无法将字符串转换为 Long: " + s, e);
                                        }
                                    })
                                    .collect(Collectors.toList());
                            writeMethod.invoke(target, longList);
                            return;
                        } else if (listElementType.equals(String.class)) {
                            String splitStr = sourceString.contains(MULTIPLE_DELIM) ? MULTIPLE_DELIM : ",";
                            if (null == StringUtils.delimitedListToStringArray(sourceString, splitStr)) {
                                List<String> stringList = Arrays.asList(sourceString);
                                writeMethod.invoke(target, stringList);
                                return;
                            }
                            List<String> stringList = Arrays.stream(StringUtils.delimitedListToStringArray(sourceString, splitStr))
                                    .filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                                    .collect(Collectors.toList());
                            writeMethod.invoke(target, stringList);
                            return;
                        } else {
                            throw new IllegalArgumentException("Unsupported List element type: " + listElementType);
                        }
                    }
                }
                throw new IllegalArgumentException("Unsupported target type: " + targetPropertyClass);
            } else if (targetPropertyClass.equals(LocalDateTime.class)) {
                LocalDateTime dateTime = LocalDateTime.parse(sourceString);
                writeMethod.invoke(target, dateTime);
            }
        } else if (sourcePropertyClass.equals(List.class)) {
            List list = (List) value;
            String targetString;
            if (null == list || list.isEmpty()) {
                targetString = "";
            } else {
                targetString = StringUtils.arrayToDelimitedString(list.toArray(), MULTIPLE_DELIM);
            }
            writeMethod.invoke(target, targetString);
        } else if (sourcePropertyClass.equals(LocalDateTime.class)) {
            LocalDateTime dateTime = (LocalDateTime) value;
            String targetString = dateTime.toString();
            writeMethod.invoke(target, targetString);
        } else if (sourcePropertyClass.equals(Date.class)) {
            if (targetPropertyClass.equals(LocalDateTime.class)) {
                LocalDateTime dateTime = ((Date) value).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                writeMethod.invoke(target, dateTime);
            }
        } else if (sourcePropertyClass.equals(LocalDateTime.class)) {
            if (targetPropertyClass.equals(Date.class)) {
                Date date = Date.from(((LocalDateTime) value).atZone(ZoneId.systemDefault()).toInstant());
                writeMethod.invoke(target, date);
            }
        }
    }

    public static List transferToList(String sources) {
        String splitStr = MULTIPLE_DELIM_COMMON;
        if (sources.contains(MULTIPLE_DELIM)) {
            splitStr = MULTIPLE_DELIM;
        }
        List list = Arrays.asList(StringUtils.delimitedListToStringArray(sources, splitStr));
        return list;
    }

    /**
     * 循环遍历调用transfer方法，使数据转换支持集合类型。
     *
     * @param sources   待转换数据集合list
     * @param classType 转换目标的类型
     * @return 转换目标的类型的对象list
     */
    public static List transferList(List sources, Class classType) {
        if (null == sources) {
            return null;
        }
        List targetList = new ArrayList();
        for (Object source : sources) {
            Object target = transfer(source, classType);
            targetList.add(target);
        }
        if (sources instanceof Page) {
            Page sourcePage = (Page) sources;
            Page targetPage = new Page<>();
            BeanUtils.copyProperties(sourcePage, targetPage, "list");
            targetPage.addAll(targetList);
            return targetPage;
        } else {
            return targetList;
        }

    }

    public static String transferString(List<String> sources) {
        if (!sources.isEmpty()) {
            return StringUtils.arrayToDelimitedString(sources.toArray(), MULTIPLE_DELIM_REGION);
        }
        return "";
    }

    public static String transferString(String sources) {
        if (com.ows.ufa.common.core.utils.StringUtils.isNoneBlank(sources)) {
            return sources.replaceAll(DataTransfer.MULTIPLE_DELIM, MULTIPLE_DELIM_REGION);
        }
        return sources;
    }

    public static String importTransferString(String sources) {
        if (com.ows.ufa.common.core.utils.StringUtils.isNoneBlank(sources)) {
            return sources.replaceAll(DataTransfer.MULTIPLE_DELIM_REGION, MULTIPLE_DELIM);
        }
        return sources;
    }
}
