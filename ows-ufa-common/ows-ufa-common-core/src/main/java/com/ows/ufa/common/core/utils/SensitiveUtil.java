package com.ows.ufa.common.core.utils;

import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;

import java.util.Arrays;
import java.util.List;

public class SensitiveUtil {

    /**
     * SM4是对称加密，需要设置一个加解密秘钥
     * <p>
     * System.out.println(Arrays.toString("@Jhx2024#$%^&*!+".getBytes(StandardCharsets.UTF_8)));
     * 特别注意字符串key的长度需要16位
     */
    private static final byte[] keys = new byte[]{64, 74, 104, 120, 50, 48, 50, 52, 35, 36, 37, 94, 38, 42, 33, 43};

    /**
     * 创建一个SM4加解密对象
     */
    private static final SM4 sm4 = SmUtil.sm4(keys);

    /**
     * 设置一个标识符，标识@BASC@- 开头的字符串是经过SM4加密的需要解密
     */
    public static final String SM4_PREFIX = "@BASC@-";


    /**
     * 对字符串进行加密
     *
     * @param value
     * @return
     */
    public static String encrypt(String value) {
        // 对加密的字符串添加前缀，方便标识这是一个加密以后的字符串
        return SM4_PREFIX + sm4.encryptBase64(value);
    }

    /**
     * 对字符串进行解密
     *
     * @param encryptValue
     * @return
     */
    public static String decrypt(String encryptValue) {
        // 解密时，需要去除加密标识符
        return !StringUtils.isEmpty(encryptValue) && encryptValue.startsWith(SM4_PREFIX) ? sm4.decryptStr(encryptValue.substring(SM4_PREFIX.length())) : encryptValue;
    }

    public static void main(String[] args) {
        List<String> a = Arrays.asList("@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-BFXocdUwuSoLB594Boj7UQ==","@BASC@-G0eB/iddpcANqJfJWBnvsw==","@BASC@-Lwjn+0YcX5P6zNbfg1LQCw==","@BASC@-fFHYP3jilPs4Mlg0cCG6dA==","","@BASC@-fFHYP3jilPs4Mlg0cCG6dA==","@BASC@-OnapGSwfx7DocHjtMhXoeA==","@BASC@-bGw25Ij1arjmih90C0kbSg==","@BASC@-d91u5NjfvUOE44dallMv5Q==","@BASC@-IaBR00ZISrTUlCE8ZTx8yg==","","@BASC@-wU4ht6DqEqixFjG0u19lnw==","","@BASC@-Wvvhfdjsv4PrfRnVEiBnxw==","","@BASC@-7NwBBUZrOhKzY7AQVGsTgg==","@BASC@-gPYhs1tG+UZG46yvp4/Gpw==","@BASC@-DvSmdRIWU9w/v5Rwr91Ruw==","@BASC@-Yah9v/Mpcuo2S5dE8Qruzw==","@BASC@-BFXocdUwuSoLB594Boj7UQ==","@BASC@-940AlIl9vs+FPB0yWecWZw==","@BASC@-S7msyrp7hC/jua2Wi2VGHw==","@BASC@-mUhWQerErwRckHDAxSUkzw==","@BASC@-RYkP11K8vtjwFwm5dOyRKg==","@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-LJgwuDfw2ivoMGkRYoz8sQ==","@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-fjJJF7aDDIh+twY0UfLA2A==","@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-e8Fd0KYqrfHFNCsRBcNLkA==","@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-8BfxGwBrM1HWAvvhoGl6xQ==","@BASC@-wk80B+ck1UcpOR/hG0rIjw==","@BASC@-W/Rgber/YsFwDXvd147bmA==","@BASC@-Z1sOXfxN7XPeb7Xr/Z8fUg==","@BASC@-1UxrSqjoliJS8bYGnleRoA==","@BASC@-8fbndPXnO2TFMwoT9qtFVA==","@BASC@-PSCw+90KelBR44cPrR97rw==","@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-61baD6/orj8aFjRE9VdMpA==","@BASC@-BFXocdUwuSoLB594Boj7UQ==","@BASC@-UFboC95mYtVx5cm+DvxZvA==","@BASC@-BFXocdUwuSoLB594Boj7UQ==","@BASC@-8BfxGwBrM1HWAvvhoGl6xQ==","@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-gPYhs1tG+UZG46yvp4/Gpw==","@BASC@-byksaZfpXvoDGpfjHRtHUQ==","@BASC@-oo+RUS5PIBmFWo/TWKKAqw==","@BASC@-n7Jstrczur2kBZiL0bYgkA==","@BASC@-ikl5DwIAhjrDuUbnJlzeUA==","@BASC@-/Pw3UKDtTCEC75bhTRyLlQ==","@BASC@-HIOL0/AEEQtVarfd0lUTqA==","@BASC@-vkzWYqFazrU6opjXeJLkbw==","");
        for(String b:a){
            if(org.apache.commons.lang3.StringUtils.isEmpty(b)){
                System.out.println(" "+"  "+" ");
            }
            System.out.println(b+"  "+decrypt(b));
        }

    }
}
