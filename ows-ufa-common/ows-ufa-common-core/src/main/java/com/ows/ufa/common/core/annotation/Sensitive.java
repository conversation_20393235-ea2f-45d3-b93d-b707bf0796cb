package com.ows.ufa.common.core.annotation;


import com.ows.ufa.common.core.enums.SensitiveType;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Sensitive {
    /**
     * 脱敏类型
     */
    SensitiveType type();

    /**
     * 密文字段名
     */
    String cipherField() default "";

    /**
     * 明文字段名（用于备份原始值）
     */
    String plainField() default "";
}
