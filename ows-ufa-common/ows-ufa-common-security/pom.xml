<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.ows.ufa</groupId>
        <artifactId>ows-ufa-common</artifactId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    
    <artifactId>ows-ufa-common-security</artifactId>

    <description>
        ows-ufa-common-security安全模块
    </description>

    <dependencies>

        <!-- Spring Web -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>

        <!-- OWS-UFA Api System -->
        <dependency>
            <groupId>com.ows.ufa</groupId>
            <artifactId>ows-ufa-api-system</artifactId>
        </dependency>

        <!-- OWS-UFA Common Redis-->
        <dependency>
            <groupId>com.ows.ufa</groupId>
            <artifactId>ows-ufa-common-redis</artifactId>
        </dependency>

    </dependencies>

</project>
