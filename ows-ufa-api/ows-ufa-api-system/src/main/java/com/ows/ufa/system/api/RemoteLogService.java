package com.ows.ufa.system.api;

import com.ows.ufa.system.api.domain.SysLogininfor;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import com.ows.ufa.common.core.constant.SecurityConstants;
import com.ows.ufa.common.core.constant.ServiceNameConstants;
import com.ows.ufa.common.core.domain.R;
import com.ows.ufa.system.api.domain.SysOperLog;
import com.ows.ufa.system.api.factory.RemoteLogFallbackFactory;

/**
 * 日志服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteLogService", value = ServiceNameConstants.SYSTEM_SERVICE, url = "${spring.cloud.openfeign.client.config.ows-ufa-system.url:}", fallbackFactory = RemoteLogFallbackFactory.class)
public interface RemoteLogService
{
    /**
     * 保存系统日志
     *
     * @param sysOperLog 日志实体
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/operlog")
    public R<Boolean> saveLog(@RequestBody SysOperLog sysOperLog, @RequestHeader(SecurityConstants.FROM_SOURCE) String source) throws Exception;

    /**
     * 保存访问记录
     *
     * @param sysLogininfor 访问实体
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/logininfor")
    public R<Boolean> saveLogininfor(@RequestBody SysLogininfor sysLogininfor, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
