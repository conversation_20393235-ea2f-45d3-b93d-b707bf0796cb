# Tomcat
server:
  port: 8080
  compression:
    enabled: true  #  开启支持gzip压缩
    mime-types:  application/json,application/xml,text/html,text/xml,text/plain,application/javascript,text/css
    min-response-size:  128  #  当响应长度超过128时，才执行压缩（默认2048）

# Spring
spring: 
  application:
    # 应用名称
    name: ows-ufa-gateway
  profiles:
    # 环境配置
    active: dev
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure

  cloud:
    gateway:
      globalcors:
        corsConfigurations:
          '[/**]':
            allowedOriginPatterns: "*"
            allowedMethods: "*"
            allowedHeaders:   "*"   #   允许所有的请求头，也可以指定具体的请求头
            allowCredentials:   true   #   是否允许携带凭证（cookies）
            maxAge:   3600




# feign 配置
feign:
  sentinel:
    enabled: true
  okhttp:
    enabled: true
  httpclient:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 10000
        readTimeout: 10000
  compression:
    request:
      enabled: true
      min-request-size: 8192
    response:
      enabled: true

# 暴露监控端点
management:
  endpoints:
    enabled-by-default: false
