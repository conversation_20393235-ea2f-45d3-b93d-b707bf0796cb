<h4 align="center">基于 Vue/Element UI 和 Spring Boot/Spring Cloud & Alibaba 前后端分离的分布式微服务架构</h4>
## 平台简介

* 采用前后端分离的模式，微服务版本前端。
* 后端采用Spring Boot、Spring Cloud & Alibaba。
* 注册中心、配置中心选型Nacos，权限认证使用Redis。
* 流量控制框架选型Sentinel，分布式事务选型Seata。

## 系统模块

~~~
com.ows.ufa     
├── ows-ufa-ui              // 前端框架 [80]
├── ows-ufa-gateway         // 网关模块 [8080]
├── ows-ufa-auth            // 认证中心 [9200]
├── ows-ufa-api             // 接口模块
│       └── ows-ufa-api-system                          // 系统接口
├── ows-ufa-common          // 通用模块
│       └── ows-ufa-common-core                         // 核心模块
│       └── ows-ufa-common-datascope                    // 权限范围
│       └── ows-ufa-common-datasource                   // 多数据源
│       └── ows-ufa-common-log                          // 日志记录
│       └── ows-ufa-common-redis                        // 缓存服务
│       └── ows-ufa-common-seata                        // 分布式事务
│       └── ows-ufa-common-security                     // 安全模块
│       └── ows-ufa-common-sensitive                    // 数据脱敏
│       └── ows-ufa-common-swagger                      // 系统接口
├── ows-ufa-modules         // 业务模块
│       └── ows-ufa-system                              // 系统模块 [9201]
├──pom.xml                // 公共依赖
~~~
