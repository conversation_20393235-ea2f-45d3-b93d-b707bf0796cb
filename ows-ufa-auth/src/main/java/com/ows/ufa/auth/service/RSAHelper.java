package com.ows.ufa.auth.service;

import com.ows.ufa.common.core.exception.ServiceException;
import org.bouncycastle.util.encoders.Base64;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Arrays;

public class RSAHelper {


    public static String encrypt(String json, String publicKeyStr) {
        try {
            byte[] publicKeyBytes = org.bouncycastle.util.encoders.Base64.decode(publicKeyStr);
            X509EncodedKeySpec x509KeySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey restoredPublicKey = keyFactory.generatePublic(x509KeySpec);
            // 2. 加密
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding"); // 指定填充模式
            cipher.init(Cipher.ENCRYPT_MODE, restoredPublicKey);

            byte[] jsonBytes = json.getBytes("UTF-8");
            int maxEncryptLen = 117;// 根据RSA加密块大小和填充方式确定，1024位密钥下
            int inputLen = jsonBytes.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段加密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > maxEncryptLen) {
                    cache = cipher.doFinal(jsonBytes, offSet, maxEncryptLen);
                } else {
                    cache = cipher.doFinal(jsonBytes, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * maxEncryptLen;
            }
            byte[] encryptedData = out.toByteArray();
            out.close();

            return bytesToHex(encryptedData);
        } catch (Exception e) {
            throw new ServiceException("加密失败" + e.getMessage());
        }
        // 从字符串形式恢复公钥
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    private static byte[] hexStringToByteArray(String s) {
        int len = s.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
                    + Character.digit(s.charAt(i + 1), 16));
        }
        return data;
    }

    public static String decrypt(String json, String privateKeyStr) {
        // 解码私钥
        try {
            byte[] privateKeyBytes = Base64.decode(privateKeyStr);
            PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(privateKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey restoredPrivateKey = keyFactory.generatePrivate(pkcs8KeySpec);
            // 初始化Cipher对象进行解密
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding"); // 指定填充模式
            cipher.init(Cipher.DECRYPT_MODE, restoredPrivateKey);

            // 将十六进制字符串转换为字节数组
            byte[] encryptedData = hexStringToByteArray(json);
            int maxDecryptLen = 128; // 根据RSA密钥长度和填充方式确定，1024位密钥下
            int inputLen = encryptedData.length;
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            int offSet = 0;
            byte[] cache;
            int i = 0;
            // 对数据分段解密
            while (inputLen - offSet > 0) {
                if (inputLen - offSet > maxDecryptLen) {
                    cache = cipher.doFinal(encryptedData, offSet, maxDecryptLen);
                } else {
                    cache = cipher.doFinal(encryptedData, offSet, inputLen - offSet);
                }
                out.write(cache, 0, cache.length);
                i++;
                offSet = i * maxDecryptLen;
            }
            byte[] decryptedData = out.toByteArray();
            out.close();

            return new String(decryptedData, "UTF-8");
        } catch (Exception e) {
            throw new ServiceException("加密失败" + e.getMessage());
        }
    }
}
