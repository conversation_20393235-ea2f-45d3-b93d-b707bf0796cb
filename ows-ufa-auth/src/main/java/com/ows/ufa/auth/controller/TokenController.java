package com.ows.ufa.auth.controller;

import javax.servlet.http.HttpServletRequest;

import com.ows.ufa.auth.form.LoginBody;
import com.ows.ufa.auth.form.RegisterBody;
import com.ows.ufa.auth.form.ThirdPartLoginBody;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.ows.ufa.auth.service.SysLoginService;
import com.ows.ufa.common.core.domain.R;
import com.ows.ufa.common.core.utils.JwtUtils;
import com.ows.ufa.common.core.utils.StringUtils;
import com.ows.ufa.common.security.auth.AuthUtil;
import com.ows.ufa.common.security.service.TokenService;
import com.ows.ufa.common.security.utils.SecurityUtils;
import com.ows.ufa.system.api.model.LoginUser;

/**
 * token 控制
 * 
 * <AUTHOR>
 */
@RestController
public class TokenController
{
    @Autowired
    private TokenService tokenService;

    @Autowired
    private SysLoginService sysLoginService;

    @PostMapping("login")
    public R<?> login(@RequestBody LoginBody form)
    {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @PostMapping("loginNew")
    public R<?> loginNew(@RequestBody LoginBody form)
    {
        // 用户登录
        LoginUser userInfo = sysLoginService.login(form.getUsername(), form.getPassword());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @PostMapping("loginByUserId")
    public R<?> loginByUserId(@RequestBody LoginBody form)
    {
        // 用户登录
        LoginUser userInfo = sysLoginService.loginByUserId(form.getUserId());
        // 获取登录token
        return R.ok(tokenService.createToken(userInfo));
    }

    @PostMapping("thirdPartLogin")
    public R<?> thirdPartLogin(@RequestBody ThirdPartLoginBody form)
    {
        // 用户登录
        LoginUser userInfo = sysLoginService.loginByToken(form.getToken(),form.getTicket());
        // 获取登录token
        if("1".equals(form.getSpecial())){
            return R.ok(tokenService.createLongToken(userInfo));
        }
        return R.ok(tokenService.createToken(userInfo));
    }

    @DeleteMapping("logout")
    public R<?> logout(HttpServletRequest request)
    {
        String token = SecurityUtils.getToken(request);
        if (StringUtils.isNotEmpty(token))
        {
            String username = JwtUtils.getUserName(token);
            // 删除用户缓存记录
            AuthUtil.logoutByToken(token);
            // 记录用户退出日志
            sysLoginService.logout(username);
        }
        return R.ok();
    }

    @PostMapping("refresh")
    public R<?> refresh(HttpServletRequest request)
    {
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser))
        {
            // 刷新令牌有效期
            tokenService.refreshToken(loginUser);
            return R.ok();
        }
        return R.ok();
    }

    @PostMapping("register")
    public R<?> register(@RequestBody RegisterBody registerBody)
    {
        // 用户注册
        sysLoginService.register(registerBody.getUsername(), registerBody.getPassword());
        return R.ok();
    }
}
