package com.ows.ufa.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import com.ows.ufa.common.security.annotation.EnableRyFeignClients;

/**
 * 认证授权中心
 * 
 * <AUTHOR>
 */
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class })
public class OwsUfaAuthApplication
{
    public static void main(String[] args)
    {
        SpringApplication.run(OwsUfaAuthApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  认证授权中心启动成功   ლ(´ڡ`ლ)ﾞ;");
    }
}
