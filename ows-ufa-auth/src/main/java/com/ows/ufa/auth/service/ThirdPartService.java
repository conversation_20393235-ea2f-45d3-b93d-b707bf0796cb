package com.ows.ufa.auth.service;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.jsontype.BasicPolymorphicTypeValidator;
import com.ows.ufa.auth.configuration.ThirdApiProperties;
import com.ows.ufa.auth.form.ResBody;
import com.ows.ufa.common.core.constant.SecurityConstants;
import com.ows.ufa.common.core.domain.R;
import com.ows.ufa.common.core.exception.ServiceException;
import com.ows.ufa.common.core.web.domain.AjaxResult;
import com.ows.ufa.system.api.RemoteUserService;
import com.ows.ufa.system.api.domain.SysDept;
import com.ows.ufa.system.api.domain.SysUser;
import com.ows.ufa.system.api.model.LoginUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Service
public class ThirdPartService {
    @Autowired
    private ThirdApiProperties thirdApiProperties;
    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private RemoteUserService remoteUserService;

    public LoginUser login(String token,String ticket) {
        // 构建请求头
        HttpHeaders headers = new HttpHeaders();
        //添加Referer
        headers.add("Referer", thirdApiProperties.getLoginUrl());
        if (token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        Map<String, String> request = new HashMap<>();
        request.put("token", token);
        request.put("identify", thirdApiProperties.getIdentify());
        request.put("ticket",ticket);
        String encrypt = RSAHelper.encrypt(JSONObject.toJSONString(request), thirdApiProperties.getPublicKeyStr());
        Map<String, String>  requestBody= new HashMap<>();
        requestBody.put("json", encrypt);
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(requestBody, headers);
        ResponseEntity<ResBody> infoEntity = restTemplate.exchange(
                thirdApiProperties.getLoginUrl(),
                HttpMethod.POST,
                requestEntity,
                ResBody.class
        );
        if (infoEntity.getStatusCode() != HttpStatus.OK) {
            return null;
        }
        AjaxResult result = null;
        try {
            String decrypt = RSAHelper.decrypt(infoEntity.getBody().getJson(), thirdApiProperties.getPrivateKeyStr());
            result = JSONObject.parseObject(decrypt, AjaxResult.class);
        } catch (Exception e) {
            throw new ServiceException("解密失败" + e.getMessage());
        }

        if (null == result || (int) result.get("code") != 1) {
            return null;
        }
        LoginUser sysUserVo = new LoginUser();
        sysUserVo.setThird(true);
        Map data = (Map) result.get("data");
        SysUser sysUser = new SysUser();
        Map dept = (Map) data.get("school");
        sysUser.setDeptId(String.valueOf(dept.get("deptId")));
        SysDept sysDept = new SysDept();
        sysDept.setDeptId(getLongValue(dept.get("deptId")));
        sysDept.setDeptName((String) dept.get("deptName"));
        sysDept.setParentId(getLongValue(dept.get("parentId")));
        sysDept.setParentName((String) dept.get("parentName"));
        sysDept.setDeptAddress((String) dept.get("deptAddress"));
        R<String> deptAncestors = remoteUserService.getInfoData(sysUser.getDeptId(), SecurityConstants.INNER);
        sysDept.setAncestors(deptAncestors.getData());
        sysUser.setDept(sysDept);
        sysUser.setUserName((String) data.get("userName"));
        sysUser.setNickName((String) data.get("nickName"));
        sysUser.setThirdUserId(String.valueOf(data.get("userId")));
        sysUser.setUserId(1L);
        List routers = (List) result.get("auth");
        sysUserVo.setUserid(1L);
        sysUserVo.setThirdUserid(sysUser.getThirdUserId());
        sysUserVo.setThirdDeptId(String.valueOf(dept.get("deptId")));
        sysUserVo.setAncestors(getAncestors(sysDept.getAncestors()));
        sysUserVo.setUsername(sysUser.getUserName());
        sysUserVo.setSysUser(sysUser);
        sysUserVo.setRouters(routers);
        return sysUserVo;
    }

    //getLongValue((String) dept.get("deptid")) 提取公共方法避免null
    private Long getLongValue(Object s) {
        if(null == s) {
            return null;
        }
        return Long.valueOf(s.toString());
    }

    private List<String> getAncestors(String deptAncestors){
        if(StringUtils.isNotBlank(deptAncestors)){
            return Arrays.asList(deptAncestors.split(","));
        }
        return new ArrayList<>();
    }
}