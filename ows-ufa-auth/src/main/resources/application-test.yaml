# spring配置
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: nacos:8848
      config:
        # 配置中心地址
        server-addr: nacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  redis:
    host: **************
    port: 9000
    password: Jnsk_81908834
    database: 6
third-api:
    loginUrl: https://cqlncs.12399.gov.cn:9003/glpt-api/stu/port/auth
    identify: mc
    publicKeyStr: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCF8cl+I/Di6bMnNoXqH2BHr9a2IDbnpkM6CZzZLfuzWQTHSmeQXMg+Xdq5iZWmvRjr+UJpZus5zfznXtr0zGKlwuLhYFch2SHfZF7/CooKJH8P0WytqMU3iK5DZTQ5NTfLhbaERNjFK83qlGe+yO8TNIsSApqItTIK+k1bGgso9wIDAQAB
    privateKeyStr: MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAJPdOBMtinAxWxtkM3PrzfYNpQgCy/tG+f2h8e+7gM1FnRjZXp/a0QTrEEuP89PFEhaalzqhm2UL9QBDXHpOxrRfIF/i+7GUMTcec3Ic1MH4aEwV99G4N16ElZ5tilryjYkeGC60tlHkqiZr4z8mbGJuh+KrYgrQGjYaR1/JDS0PAgMBAAECgYACn7teA8HgKdAPFWEgXn/iLO3PzZTnGaRYDuanNXGcsu6NwVW/ineJGc7JOH3ANVAT3JxwauvCCEtJvOhIN1upUtQVSBb/ad3t84B0Zrhj03B6gKKGhbKBZbBIQswUJ6QLut1lcLB9sFkOBPm9dYytDudtOqOCKiBSAdpUZPMBMQJBANSotVd2DbP6wUGPEltmhbxYE6Mg0sMBoE2DVu977FZAnp4jVuFZkbNJD6OJddxOtHpaFR90DDbaB4Pp+lwrB2cCQQCx/+V/y064E0f7np4C2jG016mQxSGwFgZfNDBcvlGdzBBhVPfxwQrZYzWXwc66kVQ8tj+N9/F4yUmfwX8eNmwZAkBivp/Np8x/GN1psqRkent41DDyG0iiMfwcYfMZKqK3/jNo3KH3655C8JbpaeenT4hwE6ohO8J8Kcs5joipCXXbAkABB4TRP2C+KcZhyyIIzDA7Zn/hiXJPbWEuDcQ11RftvK9fkLk6Wa5xYWR+8yz54TtGeptVe9iWpTtaxCizCQeJAkBWXngZSsy9WZmzfhE3ID3DoyR0eDvLgNp/y623AtcdJUNTcpnYq6CqPtR4O/0BejAJnerpxVR048uCKWhaKtm3