# spring配置
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: nacos:8848
      config:
        # 配置中心地址
        server-addr: nacos:8848
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
  redis:
    host: *************
    port: 9000
    password: Lmcc_58120427
    database: 11
third-api:
    loginUrl: https://cqlndx.12399.gov.cn:9003/glpt-api/stu/port/auth
    identify: mc
    publicKeyStr: MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCGQoBqSrFZM+5CAFmI08KNA2yjIg2Oa/tp4wSSRqlRpNWl3UfVEYZvhe8iAEedobOGyXqZ9KjZxnJgLxyX/wx7wQlVFhsFA2d8Um07KyZmCgpm+BagSKXOC3KWMqZBmwQM/kKAODkMxBsBUBLj0nKsraU1zbecXoR+tk4bbqbOpwIDAQAB
    privateKeyStr: MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAILqAQputX1CTcf0CfQNVQVg0Hb5hGwK5sYeQ02/bnAZWwC4+5FBo4SbQ6lRnHwSMAO90jHxrH7n00yEcAnEhzBasWa2yHMCwPnEJreULLGvvVsW4/G7YlkJ17x1lgdI4y76lkmWi+0bfbrw+SB+y2Ex1/AHd1HGaxX0HA3RHWepAgMBAAECgYBasfk0nAw6EiRGwYffvxjG2BK0Shr2qvrECJVCGLxPBxHnxz/pmxrItLREaljKD+OZw/kKRs7lQIA/g4UHD1NEK/gS26PWm0U5K9ZCzxtqcHCguL7uD1SLV556UYsncsq+kcuVLuumP1M4n/q+SPBcryCktWXrG/9HkIhIdmtvAQJBALekveUymDV+zKdHQ6FldGD9vSmOZyrl1+V001ncFA4qII9OyTgQBrEKqmKBUkHs89LCPZQYA6eqFY1l8WTpZwkCQQC2frD5MmF5Zk33pU/SMsh7HGsW1x+6BV9oHaM7SCOhMadlnKK7dNcAJt4hmBtCkMzJhkYwCPkMa9pfDieSSoOhAkEAjeT+zS+QqBT3cHEB8pz/lUm5dXiQgnbhoGvqOk5wOJmYWuKXW3gWk4kYKdCejE3X/4sSJXGAsXYb/Qs9v6kQcQJAFBi1gmuy4jyK33eb56jh/PSvMk+0Vbbbv8prvE6AZfLi3US3gu8l8gVVtttaPSVW9+ZKemWyj1SdMpSLdQexoQJBAJLe8FImPLBmC+p/a1u4u1sll5q2UaTsx6KF4mmqZNNVpaVUI40iRaQK5hwuTJh255QujFj147+htI8Bh0JlAt8=